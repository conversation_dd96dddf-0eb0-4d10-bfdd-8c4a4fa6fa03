/*
 * Simple STF and Histogram Transform Test Script - RECREATED
 * Only processes the specified image with the working "perfect" stretch
 */

(function(){

// File path
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-24T06-11-29/5_stacked/RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test/";

Console.show();
Console.writeln("=== STF and Histogram Transform Test - RECREATED ===");

try {
    // Open the file
    Console.writeln("Opening: " + inputFile);
    var windows = ImageWindow.open(inputFile);
    if (windows.length === 0) {
        throw new Error("Failed to open file: " + inputFile);
    }
    
    var win = windows[0];
    Console.writeln("File opened successfully: " + win.mainView.id);
    
    // Method 1: ScreenTransferFunction with the working parameters
    Console.writeln("\n=== Method 1: ScreenTransferFunction (Working Method) ===");
    var win1 = new ImageWindow(win.mainView.image.width, win.mainView.image.height, 
                               win.mainView.image.numberOfChannels, 32, true, 
                               win.mainView.image.isColor, "STF_Test");
    win1.mainView.beginProcess();
    win1.mainView.image.assign(win.mainView.image);
    win1.mainView.endProcess();
    win1.show();
    
    var P = new ScreenTransferFunction;
    P.STF = [ // c0, c1, m, r0, r1
       [0.11743, 1.00000, 0.00698, 0.00000, 1.00000],
       [0.11743, 1.00000, 0.00698, 0.00000, 1.00000],
       [0.11743, 1.00000, 0.00698, 0.00000, 1.00000],
       [0.00000, 1.00000, 0.50000, 0.00000, 1.00000]
    ];
    P.interaction = ScreenTransferFunction.prototype.SeparateChannels;
    P.executeOn(win1.mainView);
    
    // Save STF result
    var stfOutput = outputDir + "STF_Result_Perfect.xisf";
    win1.saveAs(stfOutput, false, false, false, false);
    Console.writeln("STF result saved: " + stfOutput);
    
    // Save as FITS for inspection
    var stfFits = outputDir + "STF_Result_Perfect.fit";
    win1.saveAs(stfFits, false, false, false, true);
    Console.writeln("STF result saved as FITS: " + stfFits);
    
    // Method 2: Convert STF to Histogram Transformation (Proper Conversion)
    Console.writeln("\n=== Method 2: Histogram Transformation (Converted from STF) ===");
    var win2 = new ImageWindow(win.mainView.image.width, win.mainView.image.height, 
                               win.mainView.image.numberOfChannels, 32, true, 
                               win.mainView.image.isColor, "HT_Test");
    win2.mainView.beginProcess();
    win2.mainView.image.assign(win.mainView.image);
    win2.mainView.endProcess();
    win2.show();
    
    // Convert STF parameters to Histogram Transformation (CORRECT FORMAT)
    var HT = new HistogramTransformation;
    // STF format: [c0, c1, m, r0, r1]
    // HT format:  [c0, m, c1, r0, r1] - NOTE: m and c1 are swapped!
    HT.H = [
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Red: c0, m, c1, r0, r1
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Green: c0, m, c1, r0, r1  
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Blue: c0, m, c1, r0, r1
        [0.00000, 0.50000, 1.00000, 0.00000, 1.00000]  // Luminance: c0, m, c1, r0, r1
    ];
    HT.executeOn(win2.mainView);
    
    // Save HT result
    var htOutput = outputDir + "HT_Result_Perfect.xisf";
    win2.saveAs(htOutput, false, false, false, false);
    Console.writeln("HT result saved: " + htOutput);
    
    // Save as FITS for inspection
    var htFits = outputDir + "HT_Result_Perfect.fit";
    win2.saveAs(htFits, false, false, false, true);
    Console.writeln("HT result saved as FITS: " + htFits);
    
    Console.writeln("\n=== Test Complete ===");
    Console.writeln("Compare the results:");
    Console.writeln("- STF Method: " + stfOutput);
    Console.writeln("- HT Method: " + htOutput);
    Console.writeln("Both should look identical and 'perfect'!");
    
} catch (error) {
    Console.criticalln("Error: " + error.message);
}

})();
