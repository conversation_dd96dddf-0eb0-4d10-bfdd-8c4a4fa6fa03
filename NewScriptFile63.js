#feature-id Scripts > ImageProcessing > AutoIntegrateMimic

#feature-info Simplified version of WBPP: integrates RGB filters and applies final processing.
#include <pjsr/Sizer.jsh>
#include <pjsr/FrameStyle.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/UndoFlag.jsh>

function main() {
  console.show();
  console.writeln("=== Starting AutoIntegrateMimic ===");

  var inputFiles = [
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-044558-Red-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-045203-Green-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-045809-Blue-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-052229-Red-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-054048-Green-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-060452-Blue-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-052229-Blue-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-054048-Red-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-045809-Green-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-060452-Red-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-044558-Green-BIN1-W-300-001.fit",
    "C:/Users/<USER>/OneDrive/Desktop/Input/Lights/calibrated-T14-blake-M31-20241103-045203-Blue-BIN1-W-300-001.fit"
  ];

  var outputDir = File.systemTempDirectory;
  if (!outputDir.endsWith('/') && !outputDir.endsWith('\\'))
    outputDir += '/';

  console.writeln("Output directory: ", outputDir);

  var filterGroups = {};
  for (var i = 0; i < inputFiles.length; ++i) {
    var f = inputFiles[i];
    var filterMatch = f.match(/-(Red|Green|Blue)-/i);
    var filter = filterMatch ? filterMatch[1].toUpperCase() : 'UNKNOWN';
    if (!filterGroups[filter]) filterGroups[filter] = [];
    filterGroups[filter].push(f);
  }

  var integratedViews = {};

  for (var filter in filterGroups) {
    var group = filterGroups[filter];
    console.writeln("Processing filter: ", filter, "with", group.length, "files.");

    var alignedFiles = [];

    if (group.length === 1) {
      console.warning("Only one file for " + filter + ", opening directly.");
      var w = ImageWindow.open(group[0])[0];
      integratedViews[filter] = w.mainView;
      continue;
    }

    var reference = group[0];
    alignedFiles.push(reference);
    console.writeln("Using reference for", filter, ":", reference);

    for (var i = 1; i < group.length; ++i) {
      var target = group[i];
      var name = File.extractName(target);
      var alignedFile = outputDir + name + '_r.xisf';

      console.writeln("Aligning:", target, "->", alignedFile);

      var sa = new StarAlignment();
      sa.referenceImage = reference;
      sa.referenceIsFile = true;
      sa.targetImages = [[target, alignedFile]];

      var success = sa.executeGlobal();
      if (!success) {
        console.warning("StarAlignment FAILED on", target);
        alignedFiles.push(target);
      } else {
        alignedFiles.push(alignedFile);
      }
    }

    console.writeln("Aligned images to integrate:", alignedFiles.length);

    var ii = new ImageIntegration();
    ii.combination = 1; // Average
    ii.weighting = 1;   // NoiseWeightedAverage
    ii.normalization = 2; // AdditiveWithScaling

    if (alignedFiles.length >= 3) {
      ii.rejection = 5;     // WinsorizedSigmaClipping
      ii.rejectionNormalization = 1;
    } else {
      ii.rejection = 0;     // NoRejection
    }

    ii.images = [];
    for (var f = 0; f < alignedFiles.length; ++f) {
      ii.images.push([true, alignedFiles[f], '', '']);
    }

    var success = ii.executeGlobal();
    if (!success) {
      console.criticalln("Integration failed for filter:", filter);
      return;
    }

    var integratedWindow = ImageWindow.windowById("integration");
    if (integratedWindow.isNull) {
      console.criticalln("Integration window not found for filter:", filter);
      return;
    }

    integratedViews[filter] = integratedWindow.mainView;
    integratedWindow.show();
  }

  // RGB COMBINATION
  var redView = integratedViews.RED;
  var greenView = integratedViews.GREEN;
  var blueView = integratedViews.BLUE;

  if (!redView || !greenView || !blueView) {
    console.criticalln("Not all RGB filters integrated — cannot proceed.");
    return;
  }

  console.writeln("Merging R/G/B...");
  var cc = new ChannelCombination();
  cc.colorSpace = 0; // RGB
  cc.channels = [
    [true, redView.id],
    [true, greenView.id],
    [true, blueView.id],
    [false, ""]
  ];

  var success = cc.executeGlobal();
  if (!success) {
    console.criticalln("RGB merge FAILED.");
    return;
  }

  var rgbWindow = ImageWindow.activeWindow;
  var finalView = rgbWindow.mainView;

  console.writeln("Applying final processing chain...");

  // ABE
  var abe = new AutomaticBackgroundExtractor();
  abe.sampleSize = 10;
  abe.executeOn(finalView);

  // Color Calibrate
  var cc2 = new ColorCalibration();
  cc2.executeOn(finalView);

  // STF Stretch
  var stf = new ScreenTransferFunction();
  stf.autoStretch(finalView);

  console.writeln("✅ Final image created successfully!");
}

main();
