/*
 * Post-Integration Pipeline with Astrometry.net Integration
 * Clean, working version without complex platform detection
 */

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

(function(){

// ============================================================================
// SIMPLE PLATFORM DETECTION
// ============================================================================

var isWindows = (CoreApplication.platform == "MSWindows");
if (!isWindows && File.directoryExists("C:/Program Files")) {
    isWindows = true;
}

var defaults = {
    inputDir: isWindows ? "C:/AstroImages/Processed" : File.homeDirectory + "/AstroImages/Processed",
    outputDir: isWindows ? "C:/AstroImages/Output" : File.homeDirectory + "/AstroImages/Output"
};

// ============================================================================
// ASTROMETRY.NET INTEGRATION
// ============================================================================

function getAstrometryAPIKey() {
    var settingsKey = "PostIntegrationPipeline/AstrometryAPIKey";
    try {
        var savedKey = Settings.read(settingsKey, DataType_String);
        if (savedKey && savedKey.length > 0) {
            return savedKey;
        }
    } catch (e) {
        // No saved key
    }

    // Ask user for API key
    var dialog = new APIKeyDialog();
    if (dialog.execute()) {
        var apiKey = dialog.apiKey;
        if (apiKey && apiKey.length > 0) {
            // Save the API key
            Settings.write(settingsKey, DataType_String, apiKey);
            return apiKey;
        }
    }

    return null;
}

function APIKeyDialog() {
    this.__base__ = Dialog;
    this.__base__();

    this.windowTitle = "Astrometry.net API Key Required";
    this.apiKey = "";

    this.sizer = new VerticalSizer;
    this.sizer.margin = 15;
    this.sizer.spacing = 10;

    // Info text
    var infoLabel = new Label(this);
    infoLabel.useRichText = true;
    var infoText = "<b>Astrometry.net API Key Required</b><br/><br/>";
    infoText += "No astrometric solution found in the image. To perform blind plate solving,<br/>";
    infoText += "please enter your astrometry.net API key.<br/><br/>";
    infoText += "If you don't have one, visit: nova.astrometry.net<br/>";
    infoText += "and create a free account to get your API key.";
    infoLabel.text = infoText;
    infoLabel.wordWrapping = true;
    infoLabel.minWidth = 400;
    this.sizer.add(infoLabel);

    // API Key input
    var keyLabel = new Label(this);
    keyLabel.text = "API Key:";
    this.sizer.add(keyLabel);

    this.keyEdit = new Edit(this);
    this.keyEdit.text = "kkjdojeiwunqowgv"; // Pre-fill with user's key
    this.keyEdit.minWidth = 400;
    this.keyEdit.onTextUpdated = function(text) {
        this.dialog.apiKey = text;
    };
    this.sizer.add(this.keyEdit);

    // Buttons
    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var okButton = new PushButton(this);
    okButton.text = "OK";
    okButton.defaultButton = true;
    okButton.onClick = function() {
        this.dialog.apiKey = this.dialog.keyEdit.text;
        if (this.dialog.apiKey.trim().length === 0) {
            (new MessageBox("Please enter your API key.", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        this.dialog.ok();
    };

    var cancelButton = new PushButton(this);
    cancelButton.text = "Skip Solving";
    cancelButton.onClick = function() {
        this.dialog.cancel();
    };

    buttonRow.add(okButton);
    buttonRow.add(cancelButton);
    this.sizer.add(buttonRow);

    this.adjustToContents();
}

APIKeyDialog.prototype = new Dialog;

function hasAstrometricSolution(win) {
    try {
        var view = win.mainView;
        var keywords = view.window.keywords;
        var hasRA = false, hasDEC = false, hasCD = false, hasCTYPE = false;

        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            var name = keyword.name.toUpperCase();

            if (name === "RA" || name === "OBJCTRA" || name === "CRVAL1") hasRA = true;
            if (name === "DEC" || name === "OBJCTDEC" || name === "CRVAL2") hasDEC = true;
            if (name.indexOf("CD1_") === 0 || name.indexOf("CD2_") === 0) hasCD = true;
            if (name === "CTYPE1" || name === "CTYPE2") hasCTYPE = true;
        }

        var hasBasicCoords = hasRA && hasDEC;
        var hasWCSMatrix = hasCD && hasCTYPE;

        if (hasBasicCoords && hasWCSMatrix) {
            Console.writeln("✅ Valid astrometric solution found");
            return true;
        } else {
            Console.writeln("❌ No valid astrometric solution found");
            Console.writeln("   Has coordinates: " + hasBasicCoords);
            Console.writeln("   Has WCS matrix: " + hasWCSMatrix);
            return false;
        }

    } catch (e) {
        Console.writeln("❌ Error checking astrometric solution: " + e.message);
        return false;
    }
}

function offerBlindSolving(win) {
    Console.writeln("No astrometric solution found. Offering blind solving...");

    var solveChoice = (new MessageBox(
        "No Astrometric Solution Found\n\n" +
        "The image needs an astrometric solution for SPCC to work properly.\n\n" +
        "Would you like to get instructions for blind plate solving using astrometry.net?\n\n" +
        "YES = Get solving instructions\n" +
        "NO = Skip SPCC (continue without color calibration)",
        "Blind Solving Required",
        StdIcon_Question,
        StdButton_Yes, StdButton_No
    )).execute();

    if (solveChoice == StdButton_No) {
        Console.writeln("⏭️ User chose to skip solving");
        return false;
    }

    // User wants solving instructions
    var apiKey = getAstrometryAPIKey();
    if (!apiKey) {
        Console.writeln("❌ No API key provided");
        return false;
    }

    // Provide detailed solving instructions
    Console.writeln("\n=== Astrometry.net Blind Solving Instructions ===");
    Console.writeln("");
    Console.writeln("📋 Please follow these steps to solve your image:");
    Console.writeln("");
    Console.writeln("1. 💾 Save your current image as a FITS file:");
    Console.writeln("   File → Save As → Choose FITS format");
    Console.writeln("   Suggested name: temp_solve_" + Date.now() + ".fits");
    Console.writeln("");
    Console.writeln("2. 🌐 Go to: https://nova.astrometry.net/upload");
    Console.writeln("");
    Console.writeln("3. 📤 Upload your FITS file with these settings:");
    Console.writeln("   • Your API Key: " + apiKey);
    Console.writeln("   • Scale units: arcseconds per pixel");
    Console.writeln("   • Scale estimate: Leave blank for blind solving");
    Console.writeln("   • Check 'Publicly visible' if you don't mind");
    Console.writeln("");
    Console.writeln("4. ⏳ Wait for solving to complete (usually 1-5 minutes)");
    Console.writeln("");
    Console.writeln("5. 📥 Download the 'new-image.fits' file from the results");
    Console.writeln("");
    Console.writeln("6. 📂 Open the solved image in PixInsight:");
    Console.writeln("   File → Open → Select the downloaded 'new-image.fits'");
    Console.writeln("");
    Console.writeln("7. 🔄 Run SPCC on the solved image");
    Console.writeln("");
    Console.writeln("================================================");

    var continueChoice = (new MessageBox(
        "Solving Instructions Provided\n\n" +
        "Please follow the instructions in the Console to solve the image.\n\n" +
        "After you have the solved image open, would you like to run SPCC now?\n\n" +
        "YES = Run SPCC on current image (make sure it's the solved one!)\n" +
        "NO = Skip SPCC for now",
        "Ready to Run SPCC?",
        StdIcon_Question,
        StdButton_Yes, StdButton_No
    )).execute();

    return (continueChoice == StdButton_Yes);
}

function performSPCC(win, isRGB) {
    Console.writeln("\n=== SPCC (Spectrophotometric Color Calibration) ===");

    // Check for astrometric solution first
    if (!hasAstrometricSolution(win)) {
        Console.writeln("❌ No astrometric solution found");

        var shouldProceed = offerBlindSolving(win);
        if (!shouldProceed) {
            Console.writeln("⏭️ SPCC skipped");
            return false;
        }

        // Check again after user has had chance to solve
        if (!hasAstrometricSolution(win)) {
            Console.writeln("❌ Still no astrometric solution - SPCC cannot proceed");
            return false;
        }
    }

    try {
        var P = new SpectrophotometricColorCalibration();

        // Configure SPCC parameters
        try { P.whiteReferenceId = "Average Spiral Galaxy"; } catch(_) {
            try { P.whiteReferenceId = "AVG_G2V"; } catch(_) { }
        }

        if (isRGB) {
            Console.writeln("Using RGB filter profile");
            try {
                P.redFilter = "Astronomik Typ 2c R";
                P.greenFilter = "Astronomik Typ 2c G";
                P.blueFilter = "Astronomik Typ 2c B";
            } catch(_) {}
        } else {
            Console.writeln("Using OSC filter profile");
            try {
                P.redFilter = "Sony Color Sensor R";
                P.greenFilter = "Sony Color Sensor G";
                P.blueFilter = "Sony Color Sensor B";
            } catch(_) {}
        }

        try { P.applyCalibration = true; } catch(_) {}
        try { P.generateGraphs = false; } catch(_) {}
        try { P.catalog = "Gaia DR3/SP"; } catch(_) {}

        Console.writeln("Executing SPCC...");
        P.executeOn(win.mainView);

        // Verify success by checking if astrometric solution still exists
        if (hasAstrometricSolution(win)) {
            Console.writeln("✅ SPCC completed successfully");
            return true;
        } else {
            Console.writeln("❌ SPCC failed - astrometric solution was lost during processing");
            return false;
        }

    } catch (e) {
        Console.writeln("❌ SPCC failed with error: " + e.message);

        if (e.message.indexOf("no valid astrometric solution") >= 0) {
            Console.writeln("❌ This confirms the image needs plate solving first");
        }
        return false;
    }
}

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

function processCurrentImage() {
    var currentWindow = ImageWindow.activeWindow;
    if (!currentWindow) {
        (new MessageBox(
            "No Active Image Window\n\n" +
            "Please open an image first, then run this script.",
            "No Image Found",
            StdIcon_Error,
            StdButton_Ok
        )).execute();
        return;
    }

    Console.writeln("Processing image: " + currentWindow.mainView.id);

    var isColor = currentWindow.mainView.image.isColor;
    Console.writeln("Image type: " + (isColor ? "Color" : "Monochrome"));

    if (!isColor) {
        (new MessageBox(
            "Monochrome Image Detected\n\n" +
            "SPCC is designed for color images. This appears to be a monochrome image.\n\n" +
            "SPCC will be skipped.",
            "Monochrome Image",
            StdIcon_Information,
            StdButton_Ok
        )).execute();
        Console.writeln("⏭️ SPCC skipped - monochrome image");
        return;
    }

    // Detect if this is an RGB combination or OSC image
    var isRGB = false;
    try {
        var keywords = currentWindow.mainView.window.keywords;
        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            if (keyword.name.toLowerCase().indexOf("filter") >= 0 &&
                (keyword.value.toLowerCase().indexOf("red") >= 0 ||
                 keyword.value.toLowerCase().indexOf("green") >= 0 ||
                 keyword.value.toLowerCase().indexOf("blue") >= 0)) {
                isRGB = true;
                break;
            }
        }
    } catch (e) {
        // Default to OSC
    }

    Console.writeln("Filter type: " + (isRGB ? "RGB (separate filters)" : "OSC (color camera)"));

    // Run SPCC
    var success = performSPCC(currentWindow, isRGB);

    if (success) {
        Console.writeln("\n🎉 SPCC processing completed successfully!");
    } else {
        Console.writeln("\n⚠️ SPCC was skipped or failed. See messages above for details.");
    }
}

function showMainGUI() {
    var dlg = new Dialog;
    dlg.windowTitle = "Post-Integration Pipeline with Astrometry.net";
    dlg.sizer = new VerticalSizer;
    dlg.sizer.margin = 15;
    dlg.sizer.spacing = 10;

    // Title
    var title = new Label(dlg);
    title.useRichText = true;
    title.text = "<b>Post-Integration Pipeline with Astrometry.net Integration</b>";
    title.textAlignment = TextAlign_Center;
    dlg.sizer.add(title);

    // Description
    var desc = new Label(dlg);
    desc.useRichText = true;
    desc.text = "This script provides SPCC with automatic astrometric solution detection and<br/>" +
                "blind solving instructions using astrometry.net when needed.<br/><br/>" +
                "<b>Current features:</b><br/>" +
                "• Automatic astrometric solution detection<br/>" +
                "• Blind solving instructions for astrometry.net<br/>" +
                "• SPCC with proper error handling<br/>" +
                "• Support for both RGB and OSC images";
    desc.wordWrapping = true;
    desc.minWidth = 500;
    dlg.sizer.add(desc);

    // Current image info
    var currentWindow = ImageWindow.activeWindow;
    var imageInfo = new Label(dlg);
    if (currentWindow) {
        var isColor = currentWindow.mainView.image.isColor;
        var hasWCS = hasAstrometricSolution(currentWindow);
        imageInfo.text = "Current image: " + currentWindow.mainView.id + "\n" +
                        "Type: " + (isColor ? "Color" : "Monochrome") + "\n" +
                        "Astrometric solution: " + (hasWCS ? "✅ Found" : "❌ Missing");
    } else {
        imageInfo.text = "No active image window found.\nPlease open an image first.";
    }
    imageInfo.frameStyle = FrameStyle_Box;
    imageInfo.margin = 8;
    dlg.sizer.add(imageInfo);

    // Buttons
    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var processButton = new PushButton(dlg);
    processButton.text = "Process Current Image";
    processButton.defaultButton = true;
    processButton.enabled = (currentWindow != null);
    processButton.onClick = function() {
        dlg.ok();
    };

    var cancelButton = new PushButton(dlg);
    cancelButton.text = "Cancel";
    cancelButton.onClick = function() {
        dlg.cancel();
    };

    buttonRow.add(processButton);
    buttonRow.add(cancelButton);
    dlg.sizer.add(buttonRow);

    return dlg.execute();
}

// ============================================================================
// MAIN ENTRY POINT
// ============================================================================

function main() {
    Console.show();
    Console.writeln("=== Post-Integration Pipeline with Astrometry.net Integration ===");
    Console.writeln("Platform: " + (isWindows ? "Windows" : "Unix-like"));

    if (showMainGUI()) {
        processCurrentImage();
    } else {
        Console.writeln("Cancelled by user");
    }
}

// Run the script
main();

})();
