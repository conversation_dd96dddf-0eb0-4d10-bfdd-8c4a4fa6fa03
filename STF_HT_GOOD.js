/*
 * STF Auto Stretch and App<PERSON> <PERSON>ript - Final Corrected Version
 *
 * This script correctly replicates the STF Auto-Stretch ("nuclear button")
 * and applies the stretch permanently using HistogramTransformation.
 *
 * Fixes:
 * 1. Ensures correct parameter order mapping between STF [c0, c1, m] and HT [c0, m, c1].
 * 2. Ensures statistics vectors are cloned to prevent in-place modification corruption.
 * 3. Corrects usage of the global Settings API.
 */

#feature-id    Utilities > AutoStretchAndApplyPermanent_Final

// Configuration for testing (Set TEST_MODE to false to run on the active window)
var TEST_MODE = true;
// Update these paths according to your local environment
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-24T06-11-29/5_stacked/RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test/";


/*
 * Function to retrieve STF AutoStretch preferences from the PixInsight environment.
 */
function getSTFPreferences() {
    const KEY_SCLIP = "STFAutoStretch/ShadowsClipping";
    const KEY_TBGND = "STFAutoStretch/TargetBackground";
    const KEY_CLINK = "STFAutoStretch/RgbLinked";

    // Fallback defaults
    var prefs = {
        shadowsClipping: -2.80,
        targetBackground: 0.25, // A common default matching the user's example
        rgbLinked: true
    };

    // Attempt to read the settings from the environment
    try {
        // FIX: Access Settings directly as a global object.
        if (typeof Settings !== 'undefined') {
            if (Settings.has(KEY_SCLIP)) {
                 var val = Settings.read(KEY_SCLIP, DataType_Float);
                 if (val != null) prefs.shadowsClipping = val;
            }
            if (Settings.has(KEY_TBGND)) {
                var val = Settings.read(KEY_TBGND, DataType_Float);
                if (val != null) prefs.targetBackground = val;
            }
            if (Settings.has(KEY_CLINK)) {
                var val = Settings.read(KEY_CLINK, DataType_Boolean);
                if (val != null) prefs.rgbLinked = val;
            }
        } else {
            console.writeln("Warning: Global Settings object not available. Using fallback defaults.");
        }
    } catch (e) {
        console.writeln("Warning: Error accessing Settings API. Using fallback defaults. Error: " + e.message);
    }

    console.writeln("Using STF AutoStretch Preferences:");
    console.writeln("  Shadows Clipping (sigma): " + prefs.shadowsClipping.toFixed(2));
    console.writeln("  Target Background:        " + prefs.targetBackground.toFixed(4));
    console.writeln("  RGB Linked:               " + prefs.rgbLinked);

    return prefs;
}


/*
 * STF Auto Stretch routine
 */
function STFAutoStretch( view, prefs )
{
   var shadowsClipping = prefs.shadowsClipping;
   var targetBackground = prefs.targetBackground;
   var rgbLinked = prefs.rgbLinked;

   var stf = new ScreenTransferFunction;
   var n = view.image.isColor ? 3 : 1;

   // 1. Calculate Statistics
   // FIX: Create copies (clones) using 'new Vector()' to prevent corruption of cached image stats by in-place operations.
   var median = new Vector( view.computeOrFetchProperty( "Median" ) );
   var mad = new Vector( view.computeOrFetchProperty( "MAD" ) );

   console.writeln("Original statistics computed (Linear Data):");
    for (var c = 0; c < n; ++c) {
      console.writeln("Channel " + c + ": median=" + median.at(c).toFixed(6) + ", mad=" + mad.at(c).toFixed(6));
   }

   // 2. Normalize MAD (1.4826 * MAD ≈ sigma).
   // This safely modifies the cloned vector.
   mad.mul( 1.4826 );

   // 3. Calculate Stretch Parameters
   // STF Parameter Order: [c0, c1, m, r0, r1]
   if ( rgbLinked && n > 1)
   {
      // Linked RGB channels
      console.writeln("Calculating Linked RGB Stretch...");

      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
         if ( median.at( c ) > 0.5 )
            ++invertedChannels;

      if ( invertedChannels < n )
      {
         // Noninverted image
         var c0_sum = 0, median_sum = 0;

         for ( var c = 0; c < n; ++c )
         {
            // Robust check for zero MAD
            if ( 1 + mad.at( c ) != 1 )
               // Formula: Median + (ClippingFactor * Sigma)
               c0_sum += median.at( c ) + shadowsClipping * mad.at( c );
            median_sum += median.at( c );
         }

         // Average and clamp
         var c0 = Math.range( c0_sum/n, 0.0, 1.0 );
         // Midtones Transfer Function (MTF)
         var m = Math.mtf( targetBackground, median_sum/n - c0 );

         // STF: [c0, c1, m, r0, r1]
         stf.STF = [
                     [c0, 1, m, 0, 1], // R
                     [c0, 1, m, 0, 1], // G
                     [c0, 1, m, 0, 1], // B
                     [0, 1, 0.5, 0, 1] // Alpha
                   ];
         console.writeln("Linked Stretch (STF Format): c0=" + c0.toFixed(6) + ", c1=1.00, m=" + m.toFixed(6));
      }
      else
      {
         // Inverted image logic
         console.writeln("Image appears inverted. Calculating highlight clipping.");
         var c1_sum = 0, median_sum = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c1_sum += median.at( c ) - shadowsClipping * mad.at( c );
            median_sum += median.at( c );
         }
         var c1 = Math.range( c1_sum/n, 0.0, 1.0 );
         var m = Math.mtf( c1 - median_sum/n, targetBackground );

         stf.STF = [
                     [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, 1, 0.5, 0, 1]
                   ];
         console.writeln("Inverted Linked Stretch (STF Format): c0=0.00, c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
      }
   }
   else
   {
      // Unlinked channels (or Grayscale image)
      console.writeln("Calculating Unlinked/Grayscale Stretch...");
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];

      for ( var c = 0; c < n; ++c )
      {
         if ( median.at( c ) < 0.5 )
         {
            // Noninverted channel
            var c0 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
            var m = Math.mtf( targetBackground, median.at( c ) - c0 );
            A[c] = [c0, 1, m, 0, 1];
            console.writeln("Channel " + c + ": c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
         }
         else
         {
            // Inverted channel
            var c1 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
            var m = Math.mtf( c1 - median.at( c ), targetBackground );
            A[c] = [0, c1, m, 0, 1];
             console.writeln("Channel " + c + " (Inverted): c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
         }
      }
      stf.STF = A;
   }

   return stf;
}


/*
 * Apply HistogramTransformation (CORRECTED MAPPING)
 */
function ApplyHistogramTransformation( view, stf )
{
   console.writeln("\nApplying HistogramTransformation...");
   var ht = new HistogramTransformation;

   // HT Parameter Order: [c0, m, c1, r0, r1] <-- CRITICAL DIFFERENCE FROM STF

   // Initialize HT parameters array (H).
   // 5 channels: R/K (0), G (1), B (2), RGB/K Combined (3), Alpha (4).
   // HT Identity Transform: c0=0, m=0.5, c1=1
   var HT_IDENTITY = [0, 0.5, 1, 0, 1];

   // Initialize H using copies of the identity transform
   var H = [
            HT_IDENTITY.slice(), HT_IDENTITY.slice(), HT_IDENTITY.slice(),
            HT_IDENTITY.slice(), HT_IDENTITY.slice()
           ];

   var n = view.image.isColor ? 3 : 1;

   // Helper function to map STF array to HT array
   // STF input indices: [0=c0, 1=c1, 2=m, 3=r0, 4=r1]
   // HT output indices: [0=c0, 1=m, 2=c1, 3=r0, 4=r1]
   function mapSTFtoHT(stf_channel) {
       return [
           stf_channel[0], // c0
           stf_channel[2], // m (FIX: Swapped index 1 and 2)
           stf_channel[1], // c1 (FIX: Swapped index 1 and 2)
           stf_channel[3], // r0
           stf_channel[4]  // r1
       ];
   }

   if ( view.image.isColor )
   {
       // Check if the STF channels are linked (parameters are identical)
       var linked = true;
       for(var c = 1; c < n; ++c) {
           // Compare c0, c1, and m
           if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
               linked = false;
               break;
           }
       }

       if(linked) {
           // Apply linked transform to the combined RGB/K channel (Index 3).
           // This mirrors the manual STF drag-and-drop behavior.
           H[3] = mapSTFtoHT(stf.STF[0]);
           console.writeln("Applying Linked HT to RGB/K channel.");
           console.writeln("HT Parameters (RGB/K): c0=" + H[3][0].toFixed(8) + ", m=" + H[3][1].toFixed(8) + ", c1=" + H[3][2].toFixed(8));
       } else {
           // Apply unlinked transforms to individual channels (Indices 0, 1, 2).
           console.writeln("Applying Unlinked HT to individual R, G, B channels.");
           for ( var c = 0; c < n; ++c )
           {
              H[c] = mapSTFtoHT(stf.STF[c]);
              console.writeln("HT Parameters (Ch " + c + "): c0=" + H[c][0].toFixed(8) + ", m=" + H[c][1].toFixed(8) + ", c1=" + H[c][2].toFixed(8));
           }
       }
   }
   else
   {
       // Monochrome image (Apply to Index 0)
       console.writeln("Applying HT to K channel.");
       H[0] = mapSTFtoHT(stf.STF[0]);
       console.writeln("HT Parameters (K): c0=" + H[0][0].toFixed(8) + ", m=" + H[0][1].toFixed(8) + ", c1=" + H[0][2].toFixed(8));
   }

   ht.H = H;

   // Execute the HistogramTransformation
   if (!ht.executeOn( view )) {
       console.criticalln("HistogramTransformation execution failed.");
       return false;
   }
   return true;
}

/*
 * Robustly resets the STF visualization on a window.
 */
function ResetSTF(window) {
    console.writeln("Resetting STF visualization...");
    try {
        // Preferred method
        if (window.screenTransferFunctionsEnabled) {
           window.disableScreenTransferFunctions();
        }
    } catch(e) {
        // Fallback method if the above fails (e.g., in some automated contexts)
        var stfReset = new ScreenTransferFunction;
        // Apply identity STF: [c0=0, c1=1, m=0.5, r0=0, r1=1]
        stfReset.executeOn(window.currentView, false);
        console.writeln("STF reset using fallback method.");
    }
}

// Main execution block
function main()
{
   console.show();
    console.writeln("=== STF Auto Stretch Script (Final Corrected Version) ===");

   // 1. Retrieve user preferences for STF AutoStretch
   var stfPrefs = getSTFPreferences();

   var window;

   // --- Setup Environment (Handles Test Mode or Active Window) ---
   if (TEST_MODE) {
        console.writeln("\nRunning in TEST MODE.");
         try {
            if (!File.directoryExists(outputDir)) {
                File.createDirectory(outputDir, true);
                console.writeln("Created output directory: " + outputDir);
            }
        } catch(e) {
            // Directory likely exists
        }

        console.writeln("Opening: " + inputFile);
        var windows = ImageWindow.open(inputFile);
        if (windows.length === 0) {
            console.criticalln("Failed to open file: " + inputFile);
            return;
        }
        window = windows[0];
        window.show(); // Ensure window is visible

   } else {
       // Standard operation
       window = ImageWindow.activeWindow;
   }


   if ( window == null || window.isNull )
   {
      console.criticalln( "No active image window." );
      return;
   }
   // -------------------------------------------------------------

   var view = window.currentView;
   console.writeln( "\nProcessing view: " + view.fullId );

   // 2. Ensure clean state: Disable any existing STF visualization before analysis.
   ResetSTF(window);

   // 3. Calculate STF Auto Stretch parameters.
   var stf = STFAutoStretch( view, stfPrefs );

   // 4. Apply the calculated parameters using HistogramTransformation
   if (ApplyHistogramTransformation( view, stf )) {

       // 5. Reset the STF visualization again to prevent a "double stretch" display.
       ResetSTF(window);

       // 6. Save results (if in test mode)
       if (TEST_MODE) {
           var outputPath = outputDir + "STF_Nuclear_Result_SUCCESS.xisf";
           console.writeln("\nSaving result...");
            // Arguments: path, warnings, validate, strict, security
            if (window.saveAs(outputPath, false, false, false, false)) {
                 console.writeln("✅ Result saved: " + outputPath);
            } else {
                console.criticalln("Failed to save XISF file.");
            }
       }

       console.writeln( "\n✅ STF Auto Stretch applied permanently via HistogramTransformation." );
   } else {
       console.criticalln( "\n❌ Script failed during HistogramTransformation." );
   }
}

main();
