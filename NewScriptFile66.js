// =================================================================
// PixInsight JavaScript Script to Combine R, G, B XISF Images
// =================================================================

#include <pjsr/DataType.jsh>

function getFilterName(filePath) {
    var file = new File;
    file.open(filePath, FileMode_ReadOnly);
    var fitsHeader = file.readFITSHeader();
    file.close();

    // Use regular expression to extract FILTER keyword
    var match = fitsHeader.match(/FILTER\s*=\s*'([^']+)'/i);
    if (match && match.length > 1)
        return match[1].trim();
    else
        return "";
}

function loadImageByFilter(folder, filterName) {
    var files = File.findFiles(folder, "*.xisf");
    for (var i = 0; i < files.length; ++i) {
        var filePath = files[i];
        var filter = getFilterName(filePath);
        if (filter && filter.toLowerCase().indexOf(filterName.toLowerCase()) !== -1) {
            return ImageWindow.open(filePath, "");
        }
    }
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    // Load the R, G, B images based on FITS header 'FILTER' keyword
    var redWindow = loadImageByFilter(inputDir, "R");
    var greenWindow = loadImageByFilter(inputDir, "G");
    var blueWindow = loadImageByFilter(inputDir, "B");

    if (!redWindow || !greenWindow || !blueWindow) {
        console.writeln("Error: Could not load one or more RGB images.");
        return;
    }

    // Access their main views
    var redView = redWindow[0].mainView;
    var greenView = greenWindow[0].mainView;
    var blueView = blueWindow[0].mainView;

    // Create ChannelCombination instance
    var channelCombination = new ChannelCombination;

    // Assign channels: R, G, B
    channelCombination.channels = [
        [redView],    // Red channel
        [greenView],  // Green channel
        [blueView]    // Blue channel
    ];

    // Generate output image
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,  // 3 channels = RGB
        BitDepth_IEEE32,  // Use 32-bit float for high dynamic range
        true,  // isColor
        true,  // canDelete
        "Combined_RGB"  // window ID
    );

    // Run the ChannelCombination process
    channelCombination.executeOn(outputImage.mainView, false);  // true = generate new image

    // Show result
    outputImage.show();

    console.writeln("RGB combination complete.");
}

main();
