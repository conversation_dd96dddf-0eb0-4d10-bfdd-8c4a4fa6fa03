/* AutoIntegrateSimple – ONE picker, robust RGB-separated PixInsight PJSR
   - Select ALL frames once (LIGHT + optional BIAS/DARK/FLAT)
   - Detect IMAGETYP → build masters only if present (AI_Simple/masters/*.xisf)
   - Calibrate lights (skip absent masters; never crash on missing files)
   - StarAlignment per filter group (R/G/B)
   - ImageIntegration per filter → AI_Simple/int/Integration_R/G/B.xisf
*/

// ---------- Utils ----------
function log(s){ console.writeln(s); } function warn(s){ console.warningln(s); } function err(s){ console.criticalln(s); }
function ensureDir(p){ try{ if(!File.directoryExists(p)) File.createDirectory(p); }catch(e){ throw new Error("mkdir "+p+" :: "+e); } }
function pathJoin(d,n){ return (d.endsWith("/")||d.endsWith("\\"))? d+n : d+"/"+n; }
function dirName(p){ return (typeof File.extractDirectory==="function")? File.extractDirectory(p) : p.replace(/[/\\][^/\\]*$/,""); }
function baseName(p){ return (typeof File.extractName==="function")? File.extractName(p) : p.replace(/^.*[/\\]/,"").replace(/\.[^.]*$/,""); }

function selectAllOnce(caption){
  var ofd=new OpenFileDialog; ofd.caption=caption||"Select ALL frames (LIGHT + optional BIAS/DARK/FLAT)"; ofd.multipleSelections=true; ofd.loadImageFilters();
  if(!ofd.execute()) return []; return ofd.fileNames||[];
}

function saveWindowAs(win,outPath){
  ensureDir(dirName(outPath));
  var f=new File(outPath); if(f.exists) f.remove();
  var ok=win.saveAs(outPath,false,false);
  if((!ok)||(!File.exists(outPath))) ok=win.saveAs(outPath,true,false);
  if((!ok)||(!File.exists(outPath))) throw new Error("Save failed: "+outPath);
}

function readFitsKeyword(filePath, keyUpper){
  try{ var ws=ImageWindow.open(filePath); if(!ws||ws.length===0) return null;
       var w=ws[0], kw=w.keywords, val=null;
       for(var i=0;i<kw.length;i++){ var k=(kw[i].name||"").toUpperCase(); if(k===keyUpper){ val=(kw[i].strippedValue||kw[i].value||""); break; } }
       w.close(); return val;
  }catch(e){ return null; }
}

// Safe CFA detection: returns {enabled:boolean, pattern:number|null}
function detectCFAPatternFromFITS(filePath){
  try{
    var ws=ImageWindow.open(filePath); if(!ws||ws.length===0) return {enabled:false,pattern:null};
    var w=ws[0], kw=w.keywords, val=null;
    for(var i=0;i<kw.length;i++){ var k=(kw[i].name||"").toUpperCase();
      if(k==="BAYERPAT"||k==="BAYER_PATTERN"||k==="CFAPATTERN"||k==="FILTER"){ val=(kw[i].strippedValue||kw[i].value||"").toUpperCase(); break; } }
    w.close(); if(!val) return {enabled:false,pattern:null};
    var pat=null;
    if(val.indexOf("RGGB")>=0) pat=Debayer.prototype.RGGB;
    else if(val.indexOf("BGGR")>=0) pat=Debayer.prototype.BGGR;
    else if(val.indexOf("GRBG")>=0) pat=Debayer.prototype.GRBG;
    else if(val.indexOf("GBRG")>=0) pat=Debayer.prototype.GBRG;
    return pat!=null? {enabled:true,pattern:pat}:{enabled:false,pattern:null};
  }catch(e){ return {enabled:false,pattern:null}; }
}

function filesForImageIntegration(paths){ var v=[]; for(var i=0;i<paths.length;i++) v.push([true,paths[i],"",""]); return v; }

// ---------- Classification ----------
function classifyFrames(allPaths){
  var out={lights:[], bias:[], dark:[], flat:[]};
  for(var i=0;i<allPaths.length;i++){
    var p=allPaths[i], typ=(readFitsKeyword(p,"IMAGETYP")||"").toString().trim().toUpperCase();
    var n=baseName(p).toUpperCase();
    function has(s){ return typ.indexOf(s)>=0 || n.indexOf(s)>=0; }
    if(has("LIGHT")) out.lights.push(p);
    else if(has("BIAS")||has("OFFSET")) out.bias.push(p);
    else if(has("DARK")) out.dark.push(p);
    else if(has("FLAT")) out.flat.push(p);
    else {
      // If no IMAGETYP, assume LIGHT
      out.lights.push(p);
    }
  }
  return out;
}

// ---------- Filter grouping ----------
function inferFilter(filePath){
  var f=(readFitsKeyword(filePath,"FILTER")||"").toString().trim().toUpperCase();
  var n=baseName(filePath).toUpperCase();
  if(f.includes("RED")||f==="R"||n.includes("-RED-")||/[_\-\.]R([_\-\.]|$)/.test(n)) return "R";
  if(f.includes("GREEN")||f==="G"||n.includes("-GREEN-")||/[_\-\.]G([_\-\.]|$)/.test(n)) return "G";
  if(f.includes("BLUE")||f==="B"||n.includes("-BLUE-")||/[_\-\.]B([_\-\.]|$)/.test(n)) return "B";
  return "UNK";
}
function groupByFilter(paths){
  var g={R:[],G:[],B:[],UNK:[]};
  for(var i=0;i<paths.length;i++){ g[inferFilter(paths[i])].push(paths[i]); }
  return g;
}

// ---------- Masters ----------
function integrateMaster(name, files, outDir){
  if(!files||files.length<1) throw new Error("No files for "+name);
  var P=new ImageIntegration;
  P.images=filesForImageIntegration(files);
  P.combination=ImageIntegration.prototype.Average;
  P.weightMode=ImageIntegration.prototype.DontCare;
  P.normalization=ImageIntegration.prototype.NoNormalization;
  P.rejection=ImageIntegration.prototype.PercentileClip;
  P.rejectionNormalization=ImageIntegration.prototype.NoRejectionNormalization;
  P.rangeClipLow=false;
  if(typeof P.evaluateSNR!=="undefined") P.evaluateSNR=false; else P.evaluateNoise=false;
  P.subtractPedestals=false;
  P.executeGlobal();

  var outPath=pathJoin(outDir,name+".xisf");
  var win=ImageWindow.windowById(P.integrationImageId)||ImageWindow.activeWindow;
  if(!win) throw new Error("Integration result window not found for "+name);
  saveWindowAs(win,outPath);
  if(!File.exists(outPath)) throw new Error("Master not written: "+outPath);
  log(name+" -> "+outPath);

  function closeIf(id){ if(id){ var w=ImageWindow.windowById(id); if(w) w.close(); } }
  closeIf(P.highRejectionMapImageId); closeIf(P.lowRejectionMapImageId); closeIf(P.slopeMapImageId);
  return outPath;
}

// ---------- Calibration (existence checks + auto-retry) ----------
function calibrateLights(lightFiles, masters, outDir, cfaDetect){
  if(!lightFiles||lightFiles.length===0) throw new Error("No lights");
  ensureDir(outDir);

  function exist(p){ return !!(p && File.exists(p)); }
  var use={ bias:exist(masters.bias)?masters.bias:null, dark:exist(masters.dark)?masters.dark:null, flat:exist(masters.flat)?masters.flat:null };
  if(!use.bias && !use.dark && !use.flat){ log("No valid masters on disk → skipping calibration."); return lightFiles.slice(0); }

  function doCal(u){
    var P=new ImageCalibration;
    var t=[]; for(var i=0;i<lightFiles.length;i++) t.push([true,lightFiles[i]]); P.targetFrames=t;

    P.enableCFA=false;
    if(cfaDetect&&cfaDetect.enabled&&typeof cfaDetect.pattern==="number"){ P.enableCFA=true; P.cfaPattern=cfaDetect.pattern; }

    if(u.bias){ P.masterBiasEnabled=true; P.masterBiasPath=u.bias; } else P.masterBiasEnabled=false;
    if(u.dark){ P.masterDarkEnabled=true; P.masterDarkPath=u.dark; } else P.masterDarkEnabled=false;
    if(u.flat){ P.masterFlatEnabled=true; P.masterFlatPath=u.flat; } else P.masterFlatEnabled=false;

    P.outputPedestalMode=ImageCalibration.prototype.OutputPedestal_Auto;
    P.calibrateBias=false; P.calibrateDark=false; P.calibrateFlat=false; P.optimizeDarks=false;
    P.outputDirectory=outDir; P.overwriteExistingFiles=true;

    log("Using masters → bias: "+(u.bias||"none")+", dark: "+(u.dark||"none")+", flat: "+(u.flat||"none"));
    P.executeGlobal();

    var out=[]; for(var j=0;j<P.outputData.length;j++) out.push(P.outputData[j][0]); return out;
  }

  try { return doCal(use); }
  catch(e){
    var msg=e.toString(); warn("Calibration failed: "+msg+" → retry disabling missing master.");
    var u2={bias:use.bias,dark:use.dark,flat:use.flat};
    if(msg.indexOf("bias_master")>=0) u2.bias=null;
    if(msg.indexOf("dark_master")>=0) u2.dark=null;
    if(msg.indexOf("flat_master")>=0) u2.flat=null;
    if(!u2.bias && !u2.dark && !u2.flat){ warn("All masters disabled → skipping calibration."); return lightFiles.slice(0); }
    return doCal(u2);
  }
}

// ---------- Registration ----------
function alignLights(calibratedFiles, outDir){
  if(!calibratedFiles||calibratedFiles.length<1) throw new Error("No calibrated lights");
  ensureDir(outDir);

  var SA=new StarAlignment;
  SA.referenceIsFile=true; SA.referenceImage=calibratedFiles[0];
  var targets=[]; for(var i=0;i<calibratedFiles.length;i++) targets.push([true,true,calibratedFiles[i]]);
  SA.targets=targets;
  SA.structureLayers=5; SA.noiseLayers=1; SA.hotPixelFilterRadius=1;
  SA.sensitivity=0.100; SA.maxStarDistortion=0.500; SA.matcherTolerance=0.0030;
  SA.outputDirectory=outDir; SA.outputExtension=".xisf"; SA.outputPostfix="_r"; SA.maskPostfix="_m";
  SA.generateMasks=false; SA.frameAdaptation=false; SA.useSurfaceSplines=false; SA.overwriteExistingFiles=true;
  SA.onError=StarAlignment.prototype.Continue;

  SA.executeGlobal();

  var reg=[]; for(var k=0;k<calibratedFiles.length;k++){ var bn=baseName(calibratedFiles[k]); reg.push(pathJoin(outDir,bn+"_r.xisf")); }
  return reg;
}

// ---------- Integration ----------
function integrateChannel(registeredFiles, outDir, name){
  if(!registeredFiles||registeredFiles.length<1) throw new Error("No registered files for "+name);
  ensureDir(outDir);

  var P=new ImageIntegration;
  P.images=filesForImageIntegration(registeredFiles);
  P.combination=ImageIntegration.prototype.Average;
  P.weightMode=ImageIntegration.prototype.DontCare;
  P.normalization=ImageIntegration.prototype.MultiplicativeWithScaling;
  P.rejection=ImageIntegration.prototype.PercentileClip;
  P.rejectionNormalization=ImageIntegration.prototype.EqualizeFluxes;
  if(typeof P.evaluateSNR!=="undefined") P.evaluateSNR=true; else P.evaluateNoise=true;
  P.subtractPedestals=false;
  P.executeGlobal();

  var outPath=pathJoin(outDir,name+".xisf");
  var win=ImageWindow.windowById(P.integrationImageId)||ImageWindow.activeWindow;
  if(!win) throw new Error("Integrated window not found ("+name+")");
  saveWindowAs(win,outPath); log(name+" saved: "+outPath);

  function closeIf(id){ if(id){ var w=ImageWindow.windowById(id); if(w) w.close(); } }
  closeIf(P.highRejectionMapImageId); closeIf(P.lowRejectionMapImageId); closeIf(P.slopeMapImageId);
  return outPath;
}

// ---------- Main (ONE picker) ----------
function runAutoIntegrateSimple(){
  var all=selectAllOnce("Select ALL frames at once (LIGHT + optional BIAS/DARK/FLAT)");
  if(all.length===0){ warn("No files selected. Aborting."); return; }

  var base=dirName(all[0]); var work=pathJoin(base,"AI_Simple");
  var mastersDir=pathJoin(work,"masters"), cal=pathJoin(work,"cal"), reg=pathJoin(work,"reg"), out=pathJoin(work,"int");
  ensureDir(work); ensureDir(mastersDir); ensureDir(cal); ensureDir(reg); ensureDir(out);

  var sets=classifyFrames(all);
  if(sets.lights.length===0){ warn("No LIGHT frames detected (check IMAGETYP or filenames). Aborting."); return; }
  log("Detected → LIGHT:"+sets.lights.length+"  BIAS:"+sets.bias.length+"  DARK:"+sets.dark.length+"  FLAT:"+sets.flat.length);

  var masters={bias:null,dark:null,flat:null};
  if(sets.bias.length>=1){ log("Master bias..."); masters.bias=integrateMaster("bias_master",sets.bias,mastersDir); if(!File.exists(masters.bias)) masters.bias=null; }
  if(sets.dark.length>=1){ log("Master dark..."); masters.dark=integrateMaster("dark_master",sets.dark,mastersDir); if(!File.exists(masters.dark)) masters.dark=null; }
  if(sets.flat.length>=1){ log("Master flat..."); masters.flat=integrateMaster("flat_master",sets.flat,mastersDir); if(!File.exists(masters.flat)) masters.flat=null; }

  var cfa=detectCFAPatternFromFITS(sets.lights[0]); // safe

  log("Calibrating "+sets.lights.length+" lights");
  var calLights=calibrateLights(sets.lights,masters,cal,cfa);

  var groups=groupByFilter(calLights);
  function process(label, arr){
    if(arr.length===0) return;
    log("Registering "+arr.length+" frames for "+label);
    var regCh=alignLights(arr, reg);
    log("Integrating "+label);
    integrateChannel(regCh, out, "Integration_"+label);
  }
  process("R",groups.R); process("G",groups.G); process("B",groups.B);
  if(groups.UNK.length>0) warn("Unknown FILTER files: "+groups.UNK.length+" (skipped RGB integration)");

  console.noteln("Done. Outputs in: "+out);
}

// ---------- Entry ----------
runAutoIntegrateSimple();
