/*
 * Post-Integration Pipeline with Automated Astrometry.net Blind Solving
 * Fixed version - removed undefined ColorSpace_Gray
 */

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

(function(){

// ============================================================================
// PLATFORM DETECTION
// ============================================================================

var isWindows = (CoreApplication.platform == "MSWindows" || CoreApplication.platform == "Windows");
var isMacOS = (CoreApplication.platform == "MACOSX" || CoreApplication.platform == "macOS");
var isLinux = (CoreApplication.platform == "Linux");

if (!isWindows && !isMacOS && !isLinux && File.directoryExists("C:/Program Files")) {
    isWindows = true;
    isMacOS = false;
    isLinux = false;
}

var CMD_EXEC = isWindows ? "cmd.exe" : "/bin/sh";

// ============================================================================
// ASTROMETRY.NET AUTOMATED SOLVING
// ============================================================================

function getAstrometryAPIKey() {
    var settingsKey = "PostIntegrationPipeline/AstrometryAPIKey";
    try {
        var savedKey = Settings.read(settingsKey, DataType_String);
        if (savedKey && savedKey.length > 0) {
            return savedKey;
        }
    } catch (e) {
        // No saved key
    }

    // Ask user for API key
    var dialog = new APIKeyDialog();
    if (dialog.execute()) {
        var apiKey = dialog.apiKey;
        if (apiKey && apiKey.length > 0) {
            Settings.write(settingsKey, DataType_String, apiKey);
            return apiKey;
        }
    }

    return null;
}

function APIKeyDialog() {
    this.__base__ = Dialog;
    this.__base__();

    this.windowTitle = "Astrometry.net API Key Required";
    this.apiKey = "";

    this.sizer = new VerticalSizer;
    this.sizer.margin = 15;
    this.sizer.spacing = 10;

    var infoLabel = new Label(this);
    infoLabel.useRichText = true;
    var infoText = "<b>Automated Astrometry.net Blind Solving</b><br/><br/>";
    infoText += "This script will automatically upload your image to astrometry.net,<br/>";
    infoText += "wait for the solution, and apply the WCS data to your image.<br/><br/>";
    infoText += "Please enter your astrometry.net API key:<br/>";
    infoText += "(Get one free at: nova.astrometry.net)";
    infoLabel.text = infoText;
    infoLabel.wordWrapping = true;
    infoLabel.minWidth = 400;
    this.sizer.add(infoLabel);

    var keyLabel = new Label(this);
    keyLabel.text = "API Key:";
    this.sizer.add(keyLabel);

    this.keyEdit = new Edit(this);
    this.keyEdit.text = "kkjdojeiwunqowgv"; // Pre-fill with user's key
    this.keyEdit.minWidth = 400;
    this.keyEdit.onTextUpdated = function(text) {
        this.dialog.apiKey = text;
    };
    this.sizer.add(this.keyEdit);

    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var okButton = new PushButton(this);
    okButton.text = "Start Automated Solving";
    okButton.defaultButton = true;
    okButton.onClick = function() {
        this.dialog.apiKey = this.dialog.keyEdit.text;
        if (this.dialog.apiKey.trim().length === 0) {
            (new MessageBox("Please enter your API key.", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        this.dialog.ok();
    };

    var cancelButton = new PushButton(this);
    cancelButton.text = "Skip Solving";
    cancelButton.onClick = function() {
        this.dialog.cancel();
    };

    buttonRow.add(okButton);
    buttonRow.add(cancelButton);
    this.sizer.add(buttonRow);

    this.adjustToContents();
}

APIKeyDialog.prototype = new Dialog;

function hasAstrometricSolution(win) {
    try {
        var view = win.mainView;
        var keywords = view.window.keywords;
        var hasRA = false, hasDEC = false, hasCD = false, hasCTYPE = false;

        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            var name = keyword.name.toUpperCase();

            if (name === "RA" || name === "OBJCTRA" || name === "CRVAL1") hasRA = true;
            if (name === "DEC" || name === "OBJCTDEC" || name === "CRVAL2") hasDEC = true;
            if (name.indexOf("CD1_") === 0 || name.indexOf("CD2_") === 0) hasCD = true;
            if (name === "CTYPE1" || name === "CTYPE2") hasCTYPE = true;
        }

        var hasBasicCoords = hasRA && hasDEC;
        var hasWCSMatrix = hasCD && hasCTYPE;

        if (hasBasicCoords && hasWCSMatrix) {
            Console.writeln("✅ Valid astrometric solution found");
            return true;
        } else {
            Console.writeln("❌ No valid astrometric solution found");
            Console.writeln("   Has coordinates: " + hasBasicCoords);
            Console.writeln("   Has WCS matrix: " + hasWCSMatrix);
            return false;
        }

    } catch (e) {
        Console.writeln("❌ Error checking astrometric solution: " + e.message);
        return false;
    }
}

function getSessionKey(apiKey) {
    var sessionKey = null;
    var loginUrl = "http://nova.astrometry.net/api/login";
    var requestData = "request-json={\\\"apikey\\\":\\\"" + apiKey + "\\\"}";
    var responseFilename = File.systemTempDirectory + "/astrometry_login_response.json";

    if (File.exists(responseFilename)) {
        File.remove(responseFilename);
    }

    var scriptFilePath, curlCommand, scriptContent;

    if (isWindows) {
        scriptFilePath = File.systemTempDirectory + "/login_curl.bat";
        curlCommand = "curl -X POST -d \"" + requestData + "\" " + loginUrl + " -o " + responseFilename;
        scriptContent = "@echo on\n" + curlCommand + "\nexit\n";
    } else {
        scriptFilePath = File.systemTempDirectory + "/login_curl.sh";
        curlCommand = "/usr/bin/curl -X POST -d \"" + requestData + "\" " + loginUrl + " -o " + responseFilename;
        scriptContent = "#!/bin/bash\n" + curlCommand + "\n";
    }

    try {
        File.writeTextFile(scriptFilePath, scriptContent);
        Console.writeln("Login script created: " + scriptFilePath);

        var process = new ExternalProcess;
        var args = isWindows ? ["/c", scriptFilePath] : [scriptFilePath];

        if (process.start(CMD_EXEC, args)) {
            Console.writeln("Login process started successfully");
        }

        // Wait for response file
        var maxWaitTime = 15000;
        var waitTime = 0;

        while (!File.exists(responseFilename) && waitTime < maxWaitTime) {
            Console.writeln("Waiting for login response... (" + (waitTime/1000) + "s)");
            msleep(1000);
            waitTime += 1000;
        }

        if (File.exists(responseFilename)) {
            var jsonResponse = JSON.parse(File.readFile(responseFilename));
            Console.writeln("Login response: " + JSON.stringify(jsonResponse));

            if (jsonResponse.status === "success") {
                sessionKey = jsonResponse.session;
                Console.writeln("✅ Login successful, session key: " + sessionKey);
            } else {
                Console.writeln("❌ Login failed: " + JSON.stringify(jsonResponse));
            }
            File.remove(responseFilename);
        } else {
            Console.writeln("❌ Login timeout - no response file");
        }
    } catch (error) {
        Console.writeln("❌ Login error: " + error.message);
    }

    // Cleanup
    if (File.exists(scriptFilePath)) {
        File.remove(scriptFilePath);
    }

    return sessionKey;
}

function saveImageAsJpg(win) {
    var homeDir = File.systemTempDirectory;
    var jpgPath = homeDir + "/temp_image_solve.jpg";

    // Fixed: Use simpler approach to create export image
    var isGrayscale = !win.mainView.image.isColor;
    var numChannels = isGrayscale ? 1 : 3;

    var exportImage = new ImageWindow(
        win.mainView.image.width,
        win.mainView.image.height,
        numChannels,
        16,
        false,
        isGrayscale,
        win.mainView.id + "_temp"
    );

    exportImage.mainView.beginProcess(UndoFlag_NoSwapFile);
    exportImage.mainView.image.assign(win.mainView.image);
    exportImage.mainView.endProcess();

    // Auto-stretch if image is too dark (linear)
    var medianValue = exportImage.mainView.image.median();
    if (medianValue < 0.1) {
        Console.writeln("Image is linear (median=" + medianValue.toFixed(3) + "), applying stretch for solving...");

        var P = new PixelMath;
        P.expression = "C = -2.8; B = 0.25; c = min(max(0,med($T)+C*1.4826*mdev($T)),1); mtf(mtf(B,med($T)-c),max(0,($T-c)/~c))";
        P.useSingleExpression = true;
        P.createNewImage = false;
        P.executeOn(exportImage.mainView);
    }

    exportImage.saveAs(jpgPath, false, false, false, false);
    exportImage.close();

    Console.writeln("✅ Image saved for solving: " + jpgPath);
    return jpgPath;
}

function uploadImageToAstrometry(imageFile, sessionKey) {
    var subid = null;
    var responseFilename = File.systemTempDirectory + "/astrometry_upload_response.json";

    if (File.exists(responseFilename)) {
        File.remove(responseFilename);
    }

    var scriptFilePath, curlCommand, scriptContent;

    if (isWindows) {
        scriptFilePath = File.systemTempDirectory + "/upload_curl.bat";
        curlCommand = "curl -v -F \"request-json={\\\"publicly_visible\\\": \\\"y\\\", \\\"allow_modifications\\\": \\\"d\\\", \\\"session\\\": \\\"" + sessionKey + "\\\", \\\"allow_commercial_use\\\": \\\"d\\\"}\"" +
                      " -F \"file=@" + imageFile + "\" " +
                      "http://nova.astrometry.net/api/upload -o " + responseFilename;
        scriptContent = "@echo on\n" + curlCommand + "\nexit\n";
    } else {
        scriptFilePath = File.systemTempDirectory + "/upload_curl.sh";
        curlCommand = "/usr/bin/curl -v -F \"request-json={\\\"publicly_visible\\\": \\\"y\\\", \\\"allow_modifications\\\": \\\"d\\\", \\\"session\\\": \\\"" + sessionKey + "\\\", \\\"allow_commercial_use\\\": \\\"d\\\"}\"" +
                      " -F \"file=@" + imageFile + "\" " +
                      "http://nova.astrometry.net/api/upload -o " + responseFilename;
        scriptContent = "#!/bin/bash\n" + curlCommand + "\n";
    }

    try {
        File.writeTextFile(scriptFilePath, scriptContent);
        Console.writeln("Upload script created");

        var process = new ExternalProcess;
        var args = isWindows ? ["/c", scriptFilePath] : [scriptFilePath];

        if (process.start(CMD_EXEC, args)) {
            Console.writeln("Upload process started");
        }

        // Wait for response
        var maxWaitTime = 30000; // 30 seconds for upload
        var waitTime = 0;

        while (!File.exists(responseFilename) && waitTime < maxWaitTime) {
            Console.writeln("Uploading image... (" + (waitTime/1000) + "s)");
            msleep(2000);
            waitTime += 2000;
        }

        if (File.exists(responseFilename)) {
            var jsonResponse = JSON.parse(File.readFile(responseFilename));
            Console.writeln("Upload response: " + JSON.stringify(jsonResponse));

            if (jsonResponse.status === "success") {
                subid = jsonResponse.subid;
                Console.writeln("✅ Upload successful, subid: " + subid);
            } else {
                Console.writeln("❌ Upload failed: " + JSON.stringify(jsonResponse));
            }
            File.remove(responseFilename);
        } else {
            Console.writeln("❌ Upload timeout");
        }
    } catch (error) {
        Console.writeln("❌ Upload error: " + error.message);
    }

    // Cleanup
    if (File.exists(scriptFilePath)) {
        File.remove(scriptFilePath);
    }

    return subid;
}

function pollSubmissionStatus(subid) {
    var jobID = null;
    var retries = 0;
    var maxRetries = 60; // 10 minutes total (60 * 10 seconds)

    while (retries < maxRetries) {
        var responseFilename = File.systemTempDirectory + "/astrometry_status_response.json";
        if (File.exists(responseFilename)) {
            File.remove(responseFilename);
        }

        var scriptFilePath, curlCommand, scriptContent;

        if (isWindows) {
            scriptFilePath = File.systemTempDirectory + "/status_curl.bat";
            curlCommand = "curl -X GET http://nova.astrometry.net/api/submissions/" + subid + " -o " + responseFilename;
            scriptContent = "@echo on\n" + curlCommand + "\nexit\n";
        } else {
            scriptFilePath = File.systemTempDirectory + "/status_curl.sh";
            curlCommand = "/usr/bin/curl -X GET http://nova.astrometry.net/api/submissions/" + subid + " -o " + responseFilename;
            scriptContent = "#!/bin/bash\n" + curlCommand + "\n";
        }

        try {
            File.writeTextFile(scriptFilePath, scriptContent);

            var process = new ExternalProcess;
            var args = isWindows ? ["/c", scriptFilePath] : [scriptFilePath];

            if (process.start(CMD_EXEC, args)) {
                // Wait for response
                var waitTime = 0;
                while (!File.exists(responseFilename) && waitTime < 15000) {
                    msleep(1000);
                    waitTime += 1000;
                }

                if (File.exists(responseFilename)) {
                    var jsonResponse = JSON.parse(File.readFile(responseFilename));

                    if (jsonResponse.jobs && jsonResponse.jobs.length > 0 && jsonResponse.jobs[0] !== null) {
                        jobID = jsonResponse.jobs[0];
                        Console.writeln("✅ Job ID found: " + jobID);
                        File.remove(responseFilename);
                        File.remove(scriptFilePath);
                        return jobID;
                    } else {
                        Console.writeln("Solving in progress... (attempt " + (retries + 1) + "/" + maxRetries + ")");
                    }
                    File.remove(responseFilename);
                }
            }
            File.remove(scriptFilePath);
        } catch (error) {
            Console.writeln("❌ Status check error: " + error.message);
        }

        msleep(10000); // Wait 10 seconds before retry
        retries++;
    }

    Console.writeln("❌ Timeout waiting for solution");
    return null;
}

function getJobCalibration(jobID) {
    var calibrationData = null;
    var responseFilename = File.systemTempDirectory + "/astrometry_calibration_response.json";

    if (File.exists(responseFilename)) {
        File.remove(responseFilename);
    }

    var scriptFilePath, curlCommand, scriptContent;

    if (isWindows) {
        scriptFilePath = File.systemTempDirectory + "/calibration_curl.bat";
        curlCommand = "curl -X GET http://nova.astrometry.net/api/jobs/" + jobID + "/calibration/ -o " + responseFilename;
        scriptContent = "@echo on\n" + curlCommand + "\nexit\n";
    } else {
        scriptFilePath = File.systemTempDirectory + "/calibration_curl.sh";
        curlCommand = "/usr/bin/curl -X GET http://nova.astrometry.net/api/jobs/" + jobID + "/calibration/ -o " + responseFilename;
        scriptContent = "#!/bin/bash\n" + curlCommand + "\n";
    }

    try {
        File.writeTextFile(scriptFilePath, scriptContent);

        var process = new ExternalProcess;
        var args = isWindows ? ["/c", scriptFilePath] : [scriptFilePath];

        if (process.start(CMD_EXEC, args)) {
            // Wait for response
            var waitTime = 0;
            while (!File.exists(responseFilename) && waitTime < 15000) {
                msleep(1000);
                waitTime += 1000;
            }

            if (File.exists(responseFilename)) {
                var jsonResponse = JSON.parse(File.readFile(responseFilename));
                Console.writeln("Calibration response: " + JSON.stringify(jsonResponse));

                if (jsonResponse && jsonResponse.ra) {
                    calibrationData = jsonResponse;
                    Console.writeln("✅ Calibration data retrieved");
                } else {
                    Console.writeln("❌ No calibration data in response");
                }
                File.remove(responseFilename);
            }
        }
        File.remove(scriptFilePath);
    } catch (error) {
        Console.writeln("❌ Calibration retrieval error: " + error.message);
    }

    return calibrationData;
}

function applyAstrometricSolution(win, calibrationData) {
    Console.writeln("\n=== Applying Astrometric Solution ===");
    Console.writeln("RA: " + calibrationData.ra.toFixed(6));
    Console.writeln("Dec: " + calibrationData.dec.toFixed(6));
    Console.writeln("Pixel Scale: " + calibrationData.pixscale.toFixed(6) + " arcsec/pixel");
    Console.writeln("Orientation: " + calibrationData.orientation.toFixed(6) + " degrees");

    try {
        var view = win.mainView;
        var raStr = calibrationData.ra.toFixed(6);
        var decStr = calibrationData.dec.toFixed(6);

        var keywords = [
            new FITSKeyword("CTYPE1", "RA---TAN", "Coordinate type for axis 1"),
            new FITSKeyword("CTYPE2", "DEC--TAN", "Coordinate type for axis 2"),
            new FITSKeyword("CRVAL1", raStr, "Reference value for axis 1"),
            new FITSKeyword("CRVAL2", decStr, "Reference value for axis 2"),
            new FITSKeyword("CRPIX1", (view.image.width / 2 + 0.5).toFixed(6), "Reference pixel for axis 1"),
            new FITSKeyword("CRPIX2", (view.image.height / 2 + 0.5).toFixed(6), "Reference pixel for axis 2"),
            new FITSKeyword("CD1_1", (-Math.cos(calibrationData.orientation * Math.PI / 180) * calibrationData.pixscale / 3600).toFixed(12), "Transformation matrix element 1_1"),
            new FITSKeyword("CD1_2", (Math.sin(calibrationData.orientation * Math.PI / 180) * calibrationData.pixscale / 3600).toFixed(12), "Transformation matrix element 1_2"),
            new FITSKeyword("CD2_1", (-Math.sin(calibrationData.orientation * Math.PI / 180) * calibrationData.pixscale / 3600).toFixed(12), "Transformation matrix element 2_1"),
            new FITSKeyword("CD2_2", (-Math.cos(calibrationData.orientation * Math.PI / 180) * calibrationData.pixscale / 3600).toFixed(12), "Transformation matrix element 2_2"),
            new FITSKeyword("RADECSYS", "ICRS", "Coordinate reference system")
        ];

        win.keywords = keywords;
        Console.writeln("✅ Astrometric solution applied to image header");
        return true;
    } catch (error) {
        Console.writeln("❌ Failed to apply astrometric solution: " + error.message);
        return false;
    }
}

function performAutomatedBlindSolve(win) {
    Console.writeln("\n=== Starting Automated Blind Solve ===");

    // Get API key
    var apiKey = getAstrometryAPIKey();
    if (!apiKey) {
        Console.writeln("❌ No API key provided");
        return false;
    }

    // Step 1: Save image as JPG
    Console.writeln("Step 1: Preparing image for upload...");
    var imageFile = saveImageAsJpg(win);
    if (!imageFile) {
        Console.writeln("❌ Failed to save image");
        return false;
    }

    // Step 2: Get session key
    Console.writeln("Step 2: Authenticating with astrometry.net...");
    var sessionKey = getSessionKey(apiKey);
    if (!sessionKey) {
        Console.writeln("❌ Failed to get session key");
        return false;
    }

    // Step 3: Upload image
    Console.writeln("Step 3: Uploading image to astrometry.net...");
    var subid = uploadImageToAstrometry(imageFile, sessionKey);
    if (!subid) {
        Console.writeln("❌ Failed to upload image");
        return false;
    }

    // Step 4: Wait for solution
    Console.writeln("Step 4: Waiting for solution (this may take several minutes)...");
    var jobID = pollSubmissionStatus(subid);
    if (!jobID) {
        Console.writeln("❌ Solution timeout or failed");
        return false;
    }

    // Step 5: Get calibration data
    Console.writeln("Step 5: Retrieving calibration data...");
    var calibrationData = getJobCalibration(jobID);
    if (!calibrationData) {
        Console.writeln("❌ Failed to get calibration data");
        return false;
    }

    // Step 6: Apply solution
    Console.writeln("Step 6: Applying astrometric solution...");
    var success = applyAstrometricSolution(win, calibrationData);

    // Cleanup
    try {
        if (File.exists(imageFile)) {
            File.remove(imageFile);
        }
    } catch (e) {
        Console.writeln("Warning: Could not remove temporary file");
    }

    if (success) {
        Console.writeln("🎉 Automated blind solve completed successfully!");
        return true;
    } else {
        Console.writeln("❌ Failed to apply solution");
        return false;
    }
}

function performSPCC(win, isRGB) {
    Console.writeln("\n=== SPCC (Spectrophotometric Color Calibration) ===");

    // Check for astrometric solution first
    if (!hasAstrometricSolution(win)) {
        Console.writeln("❌ No astrometric solution found");

        var solveChoice = (new MessageBox(
            "No Astrometric Solution Found\n\n" +
            "The image needs an astrometric solution for SPCC to work properly.\n\n" +
            "Would you like to perform automated blind plate solving using astrometry.net?\n\n" +
            "This will upload your image to astrometry.net, wait for the solution,\n" +
            "and automatically apply the WCS data to your image.\n\n" +
            "YES = Start automated blind solving\n" +
            "NO = Skip SPCC (continue without color calibration)",
            "Automated Blind Solving",
            StdIcon_Question,
            StdButton_Yes, StdButton_No
        )).execute();

        if (solveChoice == StdButton_No) {
            Console.writeln("⏭️ SPCC skipped by user");
            return false;
        }

        // Perform automated blind solve
        var solveSuccess = performAutomatedBlindSolve(win);
        if (!solveSuccess) {
            Console.writeln("❌ Automated blind solve failed - SPCC cannot proceed");
            return false;
        }

        // Verify we now have a solution
        if (!hasAstrometricSolution(win)) {
            Console.writeln("❌ Still no astrometric solution after blind solve - SPCC cannot proceed");
            return false;
        }
    }

    try {
        var P = new SpectrophotometricColorCalibration();

        // Configure SPCC parameters
        try { P.whiteReferenceId = "Average Spiral Galaxy"; } catch(_) {
            try { P.whiteReferenceId = "AVG_G2V"; } catch(_) { }
        }

        if (isRGB) {
            Console.writeln("Using RGB filter profile");
            try {
                P.redFilter = "Astronomik Typ 2c R";
                P.greenFilter = "Astronomik Typ 2c G";
                P.blueFilter = "Astronomik Typ 2c B";
            } catch(_) {}
        } else {
            Console.writeln("Using OSC filter profile");
            try {
                P.redFilter = "Sony Color Sensor R";
                P.greenFilter = "Sony Color Sensor G";
                P.blueFilter = "Sony Color Sensor B";
            } catch(_) {}
        }

        try { P.applyCalibration = true; } catch(_) {}
        try { P.generateGraphs = false; } catch(_) {}
        try { P.catalog = "Gaia DR3/SP"; } catch(_) {}

        Console.writeln("Executing SPCC...");
        P.executeOn(win.mainView);

        // Verify success
        if (hasAstrometricSolution(win)) {
            Console.writeln("✅ SPCC completed successfully");
            return true;
        } else {
            Console.writeln("❌ SPCC failed - astrometric solution was lost during processing");
            return false;
        }

    } catch (e) {
        Console.writeln("❌ SPCC failed with error: " + e.message);
        return false;
    }
}

// ============================================================================
// MAIN FUNCTION
// ============================================================================

function processCurrentImage() {
    var currentWindow = ImageWindow.activeWindow;
    if (!currentWindow) {
        (new MessageBox(
            "No Active Image Window\n\n" +
            "Please open an image first, then run this script.",
            "No Image Found",
            StdIcon_Error,
            StdButton_Ok
        )).execute();
        return;
    }

    Console.writeln("Processing image: " + currentWindow.mainView.id);

    var isColor = currentWindow.mainView.image.isColor;
    Console.writeln("Image type: " + (isColor ? "Color" : "Monochrome"));

    if (!isColor) {
        (new MessageBox(
            "Monochrome Image Detected\n\n" +
            "SPCC is designed for color images. This appears to be a monochrome image.\n\n" +
            "SPCC will be skipped.",
            "Monochrome Image",
            StdIcon_Information,
            StdButton_Ok
        )).execute();
        Console.writeln("⏭️ SPCC skipped - monochrome image");
        return;
    }

    // Detect RGB vs OSC
    var isRGB = false; // Default to OSC
    Console.writeln("Filter type: " + (isRGB ? "RGB (separate filters)" : "OSC (color camera)"));

    // Run SPCC with automated blind solving
    var success = performSPCC(currentWindow, isRGB);

    if (success) {
        Console.writeln("\n🎉 SPCC processing completed successfully!");
        (new MessageBox(
            "SPCC Processing Complete!\n\n" +
            "Your image has been plate solved and color calibrated successfully.\n\n" +
            "The astrometric solution and color calibration have been applied to your image.",
            "Success!",
            StdIcon_Information,
            StdButton_Ok
        )).execute();
    } else {
        Console.writeln("\n⚠️ SPCC was skipped or failed. See Console for details.");
    }
}

function showMainGUI() {
    var dlg = new Dialog;
    dlg.windowTitle = "Automated Blind Solving + SPCC";
    dlg.sizer = new VerticalSizer;
    dlg.sizer.margin = 15;
    dlg.sizer.spacing = 10;

    var title = new Label(dlg);
    title.useRichText = true;
    title.text = "<b>Automated Astrometry.net Blind Solving + SPCC</b>";
    title.textAlignment = TextAlign_Center;
    dlg.sizer.add(title);

    var desc = new Label(dlg);
    desc.useRichText = true;
    desc.text = "This script provides <b>fully automated blind plate solving</b> integrated with SPCC.<br/><br/>" +
                "<b>What it does:</b><br/>" +
                "• Checks if your image has an astrometric solution<br/>" +
                "• If not found, automatically uploads to astrometry.net<br/>" +
                "• Waits for the solution and applies WCS data<br/>" +
                "• Runs SPCC for color calibration<br/><br/>" +
                "<b>Requirements:</b><br/>" +
                "• Astrometry.net API key (free at nova.astrometry.net)<br/>" +
                "• Internet connection<br/>" +
                "• curl (usually pre-installed on most systems)";
    desc.wordWrapping = true;
    desc.minWidth = 500;
    dlg.sizer.add(desc);

    var currentWindow = ImageWindow.activeWindow;
    var imageGroup = new GroupBox(dlg);
    imageGroup.title = "Current Image Status";
    imageGroup.sizer = new VerticalSizer;
    imageGroup.sizer.margin = 8;
    imageGroup.sizer.spacing = 4;

    var imageInfo = new Label(dlg);
    if (currentWindow) {
        var isColor = currentWindow.mainView.image.isColor;
        var hasWCS = hasAstrometricSolution(currentWindow);
        imageInfo.text = "Image: " + currentWindow.mainView.id + "\n" +
                        "Type: " + (isColor ? "Color" : "Monochrome") + "\n" +
                        "Astrometric solution: " + (hasWCS ? "✅ Found" : "❌ Missing (will auto-solve)");
    } else {
        imageInfo.text = "No active image window found.\nPlease open an image first.";
    }
    imageGroup.sizer.add(imageInfo);
    dlg.sizer.add(imageGroup);

    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var processButton = new PushButton(dlg);
    processButton.text = "Start Automated Processing";
    processButton.defaultButton = true;
    processButton.enabled = (currentWindow != null);
    processButton.onClick = function() {
        dlg.ok();
    };

    var cancelButton = new PushButton(dlg);
    cancelButton.text = "Cancel";
    cancelButton.onClick = function() {
        dlg.cancel();
    };

    buttonRow.add(processButton);
    buttonRow.add(cancelButton);
    dlg.sizer.add(buttonRow);

    return dlg.execute();
}

// ============================================================================
// MAIN ENTRY POINT
// ============================================================================

function main() {
    Console.show();
    Console.writeln("=== Automated Blind Solving + SPCC ===");
    Console.writeln("Platform: " + (isWindows ? "Windows" : isMacOS ? "macOS" : "Linux"));

    if (showMainGUI()) {
        processCurrentImage();
    } else {
        Console.writeln("Cancelled by user");
    }
}

// Run the script
main();

})();
