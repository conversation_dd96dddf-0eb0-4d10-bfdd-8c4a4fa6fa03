/*
 * DEBUG VERSION - Post-Integration Pipeline
 * This version has debug output to identify where the script is failing
 */

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

(function(){

try {
    Console.show();
    Console.writeln("=== DEBUG: Script starting ===");

    // Test basic functionality
    Console.writeln("DEBUG: Platform = " + CoreApplication.platform);

    var isWindows = (CoreApplication.platform == "MSWindows");
    var isMacOS = (CoreApplication.platform == "MacOSX");
    var isLinux = (CoreApplication.platform == "Linux");

    Console.writeln("DEBUG: isWindows=" + isWindows + ", isMacOS=" + isMacOS + ", isLinux=" + isLinux);

    // Force Windows detection if needed
    if (!isWindows && !isMacOS && !isLinux && File.directoryExists("C:/Program Files")) {
        isWindows = true;
        isMacOS = false;
        isLinux = false;
        Console.writeln("DEBUG: Forced Windows detection");
    }

    Console.writeln("DEBUG: Final platform - Windows:" + isWindows + " macOS:" + isMacOS + " Linux:" + isLinux);

    // Test file system access
    var homeDir = File.homeDirectory;
    Console.writeln("DEBUG: Home directory = " + homeDir);

    // Test default paths
    var defaultInputDir = isWindows ? "C:/AstroImages/Processed" : homeDir + "/AstroImages/Processed";
    var defaultOutputDir = isWindows ? "C:/AstroImages/Output" : homeDir + "/AstroImages/Output";

    Console.writeln("DEBUG: Default input dir = " + defaultInputDir);
    Console.writeln("DEBUG: Default output dir = " + defaultOutputDir);

    // Test GUI creation
    Console.writeln("DEBUG: Creating GUI...");

    var dlg = new Dialog;
    dlg.windowTitle = "DEBUG - Post-Integration Pipeline";
    dlg.sizer = new VerticalSizer;
    dlg.sizer.margin = 10;
    dlg.sizer.spacing = 8;

    var titleLabel = new Label(dlg);
    titleLabel.text = "DEBUG VERSION - Script is working!";
    titleLabel.textAlignment = TextAlign_Center;
    dlg.sizer.add(titleLabel);

    var infoLabel = new Label(dlg);
    infoLabel.text = "Platform: " + (isWindows ? "Windows" : isMacOS ? "macOS" : "Linux") + "\n" +
                     "Home: " + homeDir + "\n" +
                     "Input: " + defaultInputDir + "\n" +
                     "Output: " + defaultOutputDir;
    dlg.sizer.add(infoLabel);

    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var okButton = new PushButton(dlg);
    okButton.text = "OK";
    okButton.defaultButton = true;
    okButton.onClick = function() {
        dlg.ok();
    };

    var cancelButton = new PushButton(dlg);
    cancelButton.text = "Cancel";
    cancelButton.onClick = function() {
        dlg.cancel();
    };

    buttonRow.add(okButton);
    buttonRow.add(cancelButton);
    dlg.sizer.add(buttonRow);

    Console.writeln("DEBUG: About to show GUI...");

    var result = dlg.execute();

    Console.writeln("DEBUG: GUI result = " + result);

    if (result) {
        Console.writeln("✅ DEBUG: All basic functionality is working!");
        Console.writeln("✅ The issue is likely in the complex parts of the main script.");
        Console.writeln("✅ Ready to run the simplified version.");
    } else {
        Console.writeln("❌ DEBUG: User cancelled");
    }

} catch (e) {
    Console.criticalln("❌ DEBUG ERROR: " + e.message);
    Console.criticalln("❌ ERROR at line: " + (e.lineNumber || "unknown"));
    Console.criticalln("❌ Full error: " + e.toString());
}

})();
