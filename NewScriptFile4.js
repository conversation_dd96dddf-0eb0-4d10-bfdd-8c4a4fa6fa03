/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * WORK5.1 VERSION - COMPATIBILITY FIX
 *
 * Changes from V5:
 * - FIX: Replaced ES11 Nullish Coalescing Operator (??) with compatible syntax for older PixInsight JS runtime.
 * - FIX: Replaced ES6 default function parameters with compatible syntax.
 * - Retains V5 features: Step Control & Subtraction-based Starmask generation.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

// --- Ensure this path is correct for your installation ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){
// -------------------- Configuration & Globals --------------------

// V5: Initialize modes from settings, defaulting to automated (false) if not set.
var settingsKey="PostIntegrationPipeline_V5";

// V5.1 FIX: Load settings using compatible syntax instead of ?? operator.
// The PixInsight JS engine may not support ??. We use a traditional check instead.
// Default: Batch mode (Interactive=false), No intermediate saves (Debug=false).

var loadedDebugMode = Settings.read(settingsKey+"/DebugMode", DataType_Boolean);
// Use == null to check for both null and undefined (older JS compatibility)
var DEBUG_MODE = (loadedDebugMode == null) ? false : loadedDebugMode;

var loadedInteractiveMode = Settings.read(settingsKey+"/InteractiveMode", DataType_Boolean);
var INTERACTIVE_MODE = (loadedInteractiveMode == null) ? false : loadedInteractiveMode;


// Enforce dependency: Interactive Mode requires Debug Mode (for revert functionality)
if (INTERACTIVE_MODE && !DEBUG_MODE) {
    DEBUG_MODE = true;
    // Update the setting persistently
    Settings.write(settingsKey+"/DebugMode", DataType_Boolean, true);
}

var debugStepCounter = 1;
var USER_ABORTED = false;

// Custom Error Types for Flow Control
const ABORT_PROCESSING = "ABORT_PROCESSING"; // Stop the entire batch
const ABORT_PROCESSING_IMAGE = "ABORT_PROCESSING_IMAGE"; // Stop current image, continue batch

var outputExtension = ".xisf";

var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  // V5 Change: Consolidated step toggles
  steps: {
      // Linear Standard
      abe: true,
      gradientCorrection: true,
      graxpert: true,
      blackPoint: true,
      spcc: true,
      // Linear AI
      deblur1: true,
      deblur2: true,
      // Stretched
      stretch: true,
      denoise: true,
      starless: true
  },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

// -------------------- Utility Functions --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}

// V5: Ensure sanitized names are valid PixelMath identifiers (alphanumeric and underscore only)
function sanitizeBase(name){
    // Replace invalid characters with underscore
    var b=name.replace(/[^a-zA-Z0-9_]/g,"_");
    // Ensure it starts with a valid character if it doesn't already
    if (!/^[a-zA-Z_]/.test(b)) {
        b = "img_" + b;
    }
    // Truncate if too long for PixInsight identifiers (e.g., > 64 chars)
    if (b.length > 60) {
        b = b.substring(0, 60);
    }
    return b.length?b:"image";
}
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}

// V5.1: Robust 16-bit TIFF saving
function saveAs16BitTiff(win, path) {
    if (!win || !win.isWindow) return;
    var tifPath = File.changeExtension(path, ".tif");
    // Ensure the image is saved as 16-bit integer format
    // Parameters: path, confirmOverwrite, signal, warnings, strict, sampleFormat (16 for 16-bit integer)
    try {
        // Attempt modern/explicit signature
        win.saveAs(tifPath, false, false, true, false, 16);
    } catch(e) {
         // Fallback for older versions where the sampleFormat argument might cause an error or be ignored
        Console.writeln("Info: Using fallback saveAs signature for TIFF. (Explicit 16-bit failed: " + e.message + ")");
        win.saveAs(tifPath, false, false, true, false);
    }
    Console.writeln("  Saved TIFF: ", File.extractName(tifPath));
}

// -------------------- Debug and Revert Functions --------------------
function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }
    // Create subfolder for this step
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    // Use sanitizeBase for the step name used in file paths
    let sanitizedStepName = sanitizeBase(stepName);
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;

    // Create the step subfolder
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        stepFolder = debugDir;
    }

    // Use sanitizeBase for the baseTag used in file paths
    let baseFileName = counterStr + "_" + sanitizeBase(baseTag) + "_" + sanitizedStepName;

    // Save as XISF (critical for revert)
    let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
    try {
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
        // If debug save fails, revert functionality is compromised.
        Console.criticalln("CRITICAL: Debug save failed. Revert functionality may be compromised. Check disk space/permissions.");
    }
    debugStepCounter++;
}

// ############ REVISED REVERT FUNCTION ############
// Dynamically searches for the previous step's folder and file. Handles window validity.
function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
    Console.writeln("🔄 REVERT: Looking for previous step before step " + currentStepNumber);

    // V5: Check if revert is possible
    if (!DEBUG_MODE) {
        Console.writeln("⚠️ Cannot revert - Debug Mode is disabled. Intermediate files were not saved.");
        return null;
    }

    if (currentStepNumber <= 1) {
        Console.writeln("⚠️ Cannot revert - already at the first step (Initial Integration).");
        return null; // Cannot revert before the start
    }

    var previousStepNumber = currentStepNumber - 1;
    var previousStepStr = (previousStepNumber < 10 ? "0" : "") + previousStepNumber;
    var revertFile = null;

    Console.writeln("🔍 Searching for previous step's folder starting with '" + previousStepStr + "_' in: " + debugDir);

    try {
        var ff = new FileFind();
        if (ff.begin(debugDir + "/" + previousStepStr + "_*")) {
            do {
                if (ff.isDirectory) {
                    var folderPath = debugDir + "/" + ff.name;
                    // Calculate substring index dynamically based on the length of the step number string.
                    var stepNamePart = ff.name.substring(previousStepStr.length + 1); // Removes the "NN_" prefix
                    // Reconstruct the expected filename (using sanitized baseTag)
                    var xisfFile = folderPath + "/" + previousStepStr + "_" + sanitizeBase(baseTag) + "_" + stepNamePart + ".xisf";

                    if (File.exists(xisfFile)) {
                        revertFile = xisfFile;
                        Console.writeln("✅ Found revert file: " + revertFile);
                        break;
                    }
                }
            } while (ff.next());
        }
        ff.end();
    } catch (e) {
        Console.writeln("❌ Error while searching for revert file: " + e.message);
    }

    if (!revertFile) {
        Console.writeln("❌ REVERT FAILED: Could not find the output file from step " + previousStepNumber);
        return null;
    }

    // --- Logic to open the file and handle window replacement ---
    try {
        Console.writeln("🔄 REVERTING: Loading " + revertFile);
        var currentId = "restored_view"; // Default ID

        // Check if the window is still valid before trying to close it
        if (win && win instanceof ImageWindow && win.isWindow) {
            currentId = win.mainView.id;
            // Ensure forceClose is still a function (defensive programming)
            if (typeof win.forceClose === 'function') {
                win.forceClose();
            } else {
                // This should theoretically not happen anymore after V4 fixes.
                Console.criticalln("❌ REVERT FAILED: win.forceClose() method is missing! Cannot close current window.");
                return null;
            }
        } else {
            // If the window is gone (e.g., process failure closed it), we just proceed to open the restored one.
            Console.writeln("Note: Current window was already closed or invalid.");
        }

        var windows = ImageWindow.open(revertFile);
        if (windows.length > 0) {
            var restoredWin = windows[0];
             // Try to preserve the view ID if possible, ensuring it doesn't conflict
            try {
                 restoredWin.mainView.id = currentId;
            } catch (e) {
                 // If the ID is already in use (e.g., by a temporary window not yet closed), use a fallback
                 Console.writeln("Warning: Could not restore original view ID (" + currentId + "): " + e.message);
            }
            restoredWin.show();
            restoredWin.bringToFront();
            Console.writeln("✅ REVERT SUCCESSFUL");
            return restoredWin;
        } else {
            Console.writeln("❌ REVERT FAILED: Could not open file " + revertFile);
            return null;
        }
    } catch (e) {
        Console.writeln("❌ REVERT ERROR during file load: " + e.message);
        return null;
    }
}

// -------------------- Interactive Review Functions --------------------

function showStretchedPreview(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    Console.writeln("📺 Applying auto-stretch for visual review...");
    try {
        win.show();
        win.bringToFront();
        win.zoomToFit();

        var view = win.mainView;

        // Calculate proper auto-stretch parameters
        var median = view.computeOrFetchProperty("Median");
        var mad = view.computeOrFetchProperty("MAD");
        var stf = new ScreenTransferFunction;

        var n = view.image.isColor ? 3 : 1;
        var stfParams = [
            [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
        ];

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826; // Convert MAD to sigma
            var c0 = Math.max(0, med - 2.8 * madVal);  // Shadow clipping
            var m = Math.mtf(0.25, med - c0);  // Midtones balance
            stfParams[c] = [c0, 1.0, m, 0, 1];
        }

        // Apply the STF
        stf.STF = stfParams;
        stf.executeOn(view);

        // Force window refresh
        win.show();
        win.bringToFront();
        Console.writeln("✅ Auto-stretch applied for review");
        return true;
    } catch(e) {
        Console.writeln("❌ Auto-stretch failed: " + e.message);
        return false;
    }
}

function resetStretch(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;
    try {
        if (typeof win.disableScreenTransferFunctions === 'function') {
            win.disableScreenTransferFunctions();
        } else {
            // Fallback: apply identity STF
            var stf = new ScreenTransferFunction;
            var identitySTF = [
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
            ];
            stf.STF = identitySTF;
            stf.executeOn(win.mainView);
        }
        Console.writeln("✅ Reset to linear view");
        return true;
    } catch(e) {
        Console.writeln("⚠️ Reset failed: " + e.message + " (continuing anyway)");
        return true;
    }
}

function askAcceptStep(stepName, description, stepNumber) {
    if (!INTERACTIVE_MODE) return "accept"; // Auto-accept if not interactive

    Console.writeln("\n🔍 REVIEW STEP " + stepNumber + ": " + stepName);

    var result = (new MessageBox(
        "Step " + stepNumber + ": " + stepName + "\n\n" +
        (description || "Please review the image on screen.") + "\n\n" +
        "Choose your action:\n\n" +
        "YES = Accept and keep this step\n" +
        "NO = Skip this step (revert to previous step)\n" +
        "CANCEL = Stop processing entirely",
        "Step " + stepNumber + " - Accept, Skip, or Stop?",
        StdIcon_Question,
        StdButton_Yes, StdButton_No, StdButton_Cancel
    )).execute();

    if (result == StdButton_Yes) {
        Console.writeln("✅ ACCEPTED Step " + stepNumber + ": " + stepName);
        return "accept";
    } else if (result == StdButton_No) {
        Console.writeln("⏭️ SKIPPED Step " + stepNumber + ": " + stepName + " - reverting to previous step");
        return "skip";
    } else {
        Console.writeln("🛑 STOPPED: Processing aborted by user at Step " + stepNumber + ": " + stepName);
        USER_ABORTED = true;
        throw new Error(ABORT_PROCESSING); // Force immediate stop of the batch
    }
}

// -------------------- File System Functions --------------------
function findAllInputImages(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[];
  var ff=new FileFind;
  var supportedExtensions = [".xisf", ".fit", ".fits", ".tif", ".tiff"];
  if(ff.begin(dir+"/*.*")){
    do {
      var nameLower = ff.name.toLowerCase();
      for (var i = 0; i < supportedExtensions.length; ++i) {
        if (nameLower.endsWith(supportedExtensions[i])) {
          v.push(dir+"/"+ff.name);
          break;
        }
      }
    } while(ff.next());
  }
  return v;
}

function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /filter-r(?![a-z])/.test(s) || /\bred\b/.test(s)) return "R";
  if (s.indexOf("filter-green")>=0 || /filter-g(?![a-z])/.test(s) || /\bgreen\b/.test(s)) return "G";
  if (s.indexOf("filter-blue")>=0 || /filter-b(?![a-z])/.test(s) || /\bblue\b/.test(s)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}

function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }

  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }

  var haveR=!!(byFilter.R&&byFilter.R.length), haveG=!!(byFilter.G&&byFilter.G.length), haveB=!!(byFilter.B&&byFilter.B.length);

  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };

  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }

  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k, isStackedRGB: false });
    }
  }

  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);

  for (var k=0;k<unknownSingles.length;++k) {
    var filePath = unknownSingles[k];
    var isColorStack = false;
    try {
        // Open temporarily to check if it's a color image
        var tempWinArr = ImageWindow.open(filePath);
        if (tempWinArr.length > 0) {
            var tempWin = tempWinArr[0];
            if (tempWin.mainView.image.isColor) isColorStack = true;
            tempWin.forceClose();
        }
    } catch (e) {
        Console.writeln("Warning: Could not determine color space for " + File.extractName(filePath) + ". Assuming Mono.");
    }
    plan.singles.push({ path: filePath, tag: isColorStack ? "Color" : "Single", isStackedRGB: isColorStack });
  }
  return plan;
}


// -------------------- Processing Functions (Robust Pattern) --------------------
/*
 * Generic handler for process execution, interactive review, and auto-revert on failure.
 */
function handleRobustExecution(win, stepName, sum, sumKey, executionFunc, baseTag, debugDir) {
    if (!win || !win.isWindow) {
        Console.criticalln("❌ CRITICAL: Invalid window passed to " + stepName + ". Aborting image.");
        throw new Error(ABORT_PROCESSING_IMAGE);
    }

    try {
        Console.writeln("\n=== Running " + stepName + " ===");

        // 1. Execute the core logic (provided via executionFunc)
        var details = executionFunc(win);
        sum[sumKey] = {name: stepName, status: "✅", details: details || "Applied successfully"};

        // 2. Save successful state (only if DEBUG_MODE=true)
        debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_'), baseTag, debugDir);

        // The step number that was just executed (and potentially saved).
        var executedStepNumber = debugStepCounter - 1;

        // 3. Interactive Review (only if INTERACTIVE_MODE=true)
        if (INTERACTIVE_MODE) {
            // Check if image is already stretched
            var isAlreadyStretched = (sum.stretch && sum.stretch.status === "✅");

            if (isAlreadyStretched) {
                // Image is already stretched - just show it
                win.show();
                win.bringToFront();
                win.zoomToFit();
                Console.writeln("📺 Displaying already stretched image for review");
            } else {
                // Image is linear - apply auto-stretch for preview
                showStretchedPreview(win);
            }

            processEvents();
            msleep(1000);

            var decision = askAcceptStep(stepName, "Review the result of " + stepName + ".", executedStepNumber);

            if (!isAlreadyStretched) {
                resetStretch(win);
            }

            if (decision === "skip") {
                // Revert FROM the executed step
                var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    sum[sumKey].status = "⏭️";
                    sum[sumKey].details = "Skipped by user (Interactive)";
                } else {
                    // This should only happen if INTERACTIVE_MODE=true but DEBUG_MODE was somehow false or file save failed.
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }
        return win;

    } catch (e) {
        if (e.message === ABORT_PROCESSING || e.message === ABORT_PROCESSING_IMAGE) throw e;

        // 4. Handle Failure and Auto-Revert
        Console.writeln("❌ " + stepName + " FAILED: " + e.message);

        // Save failed state (if DEBUG_MODE=true and window is valid)
        if (win && win.isWindow) {
            debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_') + "_FAILED", baseTag, debugDir);
        }

        // The step number associated with the failure.
        var failedStepNumber = debugStepCounter - 1;

        Console.writeln("🔄 Attempting automatic revert because " + stepName + " failed.");

        // Revert FROM the failed step. (Will only succeed if DEBUG_MODE=true)
        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            // Revert successful (DEBUG_MODE=true)
            win = restoredWin;
            sum[sumKey] = {name: stepName, status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
        } else {
            // Critical failure (DEBUG_MODE=false, or DEBUG_MODE=true but revert failed)
            Console.criticalln("❌ CRITICAL: " + stepName + " failed, and revert failed (or Debug Mode is off). Aborting image.");
            // We must abort the image as it is in an inconsistent state and cannot be recovered.
            throw new Error(ABORT_PROCESSING_IMAGE);
        }
        return win;
    }
}

// --- Specific Implementations using the Robust Pattern ---
function finalABE(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "ABE (Background Extraction)", sum, "backgroundExtraction", function(w) {
        var P = new AutomaticBackgroundExtractor;
        P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050;
        P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5;
        P.targetCorrection = AutomaticBackgroundExtractor.prototype.Subtraction; P.normalize = true; P.replaceTarget = true; P.discardModel = true;
        P.executeOn(w.mainView);
        return "ABE Subtraction applied";
    }, baseTag, debugDir);
}

function runGradientCorrection(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GradientCorrection", sum, "gradientCorrection", function(w) {
        // Check if GradientCorrection process exists (Added robustness)
        if (typeof GradientCorrection === 'undefined') {
            throw new Error("GradientCorrection process not found. Please ensure PixInsight is updated.");
        }
        var P = new GradientCorrection;
        P.reference = 0.50; P.lowThreshold = 0.20; P.lowTolerance = 0.50; P.highThreshold = 0.05; P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.automaticConvergence = true;
        P.executeOn(w.mainView);
        return "Applied with custom settings";
    }, baseTag, debugDir);
}

function runGraXpert(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GraXpert", sum, "graxpert", function(w) {
        // Check if GraXpertLib exists (Added robustness)
        if (typeof GraXpertLib === 'undefined') {
            throw new Error("GraXpertLib.jsh not found or failed to load. Check include path.");
        }
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0; // Subtraction
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = w.mainView;
        gxp.process();
        return "Applied via library call";
    }, baseTag, debugDir);
}

function autoBlackPoint(win, sum, baseTag, debugDir){
    // Note: This function may be called multiple times. The summary key "blackPoint" will be overwritten/updated.
    return handleRobustExecution(win, "Black Point Adjustment", sum, "blackPoint", function(w) {
        var img = w.mainView.image;
        var details = "";

        if (!img.readSamples) {
            // Fallback 1: Cannot read samples
            var AH_fallback = new AutoHistogram();
            AH_fallback.auto=true; AH_fallback.clipLow=0.1; AH_fallback.clipHigh=0.1;
            AH_fallback.executeOn(w.mainView);
            details = "Fallback AutoHistogram (No sample access)";
        } else if(!img.isColor || img.numberOfChannels<3){
            // Mono/NB
            var AH=new AutoHistogram();
            AH.auto=true; AH.clipLow=0.1; AH.clipHigh=0.1;
            AH.executeOn(w.mainView);
            details = "AutoHistogram (Mono/NB)";
        } else {
            // Color: Manual sampling and Levels
            var width=img.width, height=img.height, sampleSize=20, numSamples=20;
            var rs=[],gs=[],bs=[];
            for(var i=0;i<numSamples;++i){
              var x=Math.floor(Math.random()*(width-sampleSize)), y=Math.floor(Math.random()*(height-sampleSize));
              var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;
              for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }
              rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);
            }
            rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});

            var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0;
            for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; }
            R/=n; G/=n; B/=n;

            try{
              var L=new Levels();
              L.redBlack=Math.min(R*0.8,0.02);
              L.greenBlack=Math.min(G*0.9,0.03);
              L.blueBlack=Math.min(B*0.8,0.02);
              L.redWhite=0.98;
              L.greenWhite=0.97;
              L.blueWhite=0.98;
              L.executeOn(w.mainView);
              details = "Levels (Sampled)";
            }catch(e2){
            // Fallback 2: Levels failed
              var AH2=new AutoHistogram();
              AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1;
              AH2.executeOn(w.mainView);
              details = "Fallback AutoHistogram (Color)";
            }
        }
        return details;
    }, baseTag, debugDir);
}

// -------------------- AI steps --------------------
function deblur1(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Deblur V1 (Round Stars)", sum, "deblurV1", function(w) {
        // Check if BlurXTerminator exists (Added robustness)
        if (typeof BlurXTerminator === 'undefined') {
            throw new Error("BlurXTerminator process not found. Please install it.");
        }
        var P=new BlurXTerminator();
        P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
        P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
        P.executeOn(w.mainView);
        return "Round stars correction applied";
    }, baseTag, debugDir);
}

function deblur2(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Deblur V2 (Enhance)", sum, "deblurV2", function(w) {
        // Check if BlurXTerminator exists (Added robustness)
        if (typeof BlurXTerminator === 'undefined') {
            throw new Error("BlurXTerminator process not found. Please install it.");
        }
        var P=new BlurXTerminator();
        P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
        P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
        P.executeOn(w.mainView);
        return "Enhancement applied";
    }, baseTag, debugDir);
}

function denoise(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Denoising (NoiseX)", sum, "denoising", function(w) {
        // Check if NoiseXTerminator exists (Added robustness)
        if (typeof NoiseXTerminator === 'undefined') {
            throw new Error("NoiseXTerminator process not found. Please install it.");
        }
        var P=new NoiseXTerminator();
        P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
        P.executeOn(w.mainView);
        return "Denoising applied (0.90)";
    }, baseTag, debugDir);
}


// -------------------- ImageSolver + SPCC --------------------
function solveImage(win, sum, baseTag, debugDir){
  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    // Check if ImageSolver script was included (Added robustness)
    if (typeof ImageSolver === 'undefined') {
        throw new Error("ImageSolver script not found. Check include path.");
    }

    var solver = new ImageSolver();
    solver.Init(win, false);
    solver.useMetadata = true;
    solver.catalog = "GAIA DR3";
    solver.useDistortionCorrection = false;
    solver.generateErrorMaps = false;
    solver.showStars = false;
    solver.showDistortion = false;
    solver.generateDistortionMaps = false;
    solver.sensitivity = 0.1;

    if (!solver.SolveImage(win)){
      Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");
      solver.useMetadata = false;
      if (!solver.SolveImage(win)) throw new Error("Plate solution not found.");
    }
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};

    // Debug save after ImageSolver (metadata updated)
    debugSave(win, "After_ImageSolver", baseTag, debugDir);
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}

// profileSelector: "OSC" or "RGB"
function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    // Check if SPCC exists (Added robustness)
    if (typeof SpectrophotometricColorCalibration === 'undefined') {
        throw new Error("SpectrophotometricColorCalibration process not found. Please install/update it.");
    }

    var P=new SpectrophotometricColorCalibration();

    // Configuration
    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.generateGraphs=showGraphs||false; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = +2.00; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}


    P.executeOn(win.mainView);

    sum.spcc={name:"SPCC",status:"✅",details:"Applied"};

    // Debug save after SPCC
    debugSave(win, "After_SPCC", baseTag, debugDir);
    return true;
  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}


// -------------------- Stretch Functions --------------------
/*
 * STF Auto Stretch routine (Helper)
 */
function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked )
{
    if (shadowsClipping === undefined) shadowsClipping = -2.80;
    if (targetBackground === undefined) targetBackground = 0.25;
    if (rgbLinked === undefined) rgbLinked = true;

    var stf = new ScreenTransferFunction;
    var n = view.image.isColor ? 3 : 1;

    // 1. Calculate Statistics
    var median = new Vector( view.computeOrFetchProperty( "Median" ) );
    var mad = new Vector( view.computeOrFetchProperty( "MAD" ) );

    // 2. Normalize MAD (1.4826 * MAD ≈ sigma)
    mad.mul( 1.4826 );

    // 3. Calculate Stretch Parameters - STF Parameter Order: [c0, c1, m, r0, r1]
    if ( rgbLinked && n > 1)
    {
      // Linked RGB channels
      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
        if ( median.at( c ) > 0.5 )
          ++invertedChannels;

      if ( invertedChannels < n )
      {
        // Noninverted image
        var c0_sum = 0, median_sum = 0;
        for ( var c = 0; c < n; ++c )
        {
          if ( 1 + mad.at( c ) != 1 )
            c0_sum += median.at( c ) + shadowsClipping * mad.at( c );
          median_sum += median.at( c );
        }
        var c0 = Math.range( c0_sum/n, 0.0, 1.0 );
        var m = Math.mtf( targetBackground, median_sum/n - c0 );
        stf.STF = [
                        [c0, 1, m, 0, 1], [c0, 1, m, 0, 1], [c0, 1, m, 0, 1], [0, 1, 0.5, 0, 1]
                      ];
      }
      else
      {
        // Inverted image logic
        var c1_sum = 0, median_sum = 0;
        for ( var c = 0; c < n; ++c )
        {
          if ( 1 + mad.at( c ) != 1 )
            c1_sum += median.at( c ) - shadowsClipping * mad.at( c );
          median_sum += median.at( c );
        }
        var c1 = Math.range( c1_sum/n, 0.0, 1.0 );
        var m = Math.mtf( c1 - median_sum/n, targetBackground );
        stf.STF = [
                        [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, 1, 0.5, 0, 1]
                      ];
      }
    }
    else
    {
      // Unlinked channels (or Grayscale image)
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];
      for ( var c = 0; c < n; ++c )
      {
        if ( median.at( c ) < 0.5 )
        {
          var c0 = (1 + mad.at( c ) != 1) ?
                    Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
          var m = Math.mtf( targetBackground, median.at( c ) - c0 );
          A[c] = [c0, 1, m, 0, 1];
        }
        else
        {
          var c1 = (1 + mad.at( c ) != 1) ?
                    Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
          var m = Math.mtf( c1 - median.at( c ), targetBackground );
          A[c] = [0, c1, m, 0, 1];
        }
      }
      stf.STF = A;
    }
    return stf;
}

/*
 * Apply HistogramTransformation (Helper)
 */
function ApplyHistogramTransformation( view, stf )
{
    var ht = new HistogramTransformation;

    // HT Parameter Order: [c0, m, c1, r0, r1]
    var HT_IDENTITY = [0, 0.5, 1, 0, 1];
    var H = [
            HT_IDENTITY.slice(), HT_IDENTITY.slice(), HT_IDENTITY.slice(),
            HT_IDENTITY.slice(), HT_IDENTITY.slice()
            ];

    var n = view.image.isColor ? 3 : 1;

    // Helper function to map STF array to HT array
    // STF input indices: [0=c0, 1=c1, 2=m, 3=r0, 4=r1]
    // HT output indices: [0=c0, 1=m, 2=c1, 3=r0, 4=r1]
    function mapSTFtoHT(stf_channel) {
        return [
            stf_channel[0], // c0
            stf_channel[2], // m
            stf_channel[1], // c1
            stf_channel[3], // r0
            stf_channel[4]  // r1
        ];
    }

    if ( view.image.isColor )
    {
        // Check if the STF channels are linked
        var linked = true;
        for(var c = 1; c < n; ++c) {
            if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
                linked = false;
                break;
            }
        }

        if(linked) {
            // Apply linked transform to the combined RGB/K channel (Index 3)
            H[3] = mapSTFtoHT(stf.STF[0]);
        } else {
            // Apply unlinked transforms to individual channels (Indices 0, 1, 2)
            for ( var c = 0; c < n; ++c )
            {
              H[c] = mapSTFtoHT(stf.STF[c]);
            }
        }
    }
    else
    {
        // Monochrome image (Apply to K channel, index 3 in HT)
        H[3] = mapSTFtoHT(stf.STF[0]);
    }

    ht.H = H;
    return ht.executeOn( view );
}

function applyPerfectNuclearStretch(view) {
    if (!view || !view.image || view.image.isNull)
      throw new Error("No active view for nuclear stretch.");

    // Use proper linking: RGB linked for color images, unlinked for mono images
    var isColor = view.image.isColor;
    var rgbLinked = isColor; // true for RGB, false for mono
    Console.writeln("Nuclear stretch: " + (isColor ? "RGB linked" : "Mono unlinked") + " mode");

    // Calculate STF Auto Stretch parameters
    var stf = STFAutoStretch( view, -2.80, 0.25, rgbLinked );

    // Apply the calculated parameters using HistogramTransformation
    return ApplyHistogramTransformation( view, stf );
}


// -------------------- Color Enhance helpers --------------------
function applyColorEnhanceToView(view){
  // Curves: S channel enhancement
  var C = new CurvesTransformation;
  // Initialize all channels to identity
  C.R = C.G = C.B = C.K = C.A = C.L = C.a = C.b = C.c = C.H = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Rt = C.Gt = C.Bt = C.Kt = C.At = C.Lt = C.at = C.bt = C.ct = C.Ht = CurvesTransformation.prototype.AkimaSubsplines;

  // Apply S curve
  C.S = [[0.00000, 0.00000], [0.14470, 0.33247], [1.00000, 1.00000]];
  C.St = CurvesTransformation.prototype.AkimaSubsplines;
  C.executeOn(view);

  // SCNR
  var N = new SCNR;
  N.amount = 0.73;
  N.protectionMethod = SCNR.prototype.AverageNeutral;
  N.colorToRemove = SCNR.prototype.Green;
  N.preserveLightness = true;
  N.executeOn(view);
}

// -------------------- Main Processing Steps --------------------

function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");

  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();

  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");

  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);

  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}

  return {window:rgb, path:outPath, base:"RGB_Combined"};
}

// ############ V5 REVISED processOne FUNCTION ############
// V5.1 FIX: Removed ES6 default parameters for compatibility.
function processOne(win, baseTag, rootOut, config, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB, spccGraphs){
    // V5.1: Manually assign defaults if arguments are undefined
    isStackedRGB = (typeof isStackedRGB === 'undefined') ? false : isStackedRGB;
    spccGraphs = (typeof spccGraphs === 'undefined') ? false : spccGraphs;

    var sum={};
    var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
    var finalDir=rootOut+"/6_final";   ensureDir(finalDir);

    // Shorten debug folder name to avoid Windows path length limits
    var shortTag = baseTag.length > 20 ? baseTag.substring(0, 20) : baseTag;
    // Ensure the debugDir path uses sanitized names
    var debugDir = rootOut + "/debug_" + sanitizeBase(shortTag);

    // Initialize counters for the current image processing run
    debugStepCounter = 1;
    USER_ABORTED = false;

    // Ensure debug directory exists if needed (DEBUG_MODE=true)
    if (DEBUG_MODE) ensureDir(debugDir);

    // --- Initial State Save ---
    // Save the starting state of the image as Step 01. This guarantees a revert point if DEBUG_MODE=true.
    debugSave(win, "Initial_Integration", baseTag, debugDir);

    var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
    win.saveAs(integrationPath,false,false,false,false);
    sum.integrationSave={name:"Integration Save",status:"✅",details:"Integration saved"};

    closeAllWindowsExcept([win.mainView.id]);

    // Initialize path variables to null for safe cleanup
    var baseline = null;
    var stretchedPath = null;

    try {
        // --- LINEAR PROCESSING ---
        // V5 Change: Added checks for enabled steps from config.steps

        if (config.steps.abe) {
            win = finalABE(win, sum, baseTag, debugDir);
        } else {
            sum.backgroundExtraction = {name: "ABE", status: "⏭️", details: "Disabled in settings"};
        }

        // Initial Black Point
        if (config.steps.blackPoint) {
            win = autoBlackPoint(win, sum, baseTag, debugDir);
        } else {
             // Initialize the summary entry if disabled.
             sum.blackPoint = {name: "Black Point Adjustment", status: "⏭️", details: "Disabled in settings"};
        }

        if (config.steps.gradientCorrection) {
            win = runGradientCorrection(win, sum, baseTag, debugDir);
        } else {
            sum.gradientCorrection = {name: "GradientCorrection", status: "⏭️", details: "Disabled in settings"};
        }

        if (config.steps.graxpert) {
            win = runGraXpert(win, sum, baseTag, debugDir);
        } else {
            sum.graxpert = {name: "GraXpert", status: "⏭️", details: "Disabled in settings"};
        }

        // Second black point adjustment
        if (config.steps.blackPoint) {
            win = autoBlackPoint(win, sum, baseTag, debugDir);
        }

        // Step: BlurX Round Stars
        if(config.steps.deblur1){
            win = deblur1(win, sum, baseTag, debugDir);
            if(saveCfg.deblur1 && sum.deblurV1 && sum.deblurV1.status === "✅") {
                win.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
            }
        } else {
            sum.deblurV1 = {name: "Deblur V1", status: "⏭️", details: "Disabled in settings"};
        }

        // Step: SPCC (Color calibration)
        var isColor = win.mainView.image.isColor;

        if (config.steps.spcc) {
            if (isColor) {
                // Determine profile: "RGB" if combined by script, "OSC" if pre-stacked color input.
                var spccProfile = isRGBCombined ? "RGB" : "OSC";
                if (solveImage(win, sum, baseTag, debugDir)) {
                    // Use the spccGraphs setting passed into the function
                    performSPCC(win, sum, spccProfile, spccGraphs, baseTag, debugDir);
                } else {
                    sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
                }
            } else {
                sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped (mono/NB)"};
                sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (mono/NB)"};
            }
        } else {
             sum.solver = {name:"ImageSolver", status:"⏭️", details:"Disabled in settings (SPCC)"};
             sum.spcc = {name:"SPCC", status:"⏭️", details:"Disabled in settings"};
        }


        // Step: BlurX Enhanced
        if(config.steps.deblur2){
            win = deblur2(win, sum, baseTag, debugDir);
            if(saveCfg.deblur2 && sum.deblurV2 && sum.deblurV2.status === "✅") {
                win.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
            }
        } else {
            sum.deblurV2 = {name: "Deblur V2", status: "⏭️", details: "Disabled in settings"};
        }

        closeAllWindowsExcept([win.mainView.id]);

        // Save Linear Baseline
        baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
        win.saveAs(baseline,false,false,false,false);
        sum.baselineSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};
        debugSave(win, "Baseline_Linear", baseTag, debugDir);

        // --- STRETCHED PROCESSING ---
        // Step: Nuclear Screen Stretch
        stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
        if(config.steps.stretch){
            // We handle the stretch manually because the interactive review logic differs slightly (no STF preview needed).
            try {
                Console.writeln("\n=== Applying Nuclear Stretch ===");
                applyPerfectNuclearStretch(win.mainView);
                win.saveAs(stretchedPath,false,false,false,false);
                debugSave(win, "After_Nuclear_Stretch", baseTag, debugDir);
                sum.stretch={name:"Nuclear Stretch",status:"✅",details:"Applied with histogram transformation"};

                var executedStepNumber = debugStepCounter - 1;

                // Interactive review (only if INTERACTIVE_MODE=true)
                if (INTERACTIVE_MODE) {
                    win.show();
                    win.bringToFront();
                    win.zoomToFit();
                    processEvents();
                    msleep(1000);
                    var decision = askAcceptStep("Nuclear Stretch",
                        "Nuclear stretch has been applied.",
                        executedStepNumber);

                    if (decision === "skip") {
                        // Reverting a stretch means going back to the linear baseline.
                        var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                        if (restoredWin) {
                            win = restoredWin;
                            sum.stretch.status = "⏭️";
                            sum.stretch.details = "Skipped by user - reverted to linear";
                            Console.writeln("⏭️ Nuclear Stretch skipped - reverted to linear");
                            stretchedPath = null; // Image is no longer stretched
                        } else {
                            // This implies INTERACTIVE_MODE=true but revert failed (e.g., DEBUG_MODE issue or file error).
                            Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                            throw new Error(ABORT_PROCESSING_IMAGE);
                        }
                    }
                }
            } catch (e) {
                if (e.message.startsWith("ABORT_")) throw e;
                // Stretch failure handling
                Console.writeln("❌ Stretch FAILED: " + e.message);

                // Attempt revert (Will only succeed if DEBUG_MODE=true)
                Console.writeln("🔄 Attempting automatic revert because Stretch failed.");
                // Save failed state if possible
                debugSave(win, "After_Nuclear_Stretch_FAILED", baseTag, debugDir);
                var failedStepNumber = debugStepCounter - 1;
                var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

                 if (restoredWin) {
                    // Revert successful (DEBUG_MODE=true)
                    win = restoredWin;
                    sum.stretch={name:"Nuclear Stretch",status:"⚠️❌",details:"Failed and reverted to linear. Error: " + e.message};
                } else {
                    // Critical failure (DEBUG_MODE=false, or revert failed)
                    Console.criticalln("❌ CRITICAL: Stretch failed and revert failed (or Debug Mode is off). Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }else{
            sum.stretch={name:"Nuclear Stretch",status:"⏭️",details:"Disabled in settings"};
        }

        if(saveCfg.final_stretched && sum.stretch && sum.stretch.status === "✅") {
            saveAs16BitTiff(win, finalDir+"/Final_Stretched_"+baseTag);
            // Console.writeln("  Saved Final (stretched, with stars): Final_Stretched_" + baseTag + ".tif"); // Logged in saveAs16BitTiff
        }

        // Step: NoiseX (STRETCHED or LINEAR depending on previous steps)
        if(config.steps.denoise){
            win = denoise(win, sum, baseTag, debugDir);
            if (saveCfg.denoised && sum.denoising && sum.denoising.status === "✅") {
                var denoiseOutPath = finalDir+"/Denoised_"+baseTag+outputExtension;
                win.saveAs(denoiseOutPath, false, false, false, false);
            }
        } else {
            sum.denoising = {name: "Denoising (NoiseX)", status: "⏭️", details: "Disabled in settings"};
        }

        // Step: StarX
        if(config.steps.starless){
            var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
            if(needAny){
                // V5 Change: Call the revised starSeparation function (PixelMath subtraction)
                // Pass the enhance setting which was passed into this function
                win = starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir);
            }else{
                sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
            }
        } else {
            // Ensure summary entry exists even if disabled, unless already set (e.g. by "No outputs requested")
            if (!sum.starSeparation) {
                sum.starSeparation = {name: "Star Separation", status: "⏭️", details: "Disabled in settings"};
            }
        }

    } catch (e) {
        // Error handling
        if (e.message === ABORT_PROCESSING) {
            Console.writeln("🛑 Processing stopped by user (Batch Abort)");
            if (DEBUG_MODE) Console.writeln("📁 Partial results available in: " + debugDir);
            throw e; // Propagate batch abort to the main run loop
        } else if (e.message === ABORT_PROCESSING_IMAGE) {
            Console.writeln("🛑 Processing stopped for this image due to critical failure or failed revert.");
             if (DEBUG_MODE) Console.writeln("📁 Partial results available in: " + debugDir);
            // Continue to cleanup phase
        } else {
            Console.criticalln("💥 Unhandled error during processOne: " + e.message);
            throw e; // Re-throw unexpected errors
        }
    }

    // ############ Safe Cleanup ############
    // Note: The original logic used 'true' for the 'keep' parameter in removeIf, meaning these files weren't actually deleted if they existed.
    if(!saveCfg.baseline_linear && baseline) removeIf(baseline, true);
    if(!saveCfg.integration_linear && integrationPath) removeIf(integrationPath, true);

    if(!saveCfg.final_stretched && !saveCfg.denoised && stretchedPath) {
        try {
            removeIf(File.changeExtension(stretchedPath, ".tif"), true);
        } catch (e) {
            Console.writeln("Warning during cleanup (stretchedPath): " + e.message);
        }
    }


    Console.writeln("\n— Summary: "+baseTag+" —");
    var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "stretch", "denoising", "starSeparation", "integrationSave", "baselineSave"];
    for(var i=0;i<order.length;++i){
        var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
    }
    return win; // Return the final state window
}

// ############ V5 REVISED Star Separation (StarX + PixelMath Subtraction) ############
// Implements robust execution pattern and uses PixelMath for accurate star masks on stretched images.
function starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir) {
    // Initialize variables for safe cleanup
    var starsWin = null;
    var starsAndObjectWin = null; // Holds the clone (Image WITH stars)

    // Ensure we capture the original ID robustly for restoration later
    var originalWinId = (win && win.isWindow) ? win.mainView.id : "unknown_image";

    try {
        Console.writeln("\n=== Running Star Separation (StarX + PixelMath V5) ===");

        if (!win || !win.isWindow) {
             throw new Error("Invalid window passed to starSeparation.");
        }

        // Check if StarXTerminator exists (Added robustness)
        if (typeof StarXTerminator === 'undefined') {
            throw new Error("StarXTerminator process not found. Please install it.");
        }

        // Determine if the image is stretched
        // A rough check for non-linearity using the median value.
        var isStretched = win.mainView.image.median() > 0.1; // Arbitrary threshold for stretched image

        if (!isStretched && (saveCfg.stars_stretched || saveCfg.starless_stretched)) {
             Console.writeln("⚠️ NOTE: Star separation requested for stretched outputs, but image appears linear. Proceeding, but results may vary.");
        }

        // 1. Clone the input window (Image WITH stars) if needed for mask generation (Subtraction method).
        // We only need the clone if we are generating the stretched star mask via subtraction.
        if (isStretched && saveCfg.stars_stretched) {
            starsAndObjectWin = win.clone();
            // Assign a temporary, safe ID for PixelMath reference
            // Ensure it's a valid identifier
            starsAndObjectWin.mainView.id = "Temp_StarsAndObject";
        }

        // 2. Configure StarXTerminator
        var SX = new StarXTerminator();
        // CRITICAL CHANGE V5: We do NOT ask StarX to generate the star image if stretched. We calculate it via PixelMath.
        // If linear, we let StarX generate it if requested (Note: stars_linear is not currently in defaults/GUI, but handled here).
        SX.generateStarImage = !isStretched && (saveCfg.stars_stretched || (saveCfg.stars_linear !== undefined && saveCfg.stars_linear));
        SX.unscreenStars = false; // We rely on subtraction, not screening.
        SX.largeOverlap = false;

        // 3. Execute StarX on 'win' (it becomes starless)
        Console.writeln("Executing StarX (Star removal)...");
        SX.executeOn(win.mainView);
        // 'win' is now starless.

        sum.starSeparation={name:"Star Separation",status:"✅",details:"StarX applied (Starless generated)"};
        debugSave(win, "After_StarSeparation_Starless", baseTag, debugDir);
        var executedStepNumber = debugStepCounter - 1;

        // --- Interactive Review (Starless) ---
        var separationAccepted = true;
        if (INTERACTIVE_MODE) {
            win.show();
            win.bringToFront();
            win.zoomToFit();
            processEvents();
            msleep(1000);
            var decision = askAcceptStep("StarX Separation (Starless)",
                "Review the Starless image. Accept to proceed with Star Mask generation (if requested).",
                executedStepNumber);

             if (decision === "skip") {
                 separationAccepted = false;
                 // Revert 'win' (starless) back to the state before StarX.
                 var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);

                 if (restoredWin) {
                    win = restoredWin;
                    // Ensure the restored window has the original ID
                    if (win.mainView.id !== originalWinId) {
                        try { win.mainView.id = originalWinId; } catch(_) { Console.writeln("Warning: Could not restore original view ID after revert."); }
                    }
                    sum.starSeparation.status = "⏭️";
                    sum.starSeparation.details = "Skipped by user (Interactive)";
                } else {
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    // Cleanup cloned window before throwing error
                    try{ if (starsAndObjectWin) starsAndObjectWin.forceClose(); }catch(_){}
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
             }
        }

        // 4. Star Mask Generation (PixelMath if Stretched, or find StarX output if Linear)

        if (separationAccepted) {
            if (isStretched && starsAndObjectWin) {
                // --- PixelMath Subtraction (Stretched) ---
                Console.writeln("Generating Star Mask via PixelMath subtraction (Stretched - Starless)...");
                try {
                    // PixelMath: Stars = ImageWithStars - Starless
                    // We execute on ImageWithStars (starsAndObjectWin) in place.
                    // Expression: $T - Starless_ID

                    var currentStarlessID = win.mainView.id;
                    // We must ensure the ID is safe for PixelMath (valid identifier)
                    var safe_starless_ID = sanitizeBase(currentStarlessID);

                    // Defensive check: ensure the sanitized ID is not the same as the target ID
                    if (safe_starless_ID === starsAndObjectWin.mainView.id) {
                        safe_starless_ID += "_starless";
                    }

                    // Check if renaming is necessary
                    // We compare against originalWinId because that's what the ID should be if it hasn't been modified yet.
                    // Also check if the current ID is unsafe using a regex test (though sanitizeBase should handle this).
                    if (safe_starless_ID !== currentStarlessID || win.mainView.id !== originalWinId || !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(win.mainView.id)) {
                         // If the ID is unsafe or changed from the original, rename the view temporarily.
                         Console.writeln("Temporarily renaming starless view for PixelMath: " + win.mainView.id + " -> " + safe_starless_ID);
                         // Try/Catch in case the ID is already taken somehow
                         try {
                            win.mainView.id = safe_starless_ID;
                            currentStarlessID = safe_starless_ID;
                         } catch (e) {
                            Console.writeln("❌ Failed to rename view for PixelMath: " + e.message);
                            throw new Error("View ID conflict during PixelMath preparation.");
                         }
                    }

                    var PM = new PixelMath;
                    // Expression: Target (Original) - Starless
                    PM.expression = "$T - " + currentStarlessID;
                    PM.useSingleExpression = true;
                    PM.generateOutput = true;
                    PM.createNewImage = false; // Modify in place (Efficient reuse of the clone)
                    PM.rescale = false;
                    // Crucial: Clip negative values to prevent black holes in the mask
                    PM.truncate = true;
                    PM.truncateLower = 0;
                    PM.truncateUpper = 1;

                    // Execute on the clone (Original image)
                    if (PM.executeOn(starsAndObjectWin.mainView)) {
                        // starsAndObjectWin is now the star mask
                        starsWin = starsAndObjectWin;
                        // Assign the final ID to the mask
                        starsWin.mainView.id = "StarsMask_" + sanitizeBase(baseTag);
                        Console.writeln("✅ Star Mask generated successfully via subtraction.");
                        sum.starSeparation.details += " | Method: Subtraction Mask.";
                    } else {
                        Console.writeln("❌ PixelMath execution failed.");
                    }

                    // Restore win ID if it was changed
                    if (win.mainView.id !== originalWinId) {
                         Console.writeln("Restoring starless view ID: " + originalWinId);
                         try { win.mainView.id = originalWinId; } catch(_) { Console.writeln("Warning: Could not restore original view ID after PixelMath."); }
                    }

                } catch (pmError) {
                    Console.writeln("❌ Error during PixelMath execution: " + pmError.message);
                     // Restore win ID if it was changed even if PM failed
                    if (win && win.isWindow && win.mainView.id !== originalWinId) {
                         try { win.mainView.id = originalWinId; } catch(_) {}
                    }
                }
            } else if (!isStretched) {
                // --- Find StarX output (Linear) ---
                Console.writeln("Image is Linear. Locating StarX generated stars (if any).");
                 var wins = ImageWindow.windows;
                for (var i = 0; i < wins.length; ++i) {
                    // Look for new windows that contain "stars" in their ID (StarX default behavior)
                    // V5.1: Use compatibility safe check for id property
                    var viewId = wins[i].mainView.id;
                    if (wins[i] !== win && /stars/i.test(viewId || "")) {
                        starsWin = wins[i];
                        break;
                    }
                }
                if (starsWin) {
                    sum.starSeparation.details += " | Method: Standard (Linear).";
                }
            }
        }


        // --- Save Outputs (only if accepted) ---
        if (separationAccepted) {
             // Save Starless (win)
             if(saveCfg.starless_linear && !isStretched) {
                var starlessLinearPath = finalDir+"/Starless_Linear_"+baseTag+outputExtension;
                win.saveAs(starlessLinearPath,false,false,false,false);
                Console.writeln("  Saved Starless (linear): " + File.extractName(starlessLinearPath));
            }
            if(saveCfg.starless_stretched && isStretched) {
                saveAs16BitTiff(win, finalDir+"/Starless_Stretched_"+baseTag);
                // Logged in saveAs16BitTiff
            }

            // Save Stars (starsWin)
            if(starsWin){
                if(saveCfg.stars_stretched && isStretched) {
                     // Enhance star colors if requested (this applies to the generated mask)
                    if(isColor && enhanceRGBStarsStretched) {
                        Console.writeln("Applying color enhancement to stars mask...");
                        applyColorEnhanceToView(starsWin.mainView);
                    }
                    saveAs16BitTiff(starsWin, finalDir+"/Stars_Stretched_"+baseTag);
                    // Logged in saveAs16BitTiff
                }
                // Note: Linear stars saving (saveCfg.stars_linear) could be added here if needed.
            }
        }

        // Cleanup temporary windows
        try{
            if (starsWin) {
                starsWin.forceClose();
            }
            // Only close starsAndObjectWin if it wasn't converted to starsWin (e.g., if separation was skipped or PM failed)
            // This relies on starsWin being set to starsAndObjectWin upon successful PM execution.
            else if (starsAndObjectWin) {
                starsAndObjectWin.forceClose();
            }
        } catch(_){}

        return win; // Return the (potentially reverted or starless) window

    } catch(e) {
        // --- Error handling and auto-revert ---

        // Ensure cleanup of potential clones in case of early failure (e.g. StarX execution fail)
        try{ if (starsAndObjectWin) starsAndObjectWin.forceClose(); }catch(_){}

        if (e.message.startsWith("ABORT_")) throw e;

        // Automatic Revert on Failure
        Console.writeln("❌ Star Separation FAILED: " + e.message);
        if (win && win.isWindow) {
            debugSave(win, "After_StarSeparation_FAILED", baseTag, debugDir);
        }

        // Determine the step number associated with the failure.
        var failedStepNumber = debugStepCounter - 1;

        Console.writeln("🔄 Attempting automatic revert because Star Separation failed.");
        // Attempt revert (Will only succeed if DEBUG_MODE=true)
        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            win = restoredWin;
             // If revert succeeds, ensure ID is restored
            if (win.mainView.id !== originalWinId) {
                try { win.mainView.id = originalWinId; } catch(_) {}
            }
            sum.starSeparation = {name: "Star Separation", status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
            return win;
        } else {
             // Critical failure (DEBUG_MODE=false, or revert failed)
            Console.criticalln("❌ CRITICAL: Star Separation failed, and revert failed (or Debug Mode is off). Aborting image.");
            throw new Error(ABORT_PROCESSING_IMAGE);
        }
    }
}

// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog;
  // V5.1 Change: Updated Title
  dlg.windowTitle="Post-Integration Pipeline (RGB & Mono) - V5.1 (Compatibility Fix)";
  dlg.sizer=new VerticalSizer;
  dlg.sizer.margin=10;
  dlg.sizer.spacing=8;

  var head=new Label(dlg);
  head.useRichText=true;
  head.text="<b>Utah Masterclass — Post-Integration Pipeline (V5.1)</b>";
  head.textAlignment=TextAlign_Center;
  dlg.sizer.add(head);

  // --- General Group (Input/Output/Modes) ---
  var gTop=new GroupBox(dlg);
  gTop.title="General Settings";
  gTop.sizer=new VerticalSizer;
  gTop.sizer.margin=8;
  gTop.sizer.spacing=6;

  // Input Directory
  var rowIn=new HorizontalSizer;
  rowIn.spacing=6;
  var labelIn=new Label(dlg);
  labelIn.text="Input Directory:";
  labelIn.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var editIn=new Edit(dlg);
  editIn.readOnly=true;
  editIn.minWidth=560;
  editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg);
  btnIn.text="Browse...";
  btnIn.icon=dlg.scaledResource(":/icons/select-file.png");
  btnIn.onClick=function(){
    var d=new GetDirectoryDialog;
    d.caption="Select Input Directory";
    d.initialDirectory=state.inputDir||"";
    if(d.execute()){
      state.inputDir=fwd(d.directory).replace(/\/$/,"");
      editIn.text=state.inputDir;
    }
  };
  rowIn.add(labelIn);
  rowIn.add(editIn,100);
  rowIn.add(btnIn);
  gTop.sizer.add(rowIn);

  // Output Directory
  var rowOut=new HorizontalSizer;
  rowOut.spacing=6;
  var labelOut=new Label(dlg);
  labelOut.text="Output Directory:";
  labelOut.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var editOut=new Edit(dlg);
  editOut.readOnly=true;
  editOut.minWidth=560;
  editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg);
  btnOut.text="Browse...";
  btnOut.icon=dlg.scaledResource(":/icons/select-file.png");
  btnOut.onClick=function(){
    var d=new GetDirectoryDialog;
    d.caption="Select Output Base Directory";
    d.initialDirectory=state.outputDir||"";
    if(d.execute()){
      state.outputDir=fwd(d.directory).replace(/\/$/,"");
      editOut.text=state.outputDir;
    }
  };
  rowOut.add(labelOut);
  rowOut.add(editOut,100);
  rowOut.add(btnOut);
  gTop.sizer.add(rowOut);


  // V5: Add Mode controls (Interactive/Debug)
  var rowModes = new HorizontalSizer;
  rowModes.spacing = 12;
  rowModes.margin = 6;

  var cbInteractive = new CheckBox(dlg);
  cbInteractive.text = "Interactive Mode (Pause after each step for review/revert)";
  cbInteractive.checked = INTERACTIVE_MODE; // Use the global variable

  var cbDebug = new CheckBox(dlg);
  cbDebug.text = "Debug Mode (Save intermediate XISF files)";
  cbDebug.checked = DEBUG_MODE; // Use the global variable
  cbDebug.toolTip = "Saves intermediate files after every step. Required for Interactive Mode and error recovery.";

  // Logic to ensure Debug is enabled if Interactive is enabled
  cbInteractive.onCheck = function(checked) {
      if (checked) {
          cbDebug.checked = true;
          cbDebug.enabled = false; // Cannot disable debug if interactive is on
      } else {
          cbDebug.enabled = true;
      }
  };
  // Initial state synchronization
  if (cbInteractive.checked) {
      cbDebug.checked = true;
      cbDebug.enabled = false;
  }

  rowModes.add(cbInteractive);
  rowModes.add(cbDebug);
  rowModes.addStretch();
  gTop.sizer.add(rowModes);

  dlg.sizer.add(gTop);


  // V5 Change: New GroupBox for Step Control
  var gProc=new GroupBox(dlg);
  gProc.title="Processing Step Control";
  gProc.sizer=new VerticalSizer;
  gProc.sizer.margin=8;
  gProc.sizer.spacing=6;

  // --- Row 1: Background & Gradients ---
  var rowP1=new HorizontalSizer;
  rowP1.spacing=12;

  var cbABE=new CheckBox(dlg);
  cbABE.text="ABE (Extraction)";
  cbABE.checked=state.steps.abe;
  cbABE.toolTip="Automatic Background Extraction.";

  var cbGC=new CheckBox(dlg);
  cbGC.text="GradientCorrection";
  cbGC.checked=state.steps.gradientCorrection;
  cbGC.toolTip="PixInsight GradientCorrection process.";

  var cbGraX=new CheckBox(dlg);
  cbGraX.text="GraXpert";
  cbGraX.checked=state.steps.graxpert;
  cbGraX.toolTip="GraXpert background extraction.";

  var cbBP=new CheckBox(dlg);
  cbBP.text="Black Point Adj.";
  cbBP.checked=state.steps.blackPoint;
  cbBP.toolTip="Automatic black point adjustment (applied twice).";

  rowP1.add(cbABE);
  rowP1.add(cbGC);
  rowP1.add(cbGraX);
  rowP1.add(cbBP);
  rowP1.addStretch();
  gProc.sizer.add(rowP1);

  // --- Row 2: Calibration & Deblur ---
  var rowP2=new HorizontalSizer;
  rowP2.spacing=12;

  var cbSPCC=new CheckBox(dlg);
  cbSPCC.text="SPCC (Solver & Color Cal)";
  cbSPCC.checked=state.steps.spcc;
  cbSPCC.toolTip="ImageSolver followed by SpectrophotometricColorCalibration (Color images only).";

  var cbD1=new CheckBox(dlg);
  cbD1.text="✨ Deblur V1 (Round Stars)";
  cbD1.checked=state.steps.deblur1;

  var cbD2=new CheckBox(dlg);
  cbD2.text="✨ Deblur V2 (Enhance)";
  cbD2.checked=state.steps.deblur2;

  rowP2.add(cbSPCC);
  rowP2.add(cbD1);
  rowP2.add(cbD2);
  rowP2.addStretch();
  gProc.sizer.add(rowP2);

  // --- Row 3: Stretch & Final AI ---
  var rowP3=new HorizontalSizer;
  rowP3.spacing=12;

  var cbST=new CheckBox(dlg);
  cbST.text="🌀 Stretch (AutoSTF→HT)";
  cbST.checked=state.steps.stretch;

  var cbDN=new CheckBox(dlg);
  cbDN.text="🧼 Denoise (NoiseX)";
  cbDN.checked=state.steps.denoise;

  var cbSL=new CheckBox(dlg);
  cbSL.text="✂️ Star Separation (StarX+PM)";
  cbSL.checked=state.steps.starless;
  cbSL.toolTip="Star separation using StarXTerminator. Uses PixelMath subtraction for stretched images.";

  rowP3.add(cbST);
  rowP3.add(cbDN);
  rowP3.add(cbSL);
  rowP3.addStretch();
  gProc.sizer.add(rowP3);

  // --- Row 4: Advanced Options ---
  var rowP4=new HorizontalSizer;
  rowP4.spacing=12;
  var cbSPCCGraph=new CheckBox(dlg);
  cbSPCCGraph.text="📊 Show SPCC Graphs (Advanced)";
  cbSPCCGraph.checked=state.spccGraphs;
  rowP4.add(cbSPCCGraph);
  rowP4.addStretch();
  gProc.sizer.add(rowP4);

  dlg.sizer.add(gProc);


  // --- RGB Outputs Group ---
  var gRGB=new GroupBox(dlg);
  gRGB.title="RGB Outputs";
  gRGB.sizer=new VerticalSizer;
  gRGB.sizer.margin=8;
  gRGB.sizer.spacing=6;

  var rowRGBMaster=new HorizontalSizer;
  rowRGBMaster.spacing=10;
  var chkProcRGB=new CheckBox(gRGB);
  chkProcRGB.text="✅ Process RGB / Color";
  chkProcRGB.checked=state.processRGB;
  chkProcRGB.toolTip="Enable or disable all RGB/Color processing and outputs.";
  rowRGBMaster.add(chkProcRGB);
  rowRGBMaster.addStretch();
  gRGB.sizer.add(rowRGBMaster);

  var cbCombine=new CheckBox(dlg);
  cbCombine.text="Auto-detect & combine R+G+B masters";
  cbCombine.checked=state.combineRGB;
  gRGB.sizer.add(cbCombine);

  var rowR1=new HorizontalSizer;
  rowR1.spacing=10;
  var lR1=new Label(dlg);
  lR1.text="🏆 Finals:";
  lR1.minWidth=120;
  var rFinalS=new CheckBox(dlg);
  rFinalS.text="Final (stretched, with stars) (TIFF)";
  rFinalS.checked=state.save.rgb.final_stretched;
  var rFinalL=new CheckBox(dlg);
  rFinalL.text="Final (linear, with stars)";
  rFinalL.checked=state.save.rgb.final_linear;
  rowR1.add(lR1);
  rowR1.add(rFinalS);
  rowR1.add(rFinalL);
  rowR1.addStretch();
  gRGB.sizer.add(rowR1);

  var rowR2=new HorizontalSizer;
  rowR2.spacing=10;
  var lR2=new Label(dlg);
  lR2.text="🎭 Masks & Starless:";
  lR2.minWidth=120;
  var rStarsS=new CheckBox(dlg);
  rStarsS.text="Stars (stretched mask) (TIFF)";
  rStarsS.checked=state.save.rgb.stars_stretched;
  var rgbEnh=new CheckBox(dlg);
  rgbEnh.text="✨ Enhance with rich star colors";
  rgbEnh.checked=state.colorEnhanceRGBStarsStretched;
  var rSLessS=new CheckBox(dlg);
  rSLessS.text="Starless (stretched) (TIFF)";
  rSLessS.checked=state.save.rgb.starless_stretched;
  var rSLessL=new CheckBox(dlg);
  rSLessL.text="Starless (linear)";
  rSLessL.checked=state.save.rgb.starless_linear;
  rowR2.add(lR2);
  rowR2.add(rStarsS);
  rowR2.add(rgbEnh);
  rowR2.add(rSLessS);
  rowR2.add(rSLessL);
  rowR2.addStretch();
  gRGB.sizer.add(rowR2);
  dlg.sizer.add(gRGB);

  // --- Monochrome Outputs Group ---
  var gM=new GroupBox(dlg);
  gM.title="Monochrome / Narrowband Outputs";
  gM.sizer=new VerticalSizer;
  gM.sizer.margin=8;
  gM.sizer.spacing=6;

  var rowMonoMaster=new HorizontalSizer;
  rowMonoMaster.spacing=10;
  var chkProcMono=new CheckBox(gM);
  chkProcMono.text="✅ Process Monochrome / Singles";
  chkProcMono.checked=state.processMonochrome;
  chkProcMono.toolTip="Enable or disable all Monochrome/single-channel processing and outputs.";
  rowMonoMaster.add(chkProcMono);
  rowMonoMaster.addStretch();
  gM.sizer.add(rowMonoMaster);

  var rowM1=new HorizontalSizer;
  rowM1.spacing=10;
  var lM1=new Label(dlg);
  lM1.text="🏆 Finals:";
  lM1.minWidth=120;
  var mFinalS=new CheckBox(dlg);
  mFinalS.text="Final (stretched, with stars) (TIFF)";
  mFinalS.checked=state.save.mono.final_stretched;
  var mFinalL=new CheckBox(dlg);
  mFinalL.text="Final (linear, with stars)";
  mFinalL.checked=state.save.mono.final_linear;
  rowM1.add(lM1);
  rowM1.add(mFinalS);
  rowM1.add(mFinalL);
  rowM1.addStretch();
  gM.sizer.add(rowM1);

  var rowM2=new HorizontalSizer;
  rowM2.spacing=10;
  var lM2=new Label(dlg);
  lM2.text="🎭 Starless & Masks:";
  lM2.minWidth=120;
  var mStarsS=new CheckBox(dlg);
  mStarsS.text="Stars (stretched mask) (TIFF)";
  mStarsS.checked=state.save.mono.stars_stretched;
  var mSLessS=new CheckBox(dlg);
  mSLessS.text="Starless (stretched) (TIFF)";
  mSLessS.checked=state.save.mono.starless_stretched;
  var mSLessL=new CheckBox(dlg);
  mSLessL.text="Starless (linear)";
  mSLessL.checked=state.save.mono.starless_linear;
  rowM2.add(lM2);
  rowM2.add(mStarsS);
  rowM2.add(mSLessS);
  rowM2.add(mSLessL);
  rowM2.addStretch();
  gM.sizer.add(rowM2);
  dlg.sizer.add(gM);


  // GUI Logic for enabling/disabling sections
  var rgbControls=[cbCombine,rFinalS,rFinalL,rStarsS,rgbEnh,rSLessS,rSLessL];
  var monoControls=[mFinalS,mFinalL,mStarsS,mSLessS,mSLessL];
  function toggleSection(enabled,controls){
    for(var i=0;i<controls.length;++i)controls[i].enabled=enabled;
  }
  chkProcRGB.onCheck=function(checked){
    toggleSection(checked,rgbControls);
  };
  chkProcMono.onCheck=function(checked){
    toggleSection(checked,monoControls);
  };
  toggleSection(chkProcRGB.checked,rgbControls);
  toggleSection(chkProcMono.checked,monoControls);

  // --- Button Row ---
  var rowBtn=new HorizontalSizer;
  rowBtn.spacing=8;
  rowBtn.addStretch();
  var bStart=new PushButton(dlg);
  bStart.text="Start";
  bStart.icon=dlg.scaledResource(":/icons/ok.png");
  bStart.defaultButton=true;
  var bCancel=new PushButton(dlg);
  bCancel.text="Cancel";
  bCancel.icon=dlg.scaledResource(":/icons/close.png");
  rowBtn.add(bStart);
  rowBtn.add(bCancel);
  dlg.sizer.add(rowBtn);

  bCancel.onClick=function(){dlg.cancel();};

  bStart.onClick=function(){
    if(!state.inputDir||!File.directoryExists(state.inputDir)){
      (new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();
      return;
    }
    if(!state.outputDir||!File.directoryExists(state.outputDir)){
      (new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();
      return;
    }

    // V5: Update Global Modes and Save Settings Persistently
    INTERACTIVE_MODE = cbInteractive.checked;
    DEBUG_MODE = cbDebug.checked;
    Settings.write(settingsKey+"/InteractiveMode", DataType_Boolean, INTERACTIVE_MODE);
    Settings.write(settingsKey+"/DebugMode", DataType_Boolean, DEBUG_MODE);

    // Update state from GUI elements
    state.processRGB=chkProcRGB.checked;
    state.processMonochrome=chkProcMono.checked;
    state.combineRGB=cbCombine.checked;

    // V5 Change: Update state from new step control checkboxes
    state.steps.abe = cbABE.checked;
    state.steps.gradientCorrection = cbGC.checked;
    state.steps.graxpert = cbGraX.checked;
    state.steps.blackPoint = cbBP.checked;
    state.steps.spcc = cbSPCC.checked;
    state.steps.deblur1 = cbD1.checked;
    state.steps.deblur2 = cbD2.checked;
    state.steps.stretch = cbST.checked;
    state.steps.denoise = cbDN.checked;
    state.steps.starless = cbSL.checked;

    state.colorEnhanceRGBStarsStretched=rgbEnh.checked;
    state.spccGraphs=cbSPCCGraph.checked;

    // Update save configurations
    state.save.rgb={
      final_stretched:rFinalS.checked,
      final_linear:rFinalL.checked,
      stars_stretched:rStarsS.checked,
      starless_stretched:rSLessS.checked,
      starless_linear:rSLessL.checked,
      integration_linear:false,
      baseline_linear:false,
      deblur1:false,
      deblur2:false,
      denoised:false
    };
    state.save.mono={
      final_stretched:mFinalS.checked,
      final_linear:mFinalL.checked,
      stars_stretched:mStarsS.checked,
      starless_stretched:mSLessS.checked,
      starless_linear:mSLessL.checked,
      integration_linear:false,
      baseline_linear:false,
      deblur1:false,
      deblur2:false,
      denoised:false
    };
    dlg.ok();
  };
  return dlg.execute();
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — V5.1 ===");
  // Note: settingsKey is defined in Globals section.

  // V5 Change: Initialize state with the new structure
  var state={
    // V5.1: Use || for compatibility when loading directories
    inputDir:Settings.read(settingsKey+"/InputDir",DataType_String)||defaults.inputDir,
    outputDir:Settings.read(settingsKey+"/OutputDir",DataType_String)||defaults.outputDir,
    processRGB:defaults.processRGB,
    processMonochrome:defaults.processMonochrome,
    combineRGB:defaults.combineRGB,
    steps: defaults.steps, // Initialize consolidated steps
    colorEnhanceRGBStarsStretched:defaults.colorEnhanceRGBStarsStretched,
    spccGraphs:defaults.spccGraphs,
    save:{
      rgb:defaults.save.rgb,
      mono:defaults.save.mono
    }
  };

  if(!showGUI(state)){
    Console.writeln("Cancelled.");
    return;
  }

  // Save directory settings for next run
  Settings.write(settingsKey+"/InputDir",DataType_String,state.inputDir);
  Settings.write(settingsKey+"/OutputDir",DataType_String,state.outputDir);

  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);
  Console.writeln("Mode: " + (INTERACTIVE_MODE ? "Interactive" : "Automated") + (DEBUG_MODE ? " (Debug ON)" : " (Debug OFF)"));


  // Setup output folder structure
  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder: "+root);
  ensureDir(root+"/5_stacked");
  ensureDir(root+"/6_final");

  try{
    var plan=buildWorkPlan(state.inputDir,state.combineRGB);
    var doRGB=state.processRGB&&plan.doRGB&&shouldProcessConfig(state.save.rgb);

    if(!state.processRGB&&!state.processMonochrome){
      Console.writeln("Both RGB and Monochrome processing are disabled. Exiting.");
      return;
    }

    // Process RGB Combination
    if(doRGB){
      Console.writeln("\n→ Building RGB from:");
      Console.writeln("    R: "+File.extractName(plan.r));
      Console.writeln("    G: "+File.extractName(plan.g));
      Console.writeln("    B: "+File.extractName(plan.b));
      var combo=combineRGB(plan.r,plan.g,plan.b,root);
      // V5 Change: Pass the whole 'state' object as the configuration, and individual settings where needed by the function signature.
      var finalWin = processOne(combo.window,combo.base,root,state,state.save.rgb,true,state.colorEnhanceRGBStarsStretched,false,state.spccGraphs);
      try{if (finalWin && finalWin.isWindow) finalWin.forceClose();}catch(_){}
    }else{
      Console.writeln("RGB Combination: skipped (no R+G+B set found or option disabled).");
    }

    // Process Singles (Mono/NB/Pre-stacked Color)
    if(plan.singles.length>0){
      Console.writeln("\n→ Processing mono/narrowband/color singles: "+plan.singles.length);
      for(var i=0;i<plan.singles.length;++i){
        var singleInfo=plan.singles[i];
        var p=singleInfo.path;
        var tag=singleInfo.tag;
        var isStackedRGB=singleInfo.isStackedRGB;

        Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");

        var w=ImageWindow.open(p);
        if(w.length===0){
          Console.writeln("  ⚠️ Could not open, skipping.");
          continue;
        }

        var win=w[0],base=tag+"_"+sanitizeBase(File.extractName(p));
        var isColor=win.mainView.image.isColor;
        var saveCfg=isColor?state.save.rgb:state.save.mono;
        var procEnabled=isColor?state.processRGB:state.processMonochrome;

        if(!procEnabled||!shouldProcessConfig(saveCfg)){
          Console.writeln("  ⏭️ Skipped (processing or outputs disabled for this image type).");
          try{win.forceClose();}catch(_){}
          continue;
        }

        // V5 Change: Pass the whole 'state' object as the configuration, and individual settings where needed.
        var finalWinSingle = processOne(win,base,root,state,saveCfg,false,isColor&&state.colorEnhanceRGBStarsStretched,isStackedRGB,state.spccGraphs);
        try{if (finalWinSingle && finalWinSingle.isWindow) finalWinSingle.forceClose();}catch(_){}
        closeAllWindowsExcept(null);
      }
    }else{
      Console.writeln("\nNo single channel (Mono/NB/Color) masters found to process.");
    }

    Console.writeln("\n=== Done. Output: "+root+" ===");

  }catch(err){
    if (err.message === ABORT_PROCESSING) {
        Console.writeln("\n=== Batch Processing Aborted by User ===");
    } else {
        Console.criticalln("Error: "+err.message);
        // Show error dialog regardless of DEBUG_MODE if the script crashes unexpectedly.
        (new MessageBox(err.message,"Script Aborted",StdIcon_Error,StdButton_Ok)).execute();

        // If not in debug mode, re-throw to ensure PixInsight registers the failure.
        if (!DEBUG_MODE) {
            throw err;
        }
    }
  }
}

run();
})(); // IIFE
