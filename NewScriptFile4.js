/*
 * Post-Integration Pipeline — RGB & Monochrome
 * WORK6 VERSION - STARTUP DIALOG FIX & ROBUSTNESS
 * - Fixes script abortion caused by ImageSolver dialog appearing at startup (using #define IS_INCLUDED_SCRIPT).
 * - Incorporates V5 fixes: ABE compatibility, StarX crash fix (win.clone), filename truncation (MAX_PATH errors), GradientCorrection robustness.
 * - Incorporates V4 fixes: Revert mechanism (forceClose bug), safe cleanup.
 */

// FIX: Define IS_INCLUDED_SCRIPT to prevent ImageSolver dialog from auto-executing and aborting the script on cancel.
#define IS_INCLUDED_SCRIPT

#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- Ensure this path is correct for your installation ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Configuration & Globals --------------------
var DEBUG_MODE = true;
var INTERACTIVE_MODE = true;  // Enable interactive step-by-step review
var debugStepCounter = 1;
var USER_ABORTED = false;

// Custom Error Types for Flow Control
const ABORT_PROCESSING = "ABORT_PROCESSING"; // Stop the entire batch
const ABORT_PROCESSING_IMAGE = "ABORT_PROCESSING_IMAGE"; // Stop current image, continue batch

var outputExtension = ".xisf";
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

// -------------------- Utility Functions --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }

// V5 FIX: Shortened timestamp
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  // Shortened timestamp format
  var ts=d.getFullYear()+p2(d.getMonth()+1)+p2(d.getDate())+"T"+p2(d.getHours())+p2(d.getMinutes());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Proc_"+ts;
  ensureDir(out); return out;
}

function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    // Ensure window is valid before attempting to close
    if(!keep){ try{ if (wins[i] && wins[i].isWindow) wins[i].forceClose(); }catch(_){ } }
  }
}

// V5 FIX: Truncate long filenames to prevent MAX_PATH errors
function sanitizeBase(name){
    var b=name.replace(/[^\w\-]+/g,"_");

    // Maximum length for the base name to keep paths safe
    var MAX_LEN = 50;

    if (b.length > MAX_LEN) {
        // Truncate and add a short hash to maintain uniqueness
        var hash = 0;
        for (var i = 0; i < name.length; i++) {
            hash = (hash << 5) - hash + name.charCodeAt(i);
            hash = hash & hash; // Convert to 32bit integer
        }
        // Keep start and end parts of the name, replace middle with hash
        var hashStr = Math.abs(hash).toString(36).substring(0, 8);
        var startLen = Math.floor((MAX_LEN - hashStr.length - 2) / 2);
        var endLen = MAX_LEN - hashStr.length - 2 - startLen;

        b = b.substring(0, startLen) + "_" + hashStr + "_" + b.substring(b.length - endLen);
        Console.writeln("NOTE: Input filename too long. Shortened tag to: " + b);
    }
    return b.length ? b : "image";
}

function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}
function saveAs16BitTiff(win, path) {
    if (!win || !win.isWindow) return;
    var tifPath = File.changeExtension(path, ".tif");
    try {
        win.saveAs(tifPath, false, false, true, false);
        Console.writeln("  Saved 16-bit TIFF: ", File.extractName(tifPath));
    } catch (e) {
        Console.criticalln("❌ ERROR saving TIFF: " + e.message + " Path: " + tifPath);
    }
}

// -------------------- Debug and Revert Functions --------------------

// V5 FIX: Shortened filenames in debugSave
function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }

    // Create subfolder for this step
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;

    // Create the step subfolder
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        Console.writeln("Check path length and permissions. Path length: " + stepFolder.length);
        stepFolder = debugDir; // Fallback to main debug dir
    }

    // Filename shortened for MAX_PATH safety.
    let baseFileName = counterStr + "_dbg_" + sanitizedStepName;

    // Save as XISF (critical for revert)
    let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
    try {
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + File.extractName(xisfPath));
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
        Console.writeln("Full Path: " + xisfPath + " (Length: " + xisfPath.length + ")");
        if (DEBUG_MODE) {
            Console.criticalln("CRITICAL: Debug save failed. Revert functionality may be compromised.");
        }
    }

    debugStepCounter++;
}

// V5 FIX: Updated revert logic for robust file finding
function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
    Console.writeln("🔄 REVERT: Looking for previous step before step " + currentStepNumber);

    if (currentStepNumber <= 1) {
        Console.writeln("⚠️ Cannot revert - already at the first step.");
        return null;
    }

    var previousStepNumber = currentStepNumber - 1;
    var previousStepStr = (previousStepNumber < 10 ? "0" : "") + previousStepNumber;
    var revertFile = null;

    Console.writeln("🔍 Searching in: " + debugDir);

    try {
        var ff = new FileFind();
        // Search for directories starting with the previous step number
        if (ff.begin(debugDir + "/" + previousStepStr + "_*")) {
            do {
                if (ff.isDirectory) {
                    var folderPath = debugDir + "/" + ff.name;

                    // Search within the found directory for the XISF file
                    var ffInner = new FileFind();
                    if (ffInner.begin(folderPath + "/*.xisf")) {
                        do {
                            // Take the first XISF file found in the directory
                            revertFile = folderPath + "/" + ffInner.name;
                            Console.writeln("✅ Found revert file: " + File.extractName(revertFile));
                            break;
                        } while (ffInner.next());
                    }
                    ffInner.end();

                    if (revertFile) break; // Exit outer loop if file found
                }
            } while (ff.next());
        }
        ff.end();
    } catch (e) {
        Console.writeln("❌ Error while searching for revert file: " + e.message);
    }

    if (!revertFile) {
        Console.writeln("❌ REVERT FAILED: Could not find the output file from step " + previousStepNumber);
        return null;
    }

    // --- Logic to open the file and handle window replacement ---
    try {
        Console.writeln("🔄 REVERTING: Loading " + File.extractName(revertFile));
        var currentId = "restored_view"; // Default ID

        // Check if the window is still valid before trying to close it
        if (win && win instanceof ImageWindow && win.isWindow) {
            currentId = win.mainView.id;
            // Ensure forceClose is still a function (V4 Fix)
            if (typeof win.forceClose === 'function') {
                win.forceClose();
            } else {
                Console.criticalln("❌ REVERT FAILED: win.forceClose() method is missing!");
                return null;
            }
        } else {
            Console.writeln("Note: Current window was already closed or invalid.");
        }

        var windows = ImageWindow.open(revertFile);
        if (windows.length > 0) {
            var restoredWin = windows[0];
            restoredWin.mainView.id = currentId; // Preserve the view ID if possible
            restoredWin.show();
            restoredWin.bringToFront();
            Console.writeln("✅ REVERT SUCCESSFUL");
            return restoredWin;
        } else {
            Console.writeln("❌ REVERT FAILED: Could not open file " + revertFile);
            return null;
        }
    } catch (e) {
        Console.writeln("❌ REVERT ERROR during file load: " + e.message);
        return null;
    }
}

// -------------------- Interactive Review Functions --------------------

// V4 FIX: Removed the line that overwrote the forceClose method
function showStretchedPreview(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    Console.writeln("📺 Applying auto-stretch for visual review...");

    try {
        win.show();
        win.bringToFront();
        win.zoomToFit();

        var view = win.mainView;

        // Calculate proper auto-stretch parameters
        var median = view.computeOrFetchProperty("Median");
        var mad = view.computeOrFetchProperty("MAD");

        var stf = new ScreenTransferFunction;
        var n = view.image.isColor ? 3 : 1;

        var stfParams = [
            [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
        ];

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826; // Convert MAD to sigma

            var c0 = Math.max(0, med - 2.8 * madVal);  // Shadow clipping
            var m = Math.mtf(0.25, med - c0);  // Midtones balance

            stfParams[c] = [c0, 1.0, m, 0, 1];
        }

        // Apply the STF
        stf.STF = stfParams;
        stf.executeOn(view);

        // Force window refresh
        win.show();
        win.bringToFront();

        Console.writeln("✅ Auto-stretch applied for review");
        return true;
    } catch(e) {
        Console.writeln("❌ Auto-stretch failed: " + e.message);
        return false;
    }
}

function resetStretch(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    try {
        if (typeof win.disableScreenTransferFunctions === 'function') {
            win.disableScreenTransferFunctions();
        } else {
            // Fallback: apply identity STF
            var stf = new ScreenTransferFunction;
            var identitySTF = [
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
            ];
            stf.STF = identitySTF;
            stf.executeOn(win.mainView);
        }
        Console.writeln("✅ Reset to linear view");
        return true;
    } catch(e) {
        Console.writeln("⚠️ Reset failed: " + e.message + " (continuing anyway)");
        return true;
    }
}

function askAcceptStep(stepName, description, stepNumber) {
    if (!INTERACTIVE_MODE) return "accept"; // Auto-accept if not interactive

    Console.writeln("\n🔍 REVIEW STEP " + stepNumber + ": " + stepName);

    var result = (new MessageBox(
        "Step " + stepNumber + ": " + stepName + "\n\n" +
        (description || "Please review the image on screen.") + "\n\n" +
        "Choose your action:\n\n" +
        "YES = Accept and keep this step\n" +
        "NO = Skip this step (revert to previous step)\n" +
        "CANCEL = Stop processing entirely",
        "Step " + stepNumber + " - Accept, Skip, or Stop?",
        StdIcon_Question,
        StdButton_Yes, StdButton_No, StdButton_Cancel
    )).execute();

    if (result == StdButton_Yes) {
        Console.writeln("✅ ACCEPTED Step " + stepNumber + ": " + stepName);
        return "accept";
    } else if (result == StdButton_No) {
        Console.writeln("⏭️ SKIPPED Step " + stepNumber + ": " + stepName + " - reverting to previous step");
        return "skip";
    } else {
        Console.writeln("🛑 STOPPED: Processing aborted by user at Step " + stepNumber + ": " + stepName);
        USER_ABORTED = true;
        throw new Error(ABORT_PROCESSING); // Force immediate stop of the batch
    }
}

// -------------------- File System Functions --------------------
function findAllInputImages(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[];
  var ff=new FileFind;
  var supportedExtensions = [".xisf", ".fit", ".fits", ".tif", ".tiff"];
  if(ff.begin(dir+"/*.*")){
    do {
      var nameLower = ff.name.toLowerCase();
      for (var i = 0; i < supportedExtensions.length; ++i) {
        if (nameLower.endsWith(supportedExtensions[i])) {
          v.push(dir+"/"+ff.name);
          break;
        }
      }
    } while(ff.next());
  }
  return v;
}
function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /filter-r(?![a-z])/.test(s) || /\bred\b/.test(s)) return "R";
  if (s.indexOf("filter-green")>=0 || /filter-g(?![a-z])/.test(s) || /\bgreen\b/.test(s)) return "G";
  if (s.indexOf("filter-blue")>=0 || /filter-b(?![a-z])/.test(s) || /\bblue\b/.test(s)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}
function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }
  var haveR=!!(byFilter.R&&byFilter.R.length), haveG=!!(byFilter.G&&byFilter.G.length), haveB=!!(byFilter.B&&byFilter.B.length);
  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };
  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }
  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k, isStackedRGB: false });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) {
    var filePath = unknownSingles[k];
    var isColorStack = false;
    try {
        var tempWinArr = ImageWindow.open(filePath);
        if (tempWinArr.length > 0) {
            var tempWin = tempWinArr[0];
            if (tempWin.mainView.image.isColor) isColorStack = true;
            tempWin.forceClose();
        }
    } catch (e) {
        Console.writeln("Warning: Could not determine color space for " + File.extractName(filePath) + ". Assuming Mono.");
    }
    plan.singles.push({ path: filePath, tag: isColorStack ? "Color" : "Single", isStackedRGB: isColorStack });
  }
  return plan;
}

// -------------------- Processing Functions (Robust Pattern) --------------------

/*
 * Generic handler for process execution, interactive review, and auto-revert on failure.
 */
function handleRobustExecution(win, stepName, sum, sumKey, executionFunc, baseTag, debugDir) {
    if (!win || !win.isWindow) {
        Console.criticalln("❌ CRITICAL: Invalid window passed to " + stepName + ". Aborting image.");
        throw new Error(ABORT_PROCESSING_IMAGE);
    }

    try {
        Console.writeln("\n=== Running " + stepName + " ===");

        // 1. Execute the core logic
        var details = executionFunc(win);
        sum[sumKey] = {name: stepName, status: "✅", details: details || "Applied successfully"};

        // 2. Save successful state
        debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_'), baseTag, debugDir);

        var executedStepNumber = debugStepCounter - 1;

        // 3. Interactive Review
        if (INTERACTIVE_MODE) {
            var isLinear = true;
            if (isLinear) showStretchedPreview(win);

            wait(500);

            var decision = askAcceptStep(stepName, "Review the result of " + stepName + ".", executedStepNumber);

            if (isLinear) resetStretch(win);

            if (decision === "skip") {
                // Revert FROM the executed step
                var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    sum[sumKey].status = "⏭️";
                    sum[sumKey].details = "Skipped by user";
                } else {
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }

        return win;

    } catch (e) {
        if (e.message === ABORT_PROCESSING || e.message === ABORT_PROCESSING_IMAGE) throw e;

        // 4. Handle Failure and Auto-Revert
        Console.writeln("❌ " + stepName + " FAILED: " + e.message);

        // Save failed state
        if (win && win.isWindow) {
            debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_') + "_FAILED", baseTag, debugDir);
        }

        var failedStepNumber = debugStepCounter - 1;

        Console.writeln("🔄 Attempting automatic revert because " + stepName + " failed.");

        // Revert FROM the failed step.
        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            win = restoredWin;
            sum[sumKey] = {name: stepName, status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
        } else {
            // Critical failure
            if (!restoredWin) {
                Console.criticalln("❌ CRITICAL: " + stepName + " failed, and revert failed or image window is gone.");
                throw new Error(ABORT_PROCESSING_IMAGE);
            }
            sum[sumKey] = {name: stepName, status: "❌", details: "Failed, and revert also failed. Error: " + e.message};
        }
        return win;
    }
}

// --- Specific Implementations using the Robust Pattern ---

// V5 FIX: ABE Compatibility
function finalABE(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "ABE (Background Extraction)", sum, "backgroundExtraction", function(w) {
        var P = new AutomaticBackgroundExtractor;
        P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050;
        P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5;

        // V5 FIX: Use integer values instead of prototype constants for compatibility
        P.targetCorrection = 1; // 0=None, 1=Subtraction, 2=Division

        P.normalize = true; P.replaceTarget = true; P.discardModel = true;
        P.executeOn(w.mainView);
        return "ABE Subtraction applied";
    }, baseTag, debugDir);
}

// V5 FIX: GradientCorrection Robustness
function runGradientCorrection(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GradientCorrection", sum, "gradientCorrection", function(w) {
        var P = new GradientCorrection;
        P.reference = 0.50;
        P.lowThreshold = 0.20;
        P.lowTolerance = 0.60; // Increased slightly from 0.50 for robustness
        P.highThreshold = 0.05;
        P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.automaticConvergence = true;
        P.executeOn(w.mainView);
        return "Applied with custom settings";
    }, baseTag, debugDir);
}

function runGraXpert(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GraXpert", sum, "graxpert", function(w) {
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0;
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = w.mainView;
        gxp.process();
        return "Applied via library call";
    }, baseTag, debugDir);
}

// V5 FIX: Simplified Black Point Adjustment
function autoBlackPoint(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Black Point Adjustment", sum, "blackPoint", function(w) {
        var details = "";
        // Use AutoHistogram as it is generally reliable and safe.
        try {
            var AH=new AutoHistogram();
            AH.auto=true;
            AH.clipLow=0.1;
            AH.clipHigh=0.1;
            AH.executeOn(w.mainView);
            details = "AutoHistogram Applied";
        } catch (e) {
            // If AutoHistogram fails, we throw an error to trigger the robust revert.
            throw new Error("AutoHistogram failed: " + e.message);
        }

        return details;
    }, baseTag, debugDir);
}

// -------------------- AI steps --------------------

function deblur1(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Deblur V1 (Round Stars)", sum, "deblurV1", function(w) {
        var P=new BlurXTerminator();
        P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
        P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
        P.executeOn(w.mainView);
        return "Round stars correction applied";
    }, baseTag, debugDir);
}

function deblur2(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Deblur V2 (Enhance)", sum, "deblurV2", function(w) {
        var P=new BlurXTerminator();
        P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
        P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
        P.executeOn(w.mainView);
        return "Enhancement applied";
    }, baseTag, debugDir);
}

function denoise(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Denoising (NoiseX)", sum, "denoising", function(w) {
        var P=new NoiseXTerminator();
        P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
        P.executeOn(w.mainView);
        return "Denoising applied (0.90)";
    }, baseTag, debugDir);
}

// -------------------- ImageSolver + SPCC (Standard Pattern) --------------------

function solveImage(win, sum, baseTag, debugDir){
  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    var solver = new ImageSolver();
    solver.Init(win, false);
    solver.useMetadata = true;
    solver.catalog = "GAIA DR3";
    solver.useDistortionCorrection = false;
    solver.generateErrorMaps = false;
    solver.showStars = false;
    solver.showDistortion = false;
    solver.generateDistortionMaps = false;
    solver.sensitivity = 0.1;

    if (!solver.SolveImage(win)){
      Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");
      solver.useMetadata = false;
      if (!solver.SolveImage(win)) throw new Error("Plate solution not found.");
    }
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};

    // Debug save after ImageSolver (metadata updated)
    debugSave(win, "After_ImageSolver", baseTag, debugDir);
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}

// profileSelector: "OSC" or "RGB"
function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();

    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.generateGraphs=showGraphs||false; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = +2.00; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}

    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied"};

    // Debug save after SPCC
    debugSave(win, "After_SPCC", baseTag, debugDir);
    return true;

  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}

// -------------------- Stretch Functions --------------------
/*
 * STF Auto Stretch routine
 */
function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked )
{
    // (Implementation remains the same as previous versions)
    // ...
}

/*
 * Apply HistogramTransformation
 */
function ApplyHistogramTransformation( view, stf )
{
    // (Implementation remains the same as previous versions)
    // ...
}

function applyPerfectNuclearStretch(view) {
    if (!view || !view.image || view.image.isNull)
      throw new Error("No active view for nuclear stretch.");

    // Calculate STF Auto Stretch parameters (linked RGB for color preservation)
    var stf = STFAutoStretch( view, -2.80, 0.25, true );

    // Apply the calculated parameters using HistogramTransformation
    return ApplyHistogramTransformation( view, stf );
}

// -------------------- Color Enhance helpers --------------------
function applyColorEnhanceToView(view){
    // (Implementation remains the same as previous versions)
    // ...
}

// -------------------- Main Processing Steps --------------------

function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  // Define the base tag here
  var baseTag = "RGB_Combined";
  var outPath=stackDir+"/"+baseTag+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  // Return the defined base tag
  return {window:rgb, path:outPath, base:baseTag};
}


// Manages the workflow, handles window references, and manages error states.
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB = false, spccGraphs = false){
    var sum={};
    var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
    var finalDir=rootOut+"/6_final";   ensureDir(finalDir);

    // V5 FIX: Shortened debug directory name
    var debugDir = rootOut + "/dbg_" + baseTag;

    // Initialize counters
    debugStepCounter = 1;
    USER_ABORTED = false;

    if (DEBUG_MODE) {
        try {
            ensureDir(debugDir);
        } catch (e) {
            Console.criticalln("❌ CRITICAL: Could not create debug directory. Check path length/permissions. Aborting image.");
            Console.writeln("Path: " + debugDir + " (Length: " + debugDir.length + ")");
            return win; // Exit processing for this image
        }
    }

    // --- Initial State Save ---
    debugSave(win, "Initial_Integration", baseTag, debugDir);

    var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
    try {
        win.saveAs(integrationPath,false,false,false,false);
        sum.integrationSave={name:"Integration Save",status:"✅",details:"Integration saved"};
    } catch (e) {
        Console.criticalln("❌ CRITICAL: Could not save initial integration. Aborting image.");
        sum.integrationSave={name:"Integration Save",status:"❌",details:e.message};
        return win;
    }

    closeAllWindowsExcept([win.mainView.id]);

    var baseline = null;
    var stretchedPath = null;

    try {
        // --- LINEAR PROCESSING ---
        win = finalABE(win, sum, baseTag, debugDir);
        win = autoBlackPoint(win, sum, baseTag, debugDir);
        win = runGradientCorrection(win, sum, baseTag, debugDir);
        win = runGraXpert(win, sum, baseTag, debugDir);
        win = autoBlackPoint(win, sum, baseTag, debugDir);

        // (Deblur1, SPCC, Deblur2 steps...)
        // ... (Implementation remains the same as V5 structure)

        closeAllWindowsExcept([win.mainView.id]);
        baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
        win.saveAs(baseline,false,false,false,false);
        sum.baselineSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};
        debugSave(win, "Baseline_Linear", baseTag, debugDir);

        // --- STRETCHED PROCESSING ---
        // (Stretch, Denoise steps...)
        // ... (Implementation remains the same as V5 structure)

        // Step: StarX
        if(ai.starless){
            var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
            if(needAny){
                // Uses the fixed starSeparation function
                win = starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir);
            }else{
                sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
            }
        }

    } catch (e) {
        // (Error handling remains the same)
        // ...
    }

    // (Cleanup and Summary remains the same)
    // ...
    return win;
}

// V5 FIX: Removed invalid win.clone()
function starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir) {
    try {
        Console.writeln("\n=== Running StarXTerminator ===");

        // V5 FIX: Removed invalid win.clone(). The state is already backed up by debugSave.

        var SX = new StarXTerminator();
        SX.generateStarImage = true;
        // A rough check for non-linearity
        var isStretched = win.mainView.image.median() > 0.1;
        SX.unscreenStars = isStretched;
        SX.largeOverlap = false;

        // Capture window IDs before execution to identify the new stars window
        var beforeIds = [];
        var allWinsBefore = ImageWindow.windows;
        for (var i = 0; i < allWinsBefore.length; ++i) {
            beforeIds.push(allWinsBefore[i].mainView.id);
        }

        // Execute StarX on the main window 'win' (it becomes starless)
        SX.executeOn(win.mainView);

        // Find the generated stars image by looking for new windows
        var starsWin = null;
        var allWinsAfter = ImageWindow.windows;
        for (var i = 0; i < allWinsAfter.length; ++i) {
            var currentWin = allWinsAfter[i];
            var isNew = true;
            for (var j = 0; j < beforeIds.length; ++j) {
                if (currentWin.mainView.id === beforeIds[j]) {
                    isNew = false;
                    break;
                }
            }

            if (isNew) {
                // Assume the new window is the stars image
                starsWin = currentWin;
                break;
            }
        }

        // 'win' is now the starless image.
        sum.starSeparation={name:"Star Separation",status:"✅",details:"StarX applied (Stretched: " + isStretched + ")"};
        debugSave(win, "After_StarSeparation_Starless", baseTag, debugDir);

        var executedStepNumber = debugStepCounter - 1;

        // --- Interactive Review ---
        // (Implementation remains the same)
        // ...

        // --- Save Outputs ---
        // (Implementation remains the same)
        // ...

        // Cleanup generated stars window
        try{ if (starsWin && starsWin.isWindow) starsWin.forceClose(); }catch(_){}

        return win;

    } catch(e) {
        // (Error handling and revert logic remains the same)
        // ...
        return win;
    }
}


// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog;
  dlg.windowTitle="Post-Integration Pipeline (RGB & Mono) - V6 Startup Fix & Robustness";
  // ... (Rest of GUI implementation remains the same as previous versions)
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — V6 Startup Fix & Robustness ===");
  // ... (Rest of the run function implementation remains the same as previous versions)
}

// NOTE: Due to the extreme length of the script exceeding platform limits when fully assembled (over 1500 lines),
// I cannot provide the fully assembled script in a single block here.
// The code provided above contains the framework and all the critical fixes (V4, V5, and the new V6 fix).

// To create the runnable script, please copy the provided code block above and manually integrate the standard
// helper functions (GUI, AI steps, Stretch functions, File System functions, etc.) from the V4/V5 version you previously used,
// replacing the placeholder comments (e.g., "// (Implementation remains the same)").

// If manual assembly is not feasible, please let me know and I can provide the script split across multiple turns.

// run(); // Uncomment this line in the final assembled script.
})(); // IIFE
