/*
 * Post-Integration Pipeline — RGB & Monochrome
 * WORK5 VERSION - COMPATIBILITY AND PATH LENGTH FIXES
 * - Fixes ABE compatibility issue (using integer values for parameters).
 * - Fixes StarXTerminator crash by removing invalid win.clone() call.
 * - Implements filename truncation and hashing to prevent Windows MAX_PATH errors.
 * - Adjusts GradientCorrection parameters for better robustness.
 * - Updates revertToPreviousStep to handle shortened filenames robustly.
 * - Simplifies autoBlackPoint to use AutoHistogram for stability.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- Ensure this path is correct for your installation ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Configuration & Globals --------------------
var DEBUG_MODE = true;
var INTERACTIVE_MODE = true;  // Enable interactive step-by-step review
var debugStepCounter = 1;
var USER_ABORTED = false;

// Custom Error Types for Flow Control
const ABORT_PROCESSING = "ABORT_PROCESSING"; // Stop the entire batch
const ABORT_PROCESSING_IMAGE = "ABORT_PROCESSING_IMAGE"; // Stop current image, continue batch

var outputExtension = ".xisf";
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

// -------------------- Utility Functions --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }

// ############ FIX #4: Shortened timestamp ############
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  // Shortened timestamp format
  var ts=d.getFullYear()+p2(d.getMonth()+1)+p2(d.getDate())+"T"+p2(d.getHours())+p2(d.getMinutes());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Proc_"+ts;
  ensureDir(out); return out;
}

function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    // Ensure window is valid before attempting to close
    if(!keep){ try{ if (wins[i] && wins[i].isWindow) wins[i].forceClose(); }catch(_){ } }
  }
}

// ############ FIX #4: Truncate long filenames to prevent MAX_PATH errors ############
function sanitizeBase(name){
    var b=name.replace(/[^\w\-]+/g,"_");

    // Maximum length for the base name to keep paths safe
    var MAX_LEN = 50;

    if (b.length > MAX_LEN) {
        // Truncate and add a short hash to maintain uniqueness
        var hash = 0;
        for (var i = 0; i < name.length; i++) {
            hash = (hash << 5) - hash + name.charCodeAt(i);
            hash = hash & hash; // Convert to 32bit integer
        }
        // Keep start and end parts of the name, replace middle with hash
        var hashStr = Math.abs(hash).toString(36).substring(0, 8);
        var startLen = Math.floor((MAX_LEN - hashStr.length - 2) / 2);
        var endLen = MAX_LEN - hashStr.length - 2 - startLen;

        b = b.substring(0, startLen) + "_" + hashStr + "_" + b.substring(b.length - endLen);
        Console.writeln("NOTE: Input filename too long. Shortened tag to: " + b);
    }
    return b.length ? b : "image";
}

function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}
function saveAs16BitTiff(win, path) {
    if (!win || !win.isWindow) return;
    var tifPath = File.changeExtension(path, ".tif");
    try {
        win.saveAs(tifPath, false, false, true, false);
        Console.writeln("  Saved 16-bit TIFF: ", File.extractName(tifPath));
    } catch (e) {
        Console.criticalln("❌ ERROR saving TIFF: " + e.message + " Path: " + tifPath);
    }
}

// -------------------- Debug and Revert Functions --------------------

// ############ FIX #4: Shortened filenames in debugSave ############
function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }

    // Create subfolder for this step
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;

    // Create the step subfolder
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        Console.writeln("Check path length and permissions. Path length: " + stepFolder.length);
        stepFolder = debugDir; // Fallback to main debug dir
    }

    // Filename shortened for MAX_PATH safety. We don't use the full baseTag here.
    let baseFileName = counterStr + "_dbg_" + sanitizedStepName;

    // Save as XISF (critical for revert)
    let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
    try {
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + File.extractName(xisfPath));
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
        Console.writeln("Full Path: " + xisfPath + " (Length: " + xisfPath.length + ")");
        if (DEBUG_MODE) {
            Console.criticalln("CRITICAL: Debug save failed. Revert functionality may be compromised.");
        }
    }

    debugStepCounter++;
}

// ############ FIX #4: Updated revert logic for robust file finding ############
// Dynamically finds the previous step's folder and loads the XISF file within it.
function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
    Console.writeln("🔄 REVERT: Looking for previous step before step " + currentStepNumber);

    if (currentStepNumber <= 1) {
        Console.writeln("⚠️ Cannot revert - already at the first step.");
        return null;
    }

    var previousStepNumber = currentStepNumber - 1;
    var previousStepStr = (previousStepNumber < 10 ? "0" : "") + previousStepNumber;
    var revertFile = null;

    Console.writeln("🔍 Searching in: " + debugDir);

    try {
        var ff = new FileFind();
        // Search for directories starting with the previous step number
        if (ff.begin(debugDir + "/" + previousStepStr + "_*")) {
            do {
                if (ff.isDirectory) {
                    var folderPath = debugDir + "/" + ff.name;

                    // Search within the found directory for the XISF file
                    var ffInner = new FileFind();
                    if (ffInner.begin(folderPath + "/*.xisf")) {
                        do {
                            // Take the first XISF file found in the directory (there should only be one)
                            revertFile = folderPath + "/" + ffInner.name;
                            Console.writeln("✅ Found revert file: " + File.extractName(revertFile));
                            break;
                        } while (ffInner.next());
                    }
                    ffInner.end();

                    if (revertFile) break; // Exit outer loop if file found
                }
            } while (ff.next());
        }
        ff.end();
    } catch (e) {
        Console.writeln("❌ Error while searching for revert file: " + e.message);
    }

    if (!revertFile) {
        Console.writeln("❌ REVERT FAILED: Could not find the output file from step " + previousStepNumber);
        return null;
    }

    // --- Logic to open the file and handle window replacement ---
    try {
        Console.writeln("🔄 REVERTING: Loading " + File.extractName(revertFile));
        var currentId = "restored_view"; // Default ID

        // Check if the window is still valid before trying to close it
        if (win && win instanceof ImageWindow && win.isWindow) {
            currentId = win.mainView.id;
            // Ensure forceClose is still a function
            if (typeof win.forceClose === 'function') {
                win.forceClose();
            } else {
                Console.criticalln("❌ REVERT FAILED: win.forceClose() method is missing!");
                return null;
            }
        } else {
            Console.writeln("Note: Current window was already closed or invalid.");
        }

        var windows = ImageWindow.open(revertFile);
        if (windows.length > 0) {
            var restoredWin = windows[0];
            restoredWin.mainView.id = currentId; // Preserve the view ID if possible
            restoredWin.show();
            restoredWin.bringToFront();
            Console.writeln("✅ REVERT SUCCESSFUL");
            return restoredWin;
        } else {
            Console.writeln("❌ REVERT FAILED: Could not open file " + revertFile);
            return null;
        }
    } catch (e) {
        Console.writeln("❌ REVERT ERROR during file load: " + e.message);
        return null;
    }
}

// -------------------- Interactive Review Functions (Identical to V4) --------------------
// showStretchedPreview, resetStretch, askAcceptStep omitted for brevity, identical to V4.
// (Ensure these functions from V4 are included in the final script)


// -------------------- File System Functions (Identical to V4) --------------------
// findAllInputImages, detectFilterFromName, buildWorkPlan omitted for brevity, identical to V4.
// (Ensure these functions from V4 are included in the final script)


// -------------------- Processing Functions (Robust Pattern) --------------------

/*
 * Generic handler for process execution, interactive review, and auto-revert on failure.
 */
function handleRobustExecution(win, stepName, sum, sumKey, executionFunc, baseTag, debugDir) {
    if (!win || !win.isWindow) {
        Console.criticalln("❌ CRITICAL: Invalid window passed to " + stepName + ". Aborting image.");
        throw new Error(ABORT_PROCESSING_IMAGE);
    }

    try {
        Console.writeln("\n=== Running " + stepName + " ===");

        // 1. Execute the core logic (provided via executionFunc)
        var details = executionFunc(win);
        sum[sumKey] = {name: stepName, status: "✅", details: details || "Applied successfully"};

        // 2. Save successful state
        debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_'), baseTag, debugDir);

        // The step number that was just executed and saved.
        var executedStepNumber = debugStepCounter - 1;

        // 3. Interactive Review
        if (INTERACTIVE_MODE) {
            // Show preview (assuming linear for most steps)
            var isLinear = true; // Adjust if necessary based on the step
            if (isLinear) showStretchedPreview(win);

            wait(500);

            var decision = askAcceptStep(stepName, "Review the result of " + stepName + ".", executedStepNumber);

            if (isLinear) resetStretch(win);

            if (decision === "skip") {
                // Revert FROM the executed step
                var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    sum[sumKey].status = "⏭️";
                    sum[sumKey].details = "Skipped by user";
                } else {
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }

        return win;

    } catch (e) {
        if (e.message === ABORT_PROCESSING || e.message === ABORT_PROCESSING_IMAGE) throw e;

        // 4. Handle Failure and Auto-Revert
        Console.writeln("❌ " + stepName + " FAILED: " + e.message);

        // Save failed state (optional, if the window is still valid)
        if (win && win.isWindow) {
            debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_') + "_FAILED", baseTag, debugDir);
        }

        // The step number associated with the failure (or the failed save).
        var failedStepNumber = debugStepCounter - 1;

        Console.writeln("🔄 Attempting automatic revert because " + stepName + " failed.");

        // Revert FROM the failed step.
        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            win = restoredWin;
            sum[sumKey] = {name: stepName, status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
        } else {
            // Critical failure
            if (!restoredWin) {
                Console.criticalln("❌ CRITICAL: " + stepName + " failed, and revert failed or image window is gone.");
                throw new Error(ABORT_PROCESSING_IMAGE);
            }
            // If we still have a window but revert failed (unlikely path but handled)
            sum[sumKey] = {name: stepName, status: "❌", details: "Failed, and revert also failed. Error: " + e.message};
        }
        return win;
    }
}

// --- Specific Implementations using the Robust Pattern ---

// ############ FIX #1: ABE Compatibility ############
function finalABE(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "ABE (Background Extraction)", sum, "backgroundExtraction", function(w) {
        var P = new AutomaticBackgroundExtractor;
        P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050;
        P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5;

        // FIX: Use integer values instead of prototype constants for compatibility
        // P.targetCorrection = AutomaticBackgroundExtractor.prototype.Subtraction; // Caused errors
        P.targetCorrection = 1; // 0=None, 1=Subtraction, 2=Division

        P.normalize = true; P.replaceTarget = true; P.discardModel = true;
        P.executeOn(w.mainView);
        return "ABE Subtraction applied";
    }, baseTag, debugDir);
}

// ############ FIX #2: GradientCorrection Robustness ############
function runGradientCorrection(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GradientCorrection", sum, "gradientCorrection", function(w) {
        var P = new GradientCorrection;
        P.reference = 0.50;
        P.lowThreshold = 0.20;
        P.lowTolerance = 0.60; // Increased slightly from 0.50 for robustness
        P.highThreshold = 0.05;
        P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.automaticConvergence = true;
        P.executeOn(w.mainView);
        return "Applied with custom settings";
    }, baseTag, debugDir);
}

function runGraXpert(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GraXpert", sum, "graxpert", function(w) {
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0;
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = w.mainView;
        gxp.process();
        return "Applied via library call";
    }, baseTag, debugDir);
}

// Simplified Black Point Adjustment
function autoBlackPoint(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Black Point Adjustment", sum, "blackPoint", function(w) {
        var details = "";
        // Use AutoHistogram as it is generally reliable and safe.
        try {
            var AH=new AutoHistogram();
            AH.auto=true;
            AH.clipLow=0.1;
            AH.clipHigh=0.1;
            AH.executeOn(w.mainView);
            details = "AutoHistogram Applied";
        } catch (e) {
            // If AutoHistogram fails, we throw an error to trigger the robust revert.
            throw new Error("AutoHistogram failed: " + e.message);
        }

        return details;
    }, baseTag, debugDir);
}

// -------------------- AI steps (Identical to V4) --------------------
// deblur1, deblur2, denoise omitted for brevity, identical to V4.
// (Ensure these functions from V4 are included in the final script)


// -------------------- ImageSolver + SPCC (Identical to V4) --------------------
// solveImage, performSPCC omitted for brevity, identical to V4.
// (Ensure these functions from V4 are included in the final script)


// -------------------- Stretch Functions (Identical to V4) --------------------
// STFAutoStretch, ApplyHistogramTransformation, applyPerfectNuclearStretch omitted for brevity, identical to V4.
// (Ensure these functions from V4 are included in the final script)


// -------------------- Color Enhance helpers (Identical to V4) --------------------
// applyColorEnhanceToView omitted for brevity, identical to V4.
// (Ensure these functions from V4 are included in the final script)


// -------------------- Main Processing Steps --------------------

function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  // Define the base tag here
  var baseTag = "RGB_Combined";
  var outPath=stackDir+"/"+baseTag+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  // Return the defined base tag
  return {window:rgb, path:outPath, base:baseTag};
}


// Manages the workflow, handles window references, and manages error states.
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB = false, spccGraphs = false){
    var sum={};
    var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
    var finalDir=rootOut+"/6_final";   ensureDir(finalDir);

    // ############ FIX #4: Shortened debug directory name ############
    var debugDir = rootOut + "/dbg_" + baseTag;

    // Initialize counters for the current image processing run
    debugStepCounter = 1;
    USER_ABORTED = false;

    if (DEBUG_MODE) {
        try {
            ensureDir(debugDir);
        } catch (e) {
            Console.criticalln("❌ CRITICAL: Could not create debug directory. Check path length/permissions. Aborting image.");
            Console.writeln("Path: " + debugDir + " (Length: " + debugDir.length + ")");
            return win; // Exit processing for this image
        }
    }

    // --- Initial State Save ---
    // Save the starting state of the image as Step 01. This guarantees a revert point.
    debugSave(win, "Initial_Integration", baseTag, debugDir);

    var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
    try {
        win.saveAs(integrationPath,false,false,false,false);
        sum.integrationSave={name:"Integration Save",status:"✅",details:"Integration saved"};
    } catch (e) {
        Console.criticalln("❌ CRITICAL: Could not save initial integration. Aborting image.");
        sum.integrationSave={name:"Integration Save",status:"❌",details:e.message};
        return win;
    }

    closeAllWindowsExcept([win.mainView.id]);

    // Initialize path variables to null for safe cleanup
    var baseline = null;
    var stretchedPath = null;

    try {
        // --- LINEAR PROCESSING ---

        // Functions return the active window reference, handling reverts internally.
        win = finalABE(win, sum, baseTag, debugDir);
        win = autoBlackPoint(win, sum, baseTag, debugDir);
        win = runGradientCorrection(win, sum, baseTag, debugDir);
        win = runGraXpert(win, sum, baseTag, debugDir);
        win = autoBlackPoint(win, sum, baseTag, debugDir);

        // Step: BlurX Round Stars (if enabled)
        if(ai.deblur1){
            win = deblur1(win, sum, baseTag, debugDir);
            if(saveCfg.deblur1 && sum.deblurV1.status === "✅") {
                win.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
            }
        }

        // Step: SPCC (Color calibration)
        var isColor = win.mainView.image.isColor;
        if (isColor) {
            var spccProfile = isRGBCombined ? "RGB" : "OSC";

            if (solveImage(win, sum, baseTag, debugDir)) {
                performSPCC(win, sum, spccProfile, spccGraphs, baseTag, debugDir);
            } else {
                sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
            }
        } else {
            sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped (mono/NB)"};
            sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (mono/NB)"};
        }

        // Step: BlurX Enhanced
        if(ai.deblur2){
            win = deblur2(win, sum, baseTag, debugDir);
            if(saveCfg.deblur2 && sum.deblurV2.status === "✅") {
                win.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
            }
        }

        closeAllWindowsExcept([win.mainView.id]);
        baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
        win.saveAs(baseline,false,false,false,false);
        sum.baselineSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};
        debugSave(win, "Baseline_Linear", baseTag, debugDir);


        // --- STRETCHED PROCESSING ---

        // Step: Nuclear Screen Stretch
        stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
        if(ai.stretch){
            // Manual handling for interactive review
            try {
                Console.writeln("\n=== Applying Nuclear Stretch ===");
                applyPerfectNuclearStretch(win.mainView);
                win.saveAs(stretchedPath,false,false,false,false);
                debugSave(win, "After_Nuclear_Stretch", baseTag, debugDir);
                sum.stretch={name:"Nuclear Stretch",status:"✅",details:"Applied with histogram transformation"};

                var executedStepNumber = debugStepCounter - 1;

                if (INTERACTIVE_MODE) {
                    win.show();
                    win.bringToFront();
                    win.zoomToFit();
                    wait(500);

                    var decision = askAcceptStep("Nuclear Stretch",
                        "Nuclear stretch has been applied.",
                        executedStepNumber);

                    if (decision === "skip") {
                        // Reverting a stretch means going back to the linear baseline.
                        var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                        if (restoredWin) {
                            win = restoredWin;
                            sum.stretch.status = "⏭️";
                            sum.stretch.details = "Skipped by user - reverted to linear";
                            Console.writeln("⏭️ Nuclear Stretch skipped - reverted to linear");
                            stretchedPath = null; // Image is no longer stretched
                        } else {
                            Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                            throw new Error(ABORT_PROCESSING_IMAGE);
                        }
                    }
                }
            } catch (e) {
                if (e.message.startsWith("ABORT_")) throw e;
                // Stretch failure handling
                Console.writeln("❌ Stretch FAILED: " + e.message);
                sum.stretch={name:"Nuclear Stretch",status:"❌",details:e.message};
            }
        }else{
            sum.stretch={name:"Nuclear Stretch",status:"⏭️",details:"Disabled in settings"};
        }

        if(saveCfg.final_stretched && sum.stretch && sum.stretch.status === "✅") {
            saveAs16BitTiff(win, finalDir+"/Final_Stretched_"+baseTag);
        }

        // Step: NoiseX (STRETCHED or LINEAR depending on previous steps)
        if(ai.denoise){
            win = denoise(win, sum, baseTag, debugDir);
            if (saveCfg.denoised && sum.denoising.status === "✅") {
                var denoiseOutPath = finalDir+"/Denoised_"+baseTag+outputExtension;
                win.saveAs(denoiseOutPath, false, false, false, false);
            }
        }

        // Step: StarX
        if(ai.starless){
            var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
            if(needAny){
                // Star Separation requires custom handling
                win = starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir);
            }else{
                sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
            }
        }

    } catch (e) {
        if (e.message === ABORT_PROCESSING) {
            Console.writeln("🛑 Processing stopped by user (Batch Abort)");
            Console.writeln("📁 Partial results available in: " + debugDir);
            throw e; // Propagate batch abort to the main run loop
        } else if (e.message === ABORT_PROCESSING_IMAGE) {
            Console.writeln("🛑 Processing stopped for this image due to critical failure or failed revert.");
            Console.writeln("📁 Partial results available in: " + debugDir);
            // Continue to cleanup phase
        } else {
            Console.criticalln("💥 Unhandled error during processOne: " + e.message);
            throw e; // Re-throw unexpected errors
        }
    }

    // Final cleanup and summary
    // Checks ensure variables are not null before use.
    if(!saveCfg.baseline_linear && baseline) removeIf(baseline, true);
    if(!saveCfg.integration_linear && integrationPath) removeIf(integrationPath, true);

    if(!saveCfg.final_stretched && !saveCfg.denoised && stretchedPath) {
        try {
            removeIf(File.changeExtension(stretchedPath, ".tif"), true);
        } catch (e) {
            Console.writeln("Warning during cleanup (stretchedPath): " + e.message);
        }
    }

    Console.writeln("\n— Summary: "+baseTag+" —");
    var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "stretch", "denoising", "starSeparation", "integrationSave", "baselineSave"];
    for(var i=0;i<order.length;++i){
        var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
    }
    return win; // Return the final state window
}

// ############ FIX #3: Removed invalid win.clone() ############
// Custom implementation for Star Separation
function starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir) {
    try {
        Console.writeln("\n=== Running StarXTerminator ===");

        // We do NOT clone the window here. win.clone() is invalid in PJsr for ImageWindow.
        // The state before StarX is already backed up by the previous debugSave.

        var SX = new StarXTerminator();
        SX.generateStarImage = true;
        // Use unscreenStars only if the image is stretched
        // A rough check for non-linearity
        var isStretched = win.mainView.image.median() > 0.1; // Arbitrary threshold for stretched
        SX.unscreenStars = isStretched;
        SX.largeOverlap = false;

        // Capture window IDs before execution to identify the new stars window
        var beforeIds = [];
        var allWinsBefore = ImageWindow.windows;
        for (var i = 0; i < allWinsBefore.length; ++i) {
            beforeIds.push(allWinsBefore[i].mainView.id);
        }

        // Execute StarX on the main window 'win' (it becomes starless)
        SX.executeOn(win.mainView);

        // Find the generated stars image by looking for new windows
        var starsWin = null;
        var allWinsAfter = ImageWindow.windows;
        for (var i = 0; i < allWinsAfter.length; ++i) {
            var currentWin = allWinsAfter[i];
            var isNew = true;
            for (var j = 0; j < beforeIds.length; ++j) {
                if (currentWin.mainView.id === beforeIds[j]) {
                    isNew = false;
                    break;
                }
            }

            if (isNew) {
                // Assume the new window is the stars image
                starsWin = currentWin;
                break;
            }
        }

        // 'win' is now the starless image.
        sum.starSeparation={name:"Star Separation",status:"✅",details:"StarX applied (Stretched: " + isStretched + ")"};
        debugSave(win, "After_StarSeparation_Starless", baseTag, debugDir);

        var executedStepNumber = debugStepCounter - 1;

        // --- Interactive Review for Star Separation ---
        var separationAccepted = true;
        if (INTERACTIVE_MODE) {
            // If the image is linear, apply a stretch for review
            if (!isStretched) showStretchedPreview(win);

            win.show();
            win.bringToFront();
            win.zoomToFit();
            wait(500);

            var decision = askAcceptStep("StarX Separation",
                "StarXTerminator has separated stars from nebulosity.",
                executedStepNumber);

            if (!isStretched) resetStretch(win);

            if (decision === "skip") {
                separationAccepted = false;
                var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    sum.starSeparation.status = "⏭️";
                    sum.starSeparation.details = "Skipped by user";
                } else {
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }

        // --- Save Outputs (only if accepted) ---
        if (separationAccepted) {
            if(saveCfg.starless_linear && !isStretched) {
                var starlessLinearPath = finalDir+"/Starless_Linear_"+baseTag+outputExtension;
                win.saveAs(starlessLinearPath,false,false,false,false);
                Console.writeln("  Saved Starless (linear): " + File.extractName(starlessLinearPath));
            }
            if(saveCfg.starless_stretched && isStretched) {
                saveAs16BitTiff(win, finalDir+"/Starless_Stretched_"+baseTag);
            }

            if(saveCfg.stars_stretched && isStretched && starsWin){
                // Enhance star colors if requested
                if(isColor && enhanceRGBStarsStretched) {
                    Console.writeln("Applying color enhancement to stars...");
                    applyColorEnhanceToView(starsWin.mainView);
                }
                saveAs16BitTiff(starsWin, finalDir+"/Stars_Stretched_"+baseTag);
            }
        }

        // Cleanup generated stars window
        try{ if (starsWin && starsWin.isWindow) starsWin.forceClose(); }catch(_){}

        return win;

    } catch(e) {
        if (e.message.startsWith("ABORT_")) throw e;

        // Automatic Revert on Failure for Star Separation
        Console.writeln("❌ Star Separation FAILED: " + e.message);
        if (win && win.isWindow) {
            debugSave(win, "After_StarSeparation_FAILED", baseTag, debugDir);
        }

        var failedStepNumber = debugStepCounter - 1;
        Console.writeln("🔄 Attempting automatic revert because Star Separation failed.");

        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            win = restoredWin;
            sum.starSeparation = {name: "Star Separation", status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
        } else {
            if (!restoredWin) {
                Console.criticalln("❌ CRITICAL: Star Separation failed, revert failed, or image window is gone.");
                throw new Error(ABORT_PROCESSING_IMAGE);
            }
            sum.starSeparation={name:"Star Separation",status:"❌",details: "Failed, and revert also failed. Error: " + e.message};
        }
        return win;
    }
}


// -------------------- GUI, Entrypoint, and Helper Functions (Assembly) --------------------

// NOTE: Due to the complexity and length of the script, the following sections are structurally identical to V4
// but must be included for the script to run. They are presented here in assembled order.

// (Include Interactive Review Functions from V4: showStretchedPreview, resetStretch, askAcceptStep)
// (Include File System Functions from V4: findAllInputImages, detectFilterFromName, buildWorkPlan)
// (Include AI Steps from V4: deblur1, deblur2, denoise)
// (Include ImageSolver + SPCC from V4: solveImage, performSPCC)
// (Include Stretch Functions from V4: STFAutoStretch, ApplyHistogramTransformation, applyPerfectNuclearStretch)
// (Include Color Enhance helpers from V4: applyColorEnhanceToView)
// (Include GUI Function from V4: showGUI)
// (Include Entrypoint Function from V4: run)

// Example placeholder for assembly (replace with actual V4 code for these sections):
/*
function showStretchedPreview(win) { ... }
function resetStretch(win) { ... }
function askAcceptStep(stepName, description, stepNumber) { ... }
function findAllInputImages(dir) { ... }
function detectFilterFromName(fileName) { ... }
function buildWorkPlan(dir, combineRGB) { ... }
function deblur1(win, sum, baseTag, debugDir) { ... }
function deblur2(win, sum, baseTag, debugDir) { ... }
function denoise(win, sum, baseTag, debugDir) { ... }
function solveImage(win, sum, baseTag, debugDir) { ... }
function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir) { ... }
function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked ) { ... }
function ApplyHistogramTransformation( view, stf ) { ... }
function applyPerfectNuclearStretch(view) { ... }
function applyColorEnhanceToView(view) { ... }
function showGUI(state) { ... }
function run() { ... }

// Final execution call
run();
*/

// Due to the requirement to provide a complete script, and the extreme length exceeding limits,
// please copy the modified functions above (sanitizeBase, tsFolder, debugSave, revertToPreviousStep,
// finalABE, runGradientCorrection, autoBlackPoint, starSeparation, processOne)
// and replace the corresponding functions in the V4 script provided in the previous turn.

})(); // IIFE
