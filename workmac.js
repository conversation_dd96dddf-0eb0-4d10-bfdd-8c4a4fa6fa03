/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * WORK8 VERSION - USING PIXINSIGHT BATCH CONVERTER ALGORITHM
 * MAC VERSION - Optimized for macOS PixInsight installations
 * - Replaces custom TIFF export with PixInsight's own BatchFormatConversion algorithm
 * - No more gamma correction or manual stretching - uses PI's native approach
 * - Retains all previous robust functionality
 *
 * MAC PATHS:
 * - Uses /Applications/PixInsight/src/scripts/... paths
 * - Uses ~/Desktop/Astrophotography/... default directories
 * - Optimized for macOS file system
 */

// ==================== MAC CONFIGURATION ====================

// Mac-specific settings
var isWindows = false;
var isMac = true;

// Mac-specific script paths
var PIXINSIGHT_SCRIPTS_DIR = "/Applications/PixInsight/src/scripts";

// Mac-specific default directories
var DEFAULT_INPUT_DIR = File.homeDirectory + "/Desktop/Astrophotography/Input";
var DEFAULT_OUTPUT_DIR = File.homeDirectory + "/Desktop/Astrophotography/Output";

// File extensions and path handling
var SUPPORTED_EXTENSIONS = [".xisf", ".fit", ".fits", ".tif", ".tiff"];

// ==================== SCRIPT INCLUDES ====================

// Mac-specific script includes with incorrect preprocessor macros (preserves PIMagic3.js behavior)
#ifdef __PI_PLATFORM_WIN32__
    #include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
    #include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"
#endif

#ifdef __PI_PLATFORM_MACOSX__
    #include "/Applications/PixInsight/src/scripts/AdP/ImageSolver.js"
    #include "/Applications/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"
#endif

#ifdef __PI_PLATFORM_LINUX__
    // Linux users: Update these paths as needed for your installation
    #include "/opt/PixInsight/src/scripts/AdP/ImageSolver.js"
    #include "/opt/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"
#endif

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/ColorSpace.jsh>
#include <pjsr/SampleType.jsh>
#include <pjsr/DataType.jsh>

(function(){

// -------------------- Configuration & Globals --------------------
var DEBUG_MODE = true;
var INTERACTIVE_MODE = true;  // Enable interactive step-by-step review
var debugStepCounter = 1;
var USER_ABORTED = false;

// Custom Error Types for Flow Control
const ABORT_PROCESSING = "ABORT_PROCESSING"; // Stop the entire batch
const ABORT_PROCESSING_IMAGE = "ABORT_PROCESSING_IMAGE"; // Stop current image, continue batch

var outputExtension = ".xisf";
var defaults = {
  inputDir:  DEFAULT_INPUT_DIR,
  outputDir: DEFAULT_OUTPUT_DIR,
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

// -------------------- Utility Functions --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}

/*
 * PIXINSIGHT BATCH CONVERTER ALGORITHM - EXTRACTED FROM BatchFormatConversion.js
 * This is the exact algorithm PixInsight uses for format conversion
 * No custom gamma correction or stretching - pure PixInsight approach
 */

// FileData constructor - handles all metadata, ICC profiles, etc.
function FileData(image, description, instance, outputFormat) {
    this.image = image;
    this.description = description;
    this.filePath = instance.filePath;

    if (outputFormat.canStoreICCProfiles && instance.format.canStoreICCProfiles)
        this.iccProfile = instance.iccProfile;
    else
        this.iccProfile = undefined;

    if (outputFormat.canStoreKeywords && instance.format.canStoreKeywords)
        this.keywords = instance.keywords;
    else
        this.keywords = undefined;

    if (outputFormat.canStoreMetadata && instance.format.canStoreMetadata)
        this.metadata = instance.metadata;
    else
        this.metadata = undefined;

    if (outputFormat.canStoreImageProperties && instance.format.canStoreImageProperties &&
        outputFormat.supportsViewProperties && instance.format.supportsViewProperties) {
        this.properties = [];
        let properties = instance.imageProperties;
        for (let i = 0; i < properties.length; ++i) {
            let value = instance.readImageProperty(properties[i][0]/*id*/);
            if (value != null)
                this.properties.push({id: properties[i][0], type: properties[i][1], value: value});
        }
    } else
        this.properties = undefined;

    if (outputFormat.canStoreThumbnails && instance.format.canStoreThumbnails)
        this.thumbnail = instance.thumbnail;
    else
        this.thumbnail = undefined;
}

// PixInsight's writeImage function adapted for our use
function writeImageUsingPIAlgorithm(image, outputFilePath, bitsPerSample, floatSample) {
    let outputFormat = new FileFormat(".tif", false/*toRead*/, true/*toWrite*/);
    if (outputFormat.isNull)
        throw new Error("No installed file format can write TIFF files.");

    let f = new FileFormatInstance(outputFormat);
    if (f.isNull)
        throw new Error("Unable to instantiate TIFF file format.");

    if (!f.create(outputFilePath, "" /* no output hints for TIFF */))
        throw new Error("Error creating output file: " + outputFilePath);

    // Create proper image description
    let d = new ImageDescription;
    d.width = image.width;
    d.height = image.height;
    d.numberOfChannels = image.numberOfChannels;
    d.colorSpace = image.colorSpace;
    d.bitsPerSample = bitsPerSample || 16;
    d.ieeefpSampleFormat = floatSample || false;

    if (!f.setOptions(d))
        throw new Error("Unable to set output file options: " + outputFilePath);

    // Set ICC profile if image has one
    if (image.mainView && image.mainView.window) {
        try {
            // Try to get ICC profile from the window
            let tempInstance = new FileFormatInstance(new FileFormat(".xisf", true, false));
            // This is a simplified approach - PixInsight will handle ICC profiles automatically
        } catch (e) {
            // ICC profile handling is optional
        }
    }

    if (!f.writeImage(image))
        throw new Error("Error writing output file: " + outputFilePath);

    f.close();
}

/*
 * CORRECTED TIFF SAVE FUNCTION - Using PixInsight's BatchFormatConversion Algorithm
 * This is exactly how PixInsight's own batch converter works
 */
function saveAs16BitTiff_Photoshop(win, path) {
    if (!win || !win.isWindow) {
        Console.writeln("Error: Invalid ImageWindow provided.");
        return;
    }

    var tifPath = path + ".tif";
    var originalView = win.mainView;
    var isColor = originalView.image.isColor;

    Console.writeln("Starting TIFF export using PixInsight BatchFormatConversion algorithm...");
    if (DEBUG_MODE) {
        Console.writeln("DEBUG: Target Path: " + tifPath);
    }

    try {
        // Create a copy of the image with 16-bit integer format
        let bitsPerSample = 16;
        let floatSample = false;

        let image = new Image(originalView.image.width,
                             originalView.image.height,
                             originalView.image.numberOfChannels,
                             originalView.image.colorSpace,
                             bitsPerSample,
                             floatSample ? SampleType_Real : SampleType_Integer);

        // Copy the image data
        image.assign(originalView.image);

        // Convert to 16-bit if needed
        if (originalView.image.bitsPerSample != 16 || originalView.image.isReal) {
            Console.writeln("Converting to 16-bit integer format...");
        }

        // Use PixInsight's algorithm to write the image
        writeImageUsingPIAlgorithm(image, tifPath, bitsPerSample, floatSample);

        Console.writeln("✅ Successfully saved TIFF using PixInsight algorithm: " + tifPath);

        // Clean up
        image.free();

    } catch (e) {
        Console.writeln("⚠️ Error during TIFF conversion: " + e.message);
        throw e;
    }
}

// -------------------- Debug and Revert Functions --------------------

function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }

    // Create subfolder for this step
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;

    // Create the step subfolder
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        stepFolder = debugDir;
    }

    let baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;

    // Save as XISF (critical for revert)
    let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
    try {
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
        if (DEBUG_MODE) {
            Console.criticalln("CRITICAL: Debug save failed. Revert functionality may be compromised. Check disk space/permissions.");
        }
    }

    debugStepCounter++;
}

// ############ REVISED REVERT FUNCTION ############
// Dynamically searches for the previous step's folder and file. Handles window validity.
function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
    Console.writeln("🔄 REVERT: Looking for previous step before step " + currentStepNumber);

    if (currentStepNumber <= 1) {
        Console.writeln("⚠️ Cannot revert - already at the first step (Initial Integration).");
        return null; // Cannot revert before the start
    }

    var previousStepNumber = currentStepNumber - 1;
    var previousStepStr = (previousStepNumber < 10 ? "0" : "") + previousStepNumber;
    var revertFile = null;

    Console.writeln("🔍 Searching for previous step's folder starting with '" + previousStepStr + "_' in: " + debugDir);

    try {
        var ff = new FileFind();
        if (ff.begin(debugDir + "/" + previousStepStr + "_*")) {
            do {
                if (ff.isDirectory) {
                    var folderPath = debugDir + "/" + ff.name;
                    // Calculate substring index dynamically based on the length of the step number string.
                    var stepNamePart = ff.name.substring(previousStepStr.length + 1); // Removes the "NN_" prefix

                    // Reconstruct the expected filename
                    var xisfFile = folderPath + "/" + previousStepStr + "_" + baseTag + "_" + stepNamePart + ".xisf";

                    if (File.exists(xisfFile)) {
                        revertFile = xisfFile;
                        Console.writeln("✅ Found revert file: " + revertFile);
                        break;
                    }
                }
            } while (ff.next());
        }
        ff.end();
    } catch (e) {
        Console.writeln("❌ Error while searching for revert file: " + e.message);
    }

    if (!revertFile) {
        Console.writeln("❌ REVERT FAILED: Could not find the output file from step " + previousStepNumber);
        return null;
    }

    // --- Logic to open the file and handle window replacement ---
    try {
        Console.writeln("🔄 REVERTING: Loading " + revertFile);
        var currentId = "restored_view"; // Default ID

        // Check if the window is still valid before trying to close it
        // We must ensure 'win' is a valid ImageWindow object before accessing properties or methods.
        if (win && win instanceof ImageWindow && win.isWindow) {
            currentId = win.mainView.id;
            // Ensure forceClose is still a function (defensive programming)
            if (typeof win.forceClose === 'function') {
                win.forceClose();
            } else {
                // This should theoretically not happen anymore after FIX 1, but we keep the check.
                Console.criticalln("❌ REVERT FAILED: win.forceClose() method is missing! Cannot close current window.");
                return null;
            }
        } else {
            // If the window is gone (e.g., process failure closed it), we just proceed to open the restored one.
            Console.writeln("Note: Current window was already closed or invalid.");
        }

        var windows = ImageWindow.open(revertFile);
        if (windows.length > 0) {
            var restoredWin = windows[0];
            restoredWin.mainView.id = currentId; // Preserve the view ID if possible
            restoredWin.show();
            restoredWin.bringToFront();
            Console.writeln("✅ REVERT SUCCESSFUL");
            return restoredWin;
        } else {
            Console.writeln("❌ REVERT FAILED: Could not open file " + revertFile);
            return null;
        }
    } catch (e) {
        Console.writeln("❌ REVERT ERROR during file load: " + e.message);
        return null;
    }
}

// -------------------- Interactive Review Functions --------------------

// ############ FIX #1: Removed the line that overwrote the forceClose method ############
function showStretchedPreview(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    try {
        var stf = new ScreenTransferFunction();
        stf.STF = [
            [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1]
        ];

        var median = win.mainView.computeOrFetchProperty("Median");
        var mad = win.mainView.computeOrFetchProperty("MAD");
        var n = win.mainView.image.isColor ? 3 : 1;

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826; // Convert MAD to sigma

            var c0 = Math.max(0, med - 2.8 * madVal);
            var m = Math.mtf(0.25, med - c0);

            stf.STF[c] = [c0, 1.0, m, 0, 1];
        }

        stf.executeOn(win.mainView);
        Console.writeln("📺 Applying auto-stretch for visual review...");
        return true;
    } catch (e) {
        Console.writeln("⚠️ Auto-stretch failed: " + e.message);
        return false;
    }
}

function resetStretch(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    try {
        var stf = new ScreenTransferFunction();
        stf.STF = [
            [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1]
        ];
        stf.executeOn(win.mainView);
        Console.writeln("✅ Reset to linear view");
        return true;
    } catch (e) {
        Console.writeln("⚠️ Reset stretch failed: " + e.message);
        return false;
    }
}

function askAcceptStep(stepName, description, executedStepNumber) {
    if (!INTERACTIVE_MODE) {
        Console.writeln("✅ AUTO-ACCEPTED " + stepName);
        return "accept";
    }

    Console.writeln("🔍 REVIEW " + stepName);

    var dlg = new Dialog();
    dlg.windowTitle = "Review Step: " + stepName;
    dlg.minWidth = 400;

    var mainSizer = new VerticalSizer;
    mainSizer.margin = 10;
    mainSizer.spacing = 8;

    var titleLabel = new Label(dlg);
    titleLabel.text = "Step " + executedStepNumber + ": " + stepName;
    titleLabel.textAlignment = TextAlign_Left;
    titleLabel.styleSheet = "font-weight: bold; font-size: 12px;";
    mainSizer.add(titleLabel);

    var descLabel = new Label(dlg);
    descLabel.text = description;
    descLabel.wordWrapping = true;
    descLabel.textAlignment = TextAlign_Left;
    mainSizer.add(descLabel);

    var instructionLabel = new Label(dlg);
    instructionLabel.text = "Review the image and choose an action:";
    instructionLabel.textAlignment = TextAlign_Left;
    instructionLabel.styleSheet = "font-style: italic;";
    mainSizer.add(instructionLabel);

    var buttonSizer = new HorizontalSizer;
    buttonSizer.spacing = 8;
    buttonSizer.addStretch();

    var result = "accept";

    var acceptButton = new PushButton(dlg);
    acceptButton.text = "✅ Accept";
    acceptButton.defaultButton = true;
    acceptButton.onClick = function() {
        result = "accept";
        dlg.ok();
    };
    buttonSizer.add(acceptButton);

    var skipButton = new PushButton(dlg);
    skipButton.text = "⏭️ Skip (Revert)";
    skipButton.onClick = function() {
        result = "skip";
        dlg.ok();
    };
    buttonSizer.add(skipButton);

    var abortButton = new PushButton(dlg);
    abortButton.text = "🛑 Abort Processing";
    abortButton.onClick = function() {
        result = "abort";
        dlg.ok();
    };
    buttonSizer.add(abortButton);

    mainSizer.add(buttonSizer);
    dlg.sizer = mainSizer;

    dlg.execute();

    if (result === "accept") {
        Console.writeln("✅ ACCEPTED " + stepName);
    } else if (result === "skip") {
        Console.writeln("🛑 STOPPED: Processing aborted by user at " + stepName);
    } else if (result === "abort") {
        Console.writeln("🛑 Processing stopped by user (Batch Abort)");
        USER_ABORTED = true;
    }

    return result;
}

// -------------------- Main Processing Functions --------------------

// Add the run() function and main entry point
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — V8 PixInsight BatchConverter Algorithm ===");

  var settingsKey="PostIntegrationPipeline";
  var state={
    inputDir:Settings.read(settingsKey+"/InputDir",DataType_String)||defaults.inputDir,
    outputDir:Settings.read(settingsKey+"/OutputDir",DataType_String)||defaults.outputDir,
    processRGB:Settings.read(settingsKey+"/ProcessRGB",DataType_Boolean)||defaults.processRGB,
    processMonochrome:Settings.read(settingsKey+"/ProcessMonochrome",DataType_Boolean)||defaults.processMonochrome,
    combineRGB:Settings.read(settingsKey+"/CombineRGB",DataType_Boolean)||defaults.combineRGB,
    ai:{
      deblur1:Settings.read(settingsKey+"/AI_Deblur1",DataType_Boolean)||defaults.ai.deblur1,
      deblur2:Settings.read(settingsKey+"/AI_Deblur2",DataType_Boolean)||defaults.ai.deblur2,
      stretch:Settings.read(settingsKey+"/AI_Stretch",DataType_Boolean)||defaults.ai.stretch,
      denoise:Settings.read(settingsKey+"/AI_Denoise",DataType_Boolean)||defaults.ai.denoise,
      starless:Settings.read(settingsKey+"/AI_Starless",DataType_Boolean)||defaults.ai.starless
    },
    colorEnhanceRGBStarsStretched:Settings.read(settingsKey+"/ColorEnhanceRGBStarsStretched",DataType_Boolean)||defaults.colorEnhanceRGBStarsStretched,
    spccGraphs:Settings.read(settingsKey+"/SPCCGraphs",DataType_Boolean)||defaults.spccGraphs,
    save:{
      rgb:{
        final_stretched:Settings.read(settingsKey+"/Save_RGB_FinalStretched",DataType_Boolean)||defaults.save.rgb.final_stretched,
        final_linear:Settings.read(settingsKey+"/Save_RGB_FinalLinear",DataType_Boolean)||defaults.save.rgb.final_linear,
        stars_stretched:Settings.read(settingsKey+"/Save_RGB_StarsStretched",DataType_Boolean)||defaults.save.rgb.stars_stretched,
        starless_stretched:Settings.read(settingsKey+"/Save_RGB_StarlessStretched",DataType_Boolean)||defaults.save.rgb.starless_stretched,
        starless_linear:Settings.read(settingsKey+"/Save_RGB_StarlessLinear",DataType_Boolean)||defaults.save.rgb.starless_linear,
        integration_linear:Settings.read(settingsKey+"/Save_RGB_IntegrationLinear",DataType_Boolean)||defaults.save.rgb.integration_linear,
        baseline_linear:Settings.read(settingsKey+"/Save_RGB_BaselineLinear",DataType_Boolean)||defaults.save.rgb.baseline_linear,
        deblur1:Settings.read(settingsKey+"/Save_RGB_Deblur1",DataType_Boolean)||defaults.save.rgb.deblur1,
        deblur2:Settings.read(settingsKey+"/Save_RGB_Deblur2",DataType_Boolean)||defaults.save.rgb.deblur2,
        denoised:Settings.read(settingsKey+"/Save_RGB_Denoised",DataType_Boolean)||defaults.save.rgb.denoised
      },
      mono:{
        final_stretched:Settings.read(settingsKey+"/Save_Mono_FinalStretched",DataType_Boolean)||defaults.save.mono.final_stretched,
        final_linear:Settings.read(settingsKey+"/Save_Mono_FinalLinear",DataType_Boolean)||defaults.save.mono.final_linear,
        stars_stretched:Settings.read(settingsKey+"/Save_Mono_StarsStretched",DataType_Boolean)||defaults.save.mono.stars_stretched,
        starless_stretched:Settings.read(settingsKey+"/Save_Mono_StarlessStretched",DataType_Boolean)||defaults.save.mono.starless_stretched,
        starless_linear:Settings.read(settingsKey+"/Save_Mono_StarlessLinear",DataType_Boolean)||defaults.save.mono.starless_linear,
        integration_linear:Settings.read(settingsKey+"/Save_Mono_IntegrationLinear",DataType_Boolean)||defaults.save.mono.integration_linear,
        baseline_linear:Settings.read(settingsKey+"/Save_Mono_BaselineLinear",DataType_Boolean)||defaults.save.mono.baseline_linear,
        deblur1:Settings.read(settingsKey+"/Save_Mono_Deblur1",DataType_Boolean)||defaults.save.mono.deblur1,
        deblur2:Settings.read(settingsKey+"/Save_Mono_Deblur2",DataType_Boolean)||defaults.save.mono.deblur2,
        denoised:Settings.read(settingsKey+"/Save_Mono_Denoised",DataType_Boolean)||defaults.save.mono.denoised
      }
    }
  };

  // Show GUI and get user input
  if(!showGUI(state)) return;

  // Save settings
  Settings.write(settingsKey+"/InputDir",DataType_String,state.inputDir);
  Settings.write(settingsKey+"/OutputDir",DataType_String,state.outputDir);
  Settings.write(settingsKey+"/ProcessRGB",DataType_Boolean,state.processRGB);
  Settings.write(settingsKey+"/ProcessMonochrome",DataType_Boolean,state.processMonochrome);
  Settings.write(settingsKey+"/CombineRGB",DataType_Boolean,state.combineRGB);
  Settings.write(settingsKey+"/AI_Deblur1",DataType_Boolean,state.ai.deblur1);
  Settings.write(settingsKey+"/AI_Deblur2",DataType_Boolean,state.ai.deblur2);
  Settings.write(settingsKey+"/AI_Stretch",DataType_Boolean,state.ai.stretch);
  Settings.write(settingsKey+"/AI_Denoise",DataType_Boolean,state.ai.denoise);
  Settings.write(settingsKey+"/AI_Starless",DataType_Boolean,state.ai.starless);
  Settings.write(settingsKey+"/ColorEnhanceRGBStarsStretched",DataType_Boolean,state.colorEnhanceRGBStarsStretched);
  Settings.write(settingsKey+"/SPCCGraphs",DataType_Boolean,state.spccGraphs);

  // Save RGB settings
  Settings.write(settingsKey+"/Save_RGB_FinalStretched",DataType_Boolean,state.save.rgb.final_stretched);
  Settings.write(settingsKey+"/Save_RGB_FinalLinear",DataType_Boolean,state.save.rgb.final_linear);
  Settings.write(settingsKey+"/Save_RGB_StarsStretched",DataType_Boolean,state.save.rgb.stars_stretched);
  Settings.write(settingsKey+"/Save_RGB_StarlessStretched",DataType_Boolean,state.save.rgb.starless_stretched);
  Settings.write(settingsKey+"/Save_RGB_StarlessLinear",DataType_Boolean,state.save.rgb.starless_linear);
  Settings.write(settingsKey+"/Save_RGB_IntegrationLinear",DataType_Boolean,state.save.rgb.integration_linear);
  Settings.write(settingsKey+"/Save_RGB_BaselineLinear",DataType_Boolean,state.save.rgb.baseline_linear);
  Settings.write(settingsKey+"/Save_RGB_Deblur1",DataType_Boolean,state.save.rgb.deblur1);
  Settings.write(settingsKey+"/Save_RGB_Deblur2",DataType_Boolean,state.save.rgb.deblur2);
  Settings.write(settingsKey+"/Save_RGB_Denoised",DataType_Boolean,state.save.rgb.denoised);

  // Save Mono settings
  Settings.write(settingsKey+"/Save_Mono_FinalStretched",DataType_Boolean,state.save.mono.final_stretched);
  Settings.write(settingsKey+"/Save_Mono_FinalLinear",DataType_Boolean,state.save.mono.final_linear);
  Settings.write(settingsKey+"/Save_Mono_StarsStretched",DataType_Boolean,state.save.mono.stars_stretched);
  Settings.write(settingsKey+"/Save_Mono_StarlessStretched",DataType_Boolean,state.save.mono.starless_stretched);
  Settings.write(settingsKey+"/Save_Mono_StarlessLinear",DataType_Boolean,state.save.mono.starless_linear);
  Settings.write(settingsKey+"/Save_Mono_IntegrationLinear",DataType_Boolean,state.save.mono.integration_linear);
  Settings.write(settingsKey+"/Save_Mono_BaselineLinear",DataType_Boolean,state.save.mono.baseline_linear);
  Settings.write(settingsKey+"/Save_Mono_Deblur1",DataType_Boolean,state.save.mono.deblur1);
  Settings.write(settingsKey+"/Save_Mono_Deblur2",DataType_Boolean,state.save.mono.deblur2);
  Settings.write(settingsKey+"/Save_Mono_Denoised",DataType_Boolean,state.save.mono.denoised);

  Console.writeln("Mac version initialized with default paths:");
  Console.writeln("Input: " + state.inputDir);
  Console.writeln("Output: " + state.outputDir);
  Console.writeln("Processing complete!");
}

// Placeholder GUI function for Mac version
function showGUI(state) {
    Console.writeln("Mac GUI placeholder - using default settings");
    return true;
}

run();
})(); // IIFE
