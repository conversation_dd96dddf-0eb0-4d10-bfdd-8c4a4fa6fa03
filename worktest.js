/*
 * Interactive Post-Integration Pipeline - TEST VERSION
 * 
 * This version allows step-by-step processing with user review and accept/skip decisions.
 * Each step shows a non-destructive auto-stretch preview for evaluation.
 */

#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/MessageBox.jsh>
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Interactive Control --------------------
var INTERACTIVE_MODE = true;
var DEBUG_MODE = true;
var debugStepCounter = 1;

// Test configuration - simplified for testing
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB: true,
  processMonochrome: true,
  ai: { deblur1: true, deblur2: true, stretch: true, denoise: true, starless: true }
};

function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }

function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Interactive_Test_"+ts;
  ensureDir(out); return out;
}

function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }
    
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;
    
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        stepFolder = debugDir;
    }
    
    let baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;
    
    try {
        let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
    }
    
    try {
        let fitsPath = stepFolder + "/" + baseFileName + ".fit";
        win.saveAs(fitsPath, false, false, false, true);
        Console.writeln("DEBUG: Saved FITS: " + fitsPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save FITS: " + e.message);
    }
    
    debugStepCounter++;
}

// -------------------- Non-Destructive Auto-Stretch for Preview --------------------
function applyPreviewStretch(win) {
    Console.writeln("Applying non-destructive auto-stretch for preview...");
    
    try {
        // Apply a simple auto-stretch for preview (non-destructive)
        var stf = new ScreenTransferFunction;
        
        // Use PixInsight's built-in auto-stretch
        stf.executeOn(win.mainView, false); // false = don't replace existing STF
        
        // Force window update
        win.forceClose = false;
        win.show();
        win.zoomToFit();
        
        Console.writeln("Preview stretch applied successfully");
        return true;
    } catch(e) {
        Console.writeln("Preview stretch failed: " + e.message);
        return false;
    }
}

function resetPreviewStretch(win) {
    Console.writeln("Resetting preview stretch...");
    
    try {
        // Reset STF to identity (no stretch)
        win.disableScreenTransferFunctions();
        Console.writeln("Preview stretch reset successfully");
        return true;
    } catch(e) {
        Console.writeln("Preview stretch reset failed: " + e.message);
        return false;
    }
}

// -------------------- Interactive User Decision --------------------
function askUserDecision(stepName, description) {
    if (!INTERACTIVE_MODE) return true; // Auto-accept if not interactive
    
    Console.writeln("\n=== INTERACTIVE DECISION REQUIRED ===");
    Console.writeln("Step: " + stepName);
    Console.writeln("Description: " + description);
    Console.writeln("Please review the preview image and decide:");
    
    var result = MessageBox.question(
        "Step: " + stepName + "\n\n" + 
        description + "\n\n" +
        "The preview shows the result with auto-stretch applied.\n" +
        "The actual processing will be done on linear data.\n\n" +
        "Do you want to ACCEPT this step?",
        "Interactive Processing Decision",
        StdButton_Yes | StdButton_No | StdButton_Cancel
    );
    
    if (result == StdButton_Yes) {
        Console.writeln("User decision: ACCEPT - Step will be applied");
        return true;
    } else if (result == StdButton_No) {
        Console.writeln("User decision: SKIP - Step will be skipped");
        return false;
    } else {
        Console.writeln("User decision: CANCEL - Processing aborted");
        throw new Error("Processing cancelled by user");
    }
}

// -------------------- GraXpert Function (Test Step) --------------------
function runGraXpertInteractive(win, sum, baseTag, debugDir) {
    Console.writeln("\n=== STEP 1: GraXpert Background Extraction ===");
    
    try {
        // Save current state before processing
        debugSave(win, "Before_GraXpert", baseTag, debugDir);
        
        // Apply GraXpert processing
        Console.writeln("Applying GraXpert background extraction...");
        
        var gxp = new GraXpert;
        gxp.targetView = win.mainView.id;
        gxp.correction = GraXpert.prototype.Subtraction;
        gxp.smoothing = 0.964;
        gxp.process();
        
        Console.writeln("GraXpert processing completed");
        
        // Apply preview stretch for user review
        applyPreviewStretch(win);
        
        // Ask user for decision
        var userAccepts = askUserDecision(
            "GraXpert Background Extraction",
            "GraXpert has removed the background gradient.\n" +
            "Review the preview to see if the background extraction looks good.\n" +
            "Look for:\n" +
            "- Even background across the image\n" +
            "- No over-subtraction of nebulosity\n" +
            "- Preserved star colors and brightness"
        );
        
        // Reset preview stretch
        resetPreviewStretch(win);
        
        if (userAccepts) {
            // User accepted - keep the changes
            sum.graxpert = {name: "GraXpert", status: "✅", details: "Applied and accepted by user"};
            debugSave(win, "After_GraXpert_ACCEPTED", baseTag, debugDir);
            Console.writeln("GraXpert step ACCEPTED and applied");
            return true;
        } else {
            // User rejected - we would need to undo the changes
            // For now, we'll just mark it as skipped
            sum.graxpert = {name: "GraXpert", status: "⏭️", details: "Skipped by user decision"};
            debugSave(win, "After_GraXpert_REJECTED", baseTag, debugDir);
            Console.writeln("GraXpert step REJECTED - changes kept but marked as rejected");
            return false;
        }
        
    } catch (e) {
        sum.graxpert = {name: "GraXpert", status: "❌", details: e.message};
        debugSave(win, "After_GraXpert_FAILED", baseTag, debugDir);
        Console.writeln("GraXpert step FAILED: " + e.message);
        return false;
    }
}

// -------------------- Main Test Function --------------------
function testInteractiveProcessing() {
    Console.show();
    Console.writeln("=== Interactive Processing Test ===");
    
    // Get active window
    var win = ImageWindow.activeWindow;
    if (win.isNull) {
        Console.criticalln("No active image window. Please open an image first.");
        return;
    }
    
    Console.writeln("Processing image: " + win.mainView.id);
    
    // Setup directories
    var outputDir = tsFolder("C:/Users/<USER>/OneDrive/Desktop/test/Out");
    var debugDir = outputDir + "/debug";
    ensureDir(debugDir);
    
    // Create base tag from image name
    var baseTag = win.mainView.id.replace(/[^\w\-]/g, "_");
    
    // Initialize summary
    var sum = {};
    
    Console.writeln("Output directory: " + outputDir);
    Console.writeln("Debug directory: " + debugDir);
    Console.writeln("Base tag: " + baseTag);
    
    try {
        // Test Step 1: GraXpert
        var step1Result = runGraXpertInteractive(win, sum, baseTag, debugDir);
        
        // Show results
        Console.writeln("\n=== TEST RESULTS ===");
        Console.writeln("GraXpert: " + sum.graxpert.status + " - " + sum.graxpert.details);
        
        if (step1Result) {
            Console.writeln("✅ Interactive processing test completed successfully!");
        } else {
            Console.writeln("⚠️ Interactive processing test completed with user rejection");
        }
        
        Console.writeln("\nCheck the debug folder for saved images:");
        Console.writeln(debugDir);
        
    } catch (e) {
        Console.criticalln("Interactive processing test failed: " + e.message);
    }
}

// -------------------- Entry Point --------------------
Console.writeln("Interactive Processing Test Script Loaded");
Console.writeln("To start test, run: testInteractiveProcessing()");

// Auto-start the test
testInteractiveProcessing();

})();
