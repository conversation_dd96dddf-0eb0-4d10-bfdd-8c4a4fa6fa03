/*
 * Interactive Post-Integration Pipeline - TEST VERSION
 * 
 * This version allows step-by-step processing with user review and accept/skip decisions.
 * Each step shows a non-destructive auto-stretch preview for evaluation.
 */

#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Interactive Control --------------------
var INTERACTIVE_MODE = true;
var DEBUG_MODE = true;
var debugStepCounter = 1;

// Test configuration - simplified for testing
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB: true,
  processMonochrome: true,
  ai: { deblur1: true, deblur2: true, stretch: true, denoise: true, starless: true }
};

function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }

function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Interactive_Test_"+ts;
  ensureDir(out); return out;
}

function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }
    
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;
    
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        stepFolder = debugDir;
    }
    
    let baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;
    
    try {
        let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
    }
    
    try {
        let fitsPath = stepFolder + "/" + baseFileName + ".fit";
        win.saveAs(fitsPath, false, false, false, true);
        Console.writeln("DEBUG: Saved FITS: " + fitsPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save FITS: " + e.message);
    }
    
    debugStepCounter++;
}

// -------------------- Visual Preview with Auto-Stretch --------------------
function showProcessedImageWithStretch(win, stepName) {
    Console.writeln("Displaying processed image with auto-stretch for review...");

    try {
        // Make sure window is visible and active
        win.show();
        win.bringToFront();
        win.zoomToFit();

        // Apply auto-stretch for visual preview (non-destructive)
        var stf = new ScreenTransferFunction;

        // Compute auto-stretch parameters based on image statistics
        var view = win.mainView;
        var median = view.computeOrFetchProperty("Median");
        var mad = view.computeOrFetchProperty("MAD");

        // Apply simple auto-stretch (similar to clicking the auto-stretch button)
        var n = view.image.isColor ? 3 : 1;
        var stfParams = [];

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826; // Convert to sigma

            // Simple auto-stretch calculation
            var c0 = Math.max(0, med - 2.8 * madVal);
            var m = Math.mtf(0.25, med - c0);

            stfParams[c] = [c0, 1, m, 0, 1];
        }

        // Add alpha channel
        stfParams[3] = [0, 1, 0.5, 0, 1];

        stf.STF = stfParams;
        stf.executeOn(view);

        // Update window title to show it's a preview
        win.mainView.id = win.mainView.id + "_PREVIEW_" + stepName;

        Console.writeln("✅ Image displayed with auto-stretch preview");
        Console.writeln("📺 Review the image on screen - this is how it will look after processing");

        return true;
    } catch(e) {
        Console.writeln("❌ Failed to display preview: " + e.message);
        return false;
    }
}

function resetPreviewStretch(win) {
    Console.writeln("Resetting to linear view...");

    try {
        // Reset STF to identity (back to linear data)
        win.disableScreenTransferFunctions();

        // Reset window title
        var viewId = win.mainView.id;
        if (viewId.indexOf("_PREVIEW_") > -1) {
            win.mainView.id = viewId.substring(0, viewId.indexOf("_PREVIEW_"));
        }

        Console.writeln("✅ Reset to linear view - data unchanged");
        return true;
    } catch(e) {
        Console.writeln("❌ Preview reset failed: " + e.message);
        return false;
    }
}

// -------------------- Interactive User Decision --------------------
function askUserDecision(stepName, description) {
    if (!INTERACTIVE_MODE) return true; // Auto-accept if not interactive

    Console.writeln("\n=== 🔍 VISUAL REVIEW REQUIRED ===");
    Console.writeln("Step: " + stepName);
    Console.writeln("📺 The processed image is now displayed on screen with auto-stretch");
    Console.writeln("👀 Please review the visual result and make your decision");
    Console.writeln("");

    // Use PixInsight's built-in message box
    var result = (new MessageBox(
        "🔍 VISUAL REVIEW: " + stepName + "\n\n" +
        description + "\n\n" +
        "📺 The processed image is currently displayed on screen\n" +
        "    with auto-stretch applied for your review.\n\n" +
        "👀 Please examine the image visually and decide:\n\n" +
        "✅ ACCEPT: Keep this processing step\n" +
        "⏭️ SKIP: Reject this step and continue\n" +
        "❌ CANCEL: Stop processing entirely\n\n" +
        "Note: The linear data remains unchanged until you decide.",
        "Interactive Processing - Visual Review",
        StdIcon_Question,
        StdButton_Yes, StdButton_No, StdButton_Cancel
    )).execute();

    if (result == StdButton_Yes) {
        Console.writeln("✅ User decision: ACCEPT - Step will be kept");
        return true;
    } else if (result == StdButton_No) {
        Console.writeln("⏭️ User decision: SKIP - Step will be rejected");
        return false;
    } else {
        Console.writeln("❌ User decision: CANCEL - Processing aborted");
        throw new Error("Processing cancelled by user");
    }
}

// -------------------- GraXpert Function (Test Step) --------------------
function runGraXpertInteractive(win, sum, baseTag, debugDir) {
    Console.writeln("\n=== STEP 1: GraXpert Background Extraction ===");
    
    try {
        // Save current state before processing
        debugSave(win, "Before_GraXpert", baseTag, debugDir);
        
        // Apply GraXpert processing
        Console.writeln("Applying GraXpert background extraction...");
        
        var gxp = new GraXpert;
        gxp.targetView = win.mainView.id;
        gxp.correction = GraXpert.prototype.Subtraction;
        gxp.smoothing = 0.964;
        gxp.process();
        
        Console.writeln("GraXpert processing completed");

        // Show the processed image with auto-stretch for visual review
        showProcessedImageWithStretch(win, "GraXpert");

        // Ask user for decision while they can see the result
        var userAccepts = askUserDecision(
            "GraXpert Background Extraction",
            "GraXpert has removed the background gradient.\n\n" +
            "👀 Look at the displayed image and check for:\n" +
            "  • Even background across the image\n" +
            "  • No over-subtraction of nebulosity\n" +
            "  • Preserved star colors and brightness\n" +
            "  • Smooth gradient removal without artifacts"
        );

        // Reset to linear view after decision
        resetPreviewStretch(win);
        
        if (userAccepts) {
            // User accepted - keep the changes
            sum.graxpert = {name: "GraXpert", status: "✅", details: "Applied and accepted by user"};
            debugSave(win, "After_GraXpert_ACCEPTED", baseTag, debugDir);
            Console.writeln("GraXpert step ACCEPTED and applied");
            return true;
        } else {
            // User rejected - we would need to undo the changes
            // For now, we'll just mark it as skipped
            sum.graxpert = {name: "GraXpert", status: "⏭️", details: "Skipped by user decision"};
            debugSave(win, "After_GraXpert_REJECTED", baseTag, debugDir);
            Console.writeln("GraXpert step REJECTED - changes kept but marked as rejected");
            return false;
        }
        
    } catch (e) {
        sum.graxpert = {name: "GraXpert", status: "❌", details: e.message};
        debugSave(win, "After_GraXpert_FAILED", baseTag, debugDir);
        Console.writeln("GraXpert step FAILED: " + e.message);
        return false;
    }
}

// -------------------- File Selection GUI --------------------
function selectInputFiles() {
    var dialog = new OpenFileDialog;
    dialog.multipleSelections = true;
    dialog.caption = "Select Images for Interactive Processing";
    dialog.loadImageFilters();

    if (dialog.execute()) {
        return dialog.fileNames;
    }
    return null;
}

function selectOutputDirectory() {
    var dialog = new GetDirectoryDialog;
    dialog.caption = "Select Output Directory";
    dialog.initialPath = "C:/Users/<USER>/OneDrive/Desktop/test/Out";

    if (dialog.execute()) {
        return dialog.directory;
    }
    return null;
}

// -------------------- Simple Accept/Skip Dialog --------------------
function simpleAcceptDialog(stepName) {
    Console.writeln("\n🔍 STEP REVIEW: " + stepName);
    Console.writeln("📺 Check the image window - it shows the processed result with auto-stretch");

    var result = (new MessageBox(
        "Step: " + stepName + "\n\n" +
        "The processed image is displayed with auto-stretch.\n" +
        "Please review the result visually.\n\n" +
        "Do you want to ACCEPT this step?",
        "Accept or Skip Step",
        StdIcon_Question,
        StdButton_Yes, StdButton_No
    )).execute();

    if (result == StdButton_Yes) {
        Console.writeln("✅ ACCEPTED: " + stepName);
        return true;
    } else {
        Console.writeln("⏭️ SKIPPED: " + stepName);
        return false;
    }
}

// -------------------- Main Interactive Processing --------------------
function processOneFileInteractive(inputFile, outputDir) {
    Console.writeln("\n=== Processing: " + File.extractName(inputFile) + " ===");

    // Open the file
    var windows = ImageWindow.open(inputFile);
    if (windows.length === 0) {
        Console.criticalln("Failed to open: " + inputFile);
        return false;
    }

    var win = windows[0];
    win.show();
    win.bringToFront();
    win.zoomToFit();

    Console.writeln("✅ Image opened: " + win.mainView.id);

    // Setup directories
    var debugDir = outputDir + "/debug";
    ensureDir(debugDir);

    // Create base tag
    var baseTag = File.extractName(inputFile).replace(/[^\w\-]/g, "_");
    var sum = {};

    try {
        // STEP 1: GraXpert Background Extraction
        Console.writeln("\n🔄 STEP 1: Applying GraXpert...");

        // Apply GraXpert
        var gxp = new GraXpert;
        gxp.targetView = win.mainView.id;
        gxp.correction = GraXpert.prototype.Subtraction;
        gxp.smoothing = 0.964;
        gxp.process();

        Console.writeln("✅ GraXpert processing completed");

        // Show with auto-stretch for review
        showProcessedImageWithStretch(win, "GraXpert");

        // Simple accept/skip dialog
        var acceptStep1 = simpleAcceptDialog("GraXpert Background Extraction");

        // Reset to linear view
        resetPreviewStretch(win);

        if (acceptStep1) {
            sum.graxpert = {name: "GraXpert", status: "✅", details: "Accepted by user"};
            debugSave(win, "Step1_GraXpert_ACCEPTED", baseTag, debugDir);
        } else {
            sum.graxpert = {name: "GraXpert", status: "⏭️", details: "Skipped by user"};
            debugSave(win, "Step1_GraXpert_SKIPPED", baseTag, debugDir);
            // TODO: Here we would undo the GraXpert changes if possible
        }

        // TODO: Add more steps here (Step 2, Step 3, etc.)
        Console.writeln("\n📋 PROCESSING SUMMARY:");
        Console.writeln("  Step 1 - GraXpert: " + sum.graxpert.status + " " + sum.graxpert.details);

        // Save final result
        var finalPath = outputDir + "/Interactive_" + baseTag + ".xisf";
        win.saveAs(finalPath, false, false, false, false);
        Console.writeln("💾 Final result saved: " + finalPath);

        return true;

    } catch (e) {
        Console.criticalln("❌ Processing failed: " + e.message);
        return false;
    }
}

// -------------------- Main Entry Point --------------------
function main() {
    Console.show();
    Console.writeln("=== Interactive Post-Processing Pipeline ===");
    Console.writeln("This version pauses after each step for user review");

    // Select input files
    var inputFiles = selectInputFiles();
    if (!inputFiles || inputFiles.length === 0) {
        Console.writeln("❌ No files selected. Exiting.");
        return;
    }

    // Select output directory
    var outputDir = selectOutputDirectory();
    if (!outputDir) {
        Console.writeln("❌ No output directory selected. Exiting.");
        return;
    }

    Console.writeln("📁 Output directory: " + outputDir);
    Console.writeln("📄 Processing " + inputFiles.length + " file(s)");

    // Process each file
    for (var i = 0; i < inputFiles.length; i++) {
        Console.writeln("\n" + "=".repeat(60));
        Console.writeln("📄 File " + (i + 1) + " of " + inputFiles.length);

        var success = processOneFileInteractive(inputFiles[i], outputDir);

        if (!success) {
            Console.writeln("⚠️ Processing failed for: " + File.extractName(inputFiles[i]));
        }
    }

    Console.writeln("\n🎉 Interactive processing completed!");
    Console.writeln("📁 Check output directory: " + outputDir);
}

// Start the interactive processing
main();

})();
