// =================================================================
// PixInsight Script to Load R, G, B XISF Images Based on FILTER Header
// With Debug Output
// =================================================================

#include <pjsr/DataType.jsh>

function getFilterName(filePath) {
    var file = new File;
    if (!file.openForReading(filePath)) {
        console.warningln("Cannot open file: " + filePath);
        return "";
    }

    var header = file.readFITSHeader();
    file.close();

    // Search for FILTER keyword case-insensitively
    var match = header.match(/FILTER\s*=\s*'([^']+)'/i);
    if (match && match.length > 1)
        return match[1].trim();
    else
        return "";
}

function loadImageByFilter(folder, targetFilter) {
    var files = new Array;
    var ff = new FileFind;

    if (ff.begin(folder + "/*.xisf")) {
        do {
            files.push(folder + "/" + ff.name);
        } while (ff.next());
        ff.end();
    } else {
        console.criticalln("No .xisf files found in directory: " + folder);
        return null;
    }

    for (var i = 0; i < files.length; ++i) {
        var filePath = files[i];
        var filter = getFilterName(filePath);

        console.writeln("Inspecting file: " + filePath);
        console.writeln("Detected FILTER = '" + filter + "'");

        if (filter && filter.toLowerCase().indexOf(targetFilter.toLowerCase()) !== -1) {
            console.writeln("Loading matched image for filter '" + targetFilter + "'");
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0)
                return windowArray[0];
            else
                console.warningln("Failed to load window for file: " + filePath);
        }
    }

    console.criticalln("No matching image found for filter: " + targetFilter);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("Searching directory: " + inputDir);

    console.writeln("Loading Red image...");
    var redWindow = loadImageByFilter(inputDir, "R");

    console.writeln("Loading Green image...");
    var greenWindow = loadImageByFilter(inputDir, "G");

    console.writeln("Loading Blue image...");
    var blueWindow = loadImageByFilter(inputDir, "B");

    if (!redWindow || !greenWindow || !blueWindow) {
        console.criticalln("One or more RGB images failed to load.");
        return;
    }

    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    var combo = new ChannelCombination;
    combo.channels = [
        [redView],
        [greenView],
        [blueView]
    ];

    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,
        BitDepth_IEEE32,
        true,
        false,
        "Combined_RGB_Image"
    );

    combo.executeOn(outputImage.mainView);
    outputImage.show();

    console.writeln("RGB channels successfully combined.");
}

main();
