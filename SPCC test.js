/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * FINAL VERSION
 * - Uses an absolute path for the GraXpert library to ensure it is found.
 * - Uses a direct library call to GraXpert for maximum reliability.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- UPDATED: Using the absolute path to the GraXpert library ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Debugger Control --------------------
var DEBUG_MODE = true;
var debugStepCounter = 1;

// ... (The rest of the script is the same) ...
var STF_UNLINKED        = true;
var STF_K_SHADOWS       = -3.0;
var STF_TARGET_BG       = 0.22;
var STF_HIGHLIGHTS      = 1.0;
var STF_MAX_SAMPLES     = 120000;
var outputExtension = ".xisf";
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}
function debugPause(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Pause Warning: Invalid window provided for step '", stepName, "'");
        return;
    }
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let fileName = counterStr + "_" + baseTag + "_" + sanitizedStepName + ".fit";
    let filePath = debugDir + "/" + fileName;
    debugStepCounter++;
    Console.writeln("DEBUG: Saving intermediate file: ", filePath);
    win.saveAs(filePath, false, false, false, true);
    var msgBox = new MessageBox( "<h2>Debug Pause</h2><p><b>Paused after:</b> " + stepName + "</p><p>A snapshot has been saved as a FITS file in the 'debug' subfolder:</p><p><b>" + fileName + "</b></p><p>Click <b>Continue</b> to proceed, or <b>Stop</b> to abort the script.</p>", "Debug Pause", StdIcon_Information, StdButton_Ok, StdButton_Cancel );
    msgBox.okButtonText = "Continue";
    msgBox.cancelButtonText = "Stop";
    if (msgBox.execute() == StdButton_Cancel) {
        throw new Error("Script execution aborted by user after '" + stepName + "'.");
    }
}
function startConsoleLog() {
    let path = File.systemTempDirectory + "/pixinsight_console_log_" + Date.now() + ".txt";
    let P = new ProcessInstance("Console");
    P.arg = "-r=" + path;
    P.executeGlobal();
    return path;
}
function stopConsoleLog() {
    let P = new ProcessInstance("Console");
    P.arg = "-r=";
    P.executeGlobal();
}
function checkLogForFailure(filePath, searchText) {
    if (!filePath || !File.exists(filePath)) return false;
    var failureFound = false;
    try {
      var logFile = new File;
      logFile.openForReading(filePath);
      if (logFile.isOpen) {
        var s = logFile.read(DataType_ByteArray);
        logFile.close();
        var logContent = s.toString();
        if (logContent.indexOf(searchText) !== -1) failureFound = true;
      }
    } catch(e) {
      Console.writeln("Warning: Could not read console log file: " + e.message);
    } finally {
      try { File.remove(filePath); } catch(_) {}
    }
    return failureFound;
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}
function saveAs16BitTiff(win, path) {
  var tifPath = File.changeExtension(path, ".tif");
  win.saveAs(tifPath, false, false, true, false);
  Console.writeln("  Saved 16-bit TIFF: ", File.extractName(tifPath));
}
function findAllInputImages(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[];
  var ff=new FileFind;
  var supportedExtensions = [".xisf", ".fit", ".fits", ".tif", ".tiff"];
  if(ff.begin(dir+"/*.*")){
    do {
      var nameLower = ff.name.toLowerCase();
      for (var i = 0; i < supportedExtensions.length; ++i) {
        if (nameLower.endsWith(supportedExtensions[i])) {
          v.push(dir+"/"+ff.name);
          break;
        }
      }
    } while(ff.next());
  }
  return v;
}
function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /filter-r(?![a-z])/.test(s) || /\bred\b/.test(s)) return "R";
  if (s.indexOf("filter-green")>=0 || /filter-g(?![a-z])/.test(s) || /\bgreen\b/.test(s)) return "G";
  if (s.indexOf("filter-blue")>=0 || /filter-b(?![a-z])/.test(s) || /\bblue\b/.test(s)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}
function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }
  var haveR=!!(byFilter.R&&byFilter.R.length), haveG=!!(byFilter.G&&byFilter.G.length), haveB=!!(byFilter.B&&byFilter.B.length);
  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };
  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }
  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k, isStackedRGB: false });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) {
    var filePath = unknownSingles[k];
    var isColorStack = false;
    var tempWinArr = ImageWindow.open(filePath);
    if (tempWinArr.length > 0) {
        var tempWin = tempWinArr[0];
        if (tempWin.mainView.image.isColor) isColorStack = true;
        tempWin.forceClose();
    }
    plan.singles.push({ path: filePath, tag: isColorStack ? "Color" : "Single", isStackedRGB: isColorStack });
  }
  return plan;
}
function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}
function finalABE(win, sum){
  try{
    Console.writeln("\n=== Running Automatic Background Extractor (ABE) ===");
    var P = new AutomaticBackgroundExtractor;
    P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050; P.maxBackground = 1.0000; P.minBackground = 0.0000; P.useBrightnessLimits = false; P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5; P.modelImageSampleFormat = AutomaticBackgroundExtractor.prototype.f32; P.abeDownsample = 2.00; P.writeSampleBoxes = false; P.justTrySamples = false;
    P.targetCorrection = AutomaticBackgroundExtractor.prototype.Subtraction; P.normalize = true; P.replaceTarget = true; P.discardModel = true;
    P.correctedImageId = ""; P.correctedImageSampleFormat = AutomaticBackgroundExtractor.prototype.SameAsTarget; P.verboseCoefficients = false; P.compareModel = false; P.compareFactor = 10.00;
    P.executeOn(win.mainView);
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE with custom settings applied"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}
function runGradientCorrection(win, sum) {
    try {
        Console.writeln("\n=== Running GradientCorrection Tool ===");
        var P = new GradientCorrection;
        P.reference = 0.50; P.lowThreshold = 0.20; P.lowTolerance = 0.50; P.highThreshold = 0.05; P.highTolerance = 0.00; P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.protectionThreshold = 0.10; P.protectionAmount = 0.50; P.protectionSmoothingFactor = 16; P.lowClippingLevel = 0.000076; P.automaticConvergence = true; P.convergenceLimit = 0.00001000; P.maxIterations = 10; P.useSimplification = true; P.simplificationDegree = 1; P.simplificationScale = 1024; P.generateSimpleModel = false; P.generateGradientModel = false; P.generateProtectionMasks = false; P.gridSamplingDelta = 16;
        P.executeOn(win.mainView);
        sum.gradientCorrection = {name: "GradientCorrection", status: "✅", details: "Applied with custom settings"};
    } catch (e) {
        sum.gradientCorrection = {name: "GradientCorrection", status: "❌", details: e.message};
    }
}
function runGraXpert(win, sum) {
    try {
        Console.writeln("\n=== Running GraXpert via Library Call ===");
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0;
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = win.mainView;
        gxp.process();
        sum.graxpert = {name: "GraXpert", status: "✅", details: "Applied via library call"};
    } catch (e) {
        sum.graxpert = {name: "GraXpert", status: "❌", details: e.message};
    }
}
function autoBlackPoint(win, sum){try{var img = win.mainView.image;if (!img.readSamples) {var AH_fallback = new AutoHistogram(); AH_fallback.auto=true; AH_fallback.clipLow=0.1; AH_fallback.clipHigh=0.1; AH_fallback.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};return true;}if(!img.isColor || img.numberOfChannels<3){var AH=new AutoHistogram(); AH.auto=true; AH_fallback.clipLow=0.1; AH_fallback.clipHigh=0.1; AH_fallback.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"AutoHistogram (mono/NB)"}; return true;}var w=img.width,h=img.height, sampleSize=20, numSamples=20;var rs=[],gs=[],bs=[];for(var i=0;i<numSamples;++i){var x=Math.floor(Math.random()*(w-sampleSize)), y=Math.floor(Math.random()*(h-sampleSize));var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);}rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0; for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; } R/=n; G/=n; B/=n;try{var L=new Levels(); L.redBlack=Math.min(R*0.8,0.02); L.greenBlack=Math.min(G*0.9,0.03); L.blueBlack=Math.min(B*0.8,0.02);L.redWhite=0.98; L.greenWhite=0.97; L.blueWhite=0.98; L.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"Levels"};}catch(e2){var AH2=new AutoHistogram(); AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1; AH2.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};}return true;}catch(e){ sum.blackPoint={name:"Black Point",status:"❌",details:e.message}; return false; }}
function solveImage(win, sum){try{Console.writeln("\n=== ImageSolver (for SPCC) ===");var solver=new ImageSolver;try{solver.silent=true;}catch(_){}try{solver.showInterface=false;}catch(_){}try{solver.forceDialog=false;}catch(_){}try{solver.noGUIMode=true;}catch(_){}try{solver.noGUIMessages=true;}catch(_){}solver.Init(win,false);solver.useMetadata=true;solver.catalog="GAIA DR3";solver.useDistortionCorrection=false;solver.generateErrorMaps=false;solver.showStars=false;solver.showDistortion=false;solver.generateDistortionMaps=false;solver.sensitivity=0.1;if(!solver.SolveImage(win)){Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");solver.useMetadata=false;if(!solver.SolveImage(win))throw new Error("Plate solution not found.");}sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};return true;}catch(e){sum.solver={name:"ImageSolver",status:"❌",details:e.message};return false;}}
// profileSelector: "OSC" or "RGB" (curves/filters as provided)
function performSPCC(win, sum, profileSelector){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — Applying Detailed Manual Profile ===");
    var P = new SpectrophotometricColorCalibration;

    // --- Using your detailed manual parameters ---
    P.applyCalibration = true;
    P.neutralizeBackground = true;
    P.backgroundLow = -2.80;
    P.backgroundHigh = 2.00;

    // White Reference (Galaxy)
    P.whiteReferenceName = "Average Spiral Galaxy";
    P.whiteReferenceSpectrum = "200.5,0.0715066,201.5,0.0689827,202.5,0.0720216,203.5,0.0685511,204.5,0.0712370,205.5,0.0680646,206.5,0.0683024,207.4,0.0729174,207.8,0.0702124,208.5,0.0727025,209.5,0.0688880,210.5,0.0690528,211.5,0.0697566,212.5,0.0705508,213.5,0.0654581,214.5,0.0676317,215.5,0.0699038,216.5,0.0674922,217.5,0.0668344,218.5,0.0661763,219.5,0.0690803,220.5,0.0670864,221.5,0.0635644,222.5,0.0619833,223.5,0.0668687,224.5,0.0640725,225.5,0.0614358,226.5,0.0628698,227.5,0.0649014,228.5,0.0673391,229.5,0.0638038,230.5,0.0643234,231.5,0.0614849,232.5,0.0493110,233.5,0.0574873,234.5,0.0555616,235.5,0.0609369,236.5,0.0557384,237.5,0.0578991,238.5,0.0536321,239.5,0.0575370,240.5,0.0555389,241.5,0.0571506,242.5,0.0615309,243.5,0.0595363,244.5,0.0634798,245.5,0.0628886,246.5,0.0622975,247.5,0.0600475,248.5,0.0608933,249.5,0.0580972,250.5,0.0653082,251.3,0.0576207,251.8,0.0588533,252.5,0.0566401,253.5,0.0582714,254.5,0.0575809,255.5,0.0633762,256.5,0.0610093,257.5,0.0652874,258.5,0.0642648,259.5,0.0632596,260.5,0.0609384,261.5,0.0600490,262.5,0.0636409,263.5,0.0682040,264.5,0.0754600,265.5,0.0806341,266.5,0.0699754,267.5,0.0739405,268.5,0.0755243,269.5,0.0697483,270.5,0.0736132,271.5,0.0678854,272.5,0.0663086,273.5,0.0709825,274.5,0.0602999,275.5,0.0630128,276.5,0.0669431,277.5,0.0701399,278.5,0.0641577,279.5,0.0511231,280.5,0.0550197,281.5,0.0692974,282.5,0.0753517,283.5,0.0723537,284.5,0.0679725,285.5,0.0634174,286.5,0.0742486,287.5,0.0783316,288.5,0.0771108,289.5,0.0801337,291,0.0914252,293,0.0862422,295,0.0838485,297,0.0858467,299,0.0865643,301,0.0875161,303,0.0893837,305,0.0905257,307,0.0935800,309,0.0934870,311,0.0982195,313,0.0953176,315,0.0961554,317,0.0995933,319,0.0924967,321,0.0978345,323,0.0907337,325,0.1054383,327,0.1143168,329,0.1135342,331,0.1106139,333,0.1119505,335,0.1099062,337,0.0967928,339,0.1022504,341,0.1039447,343,0.1063681,345,0.1091599,347,0.1109753,349,0.1181664,351,0.1232860,353,0.1163073,355,0.1267769,357,0.1035215,359,0.1042786,361,0.1176823,363,0.1219479,364,0.1250342,365,0.1363934,367,0.1407033,369,0.1288466,371,0.1379791,373,0.1127623,375,0.1318217,377,0.1528880,379,0.1670432,381,0.1727864,383,0.1243124,385,0.1639393,387,0.1724457,389,0.1520460,391,0.2043430,393,0.1427526,395,0.1870668,397,0.1244026,399,0.2329267,401,0.2556144,403,0.2542109,405,0.2491356,407,0.2379803,409,0.2541684,411,0.2279309,413,0.2533629,415,0.2557223,417,0.2584198,419,0.2560216,421,0.2587210,423,0.2498130,425,0.2609755,427,0.2495886,429,0.2412927,431,0.2182856,433,0.2579985,435,0.2483036,437,0.2928112,439,0.2713431,441,0.2828921,443,0.2975108,445,0.3012513,447,0.3161393,449,0.3221464,451,0.3585586,453,0.3219299,455,0.3334392,457,0.3568741,459,0.3412296,461,0.3498501,463,0.3424920,465,0.3478877,467,0.3611478,469,0.3560448,471,0.3456585,473,0.3587672,475,0.3690553,477,0.3657369,479,0.3671625,481,0.3666357,483,0.3761265,485,0.3466382,487,0.3121751,489,0.3651561,491,0.3688824,493,0.3627420,495,0.3786295,497,0.3733906,499,0.3510300,501,0.3338136,503,0.3540298,505,0.3527861,507,0.3680833,509,0.3507047,511,0.3597249,513,0.3486136,515,0.3372089,517,0.3152444,519,0.3257755,521,0.3499922,523,0.3744245,525,0.3907778,527,0.3490228,529,0.3972061,531,0.4203442,533,0.3740999,535,0.4084084,537,0.4070036,539,0.3993480,541,0.3942389,543,0.4010466,545,0.4128880,547,0.4055525,549,0.4094232,551,0.4053814,553,0.4201633,555,0.4269231,557,0.4193749,559,0.4105311,561,0.4257824,563,0.4239540,565,0.4310873,567,0.4218358,569,0.4360353,571,0.4229342,573,0.4583894,575,0.4425389,577,0.4481210,579,0.4320856,581,0.4507180,583,0.4645862,585,0.4513373,587,0.4516404,589,0.4033701,591,0.4466167,593,0.4513267,595,0.4524209,597,0.4613319,599,0.4546841,601,0.4499895,603,0.4631190,605,0.4724762,607,0.4724962,609,0.4569794,611,0.4599737,613,0.4363290,615,0.4488329,617,0.4267759,619,0.4545143,621,0.4514890,623,0.4384229,625,0.4256613,627,0.4470943,629,0.4565981,631,0.4458333,633,0.4533333,635,0.4546457,637,0.4535446,639,0.4638791,641,0.4561002,643,0.4617287,645,0.4594083,647,0.4597119,649,0.4517238,651,0.4686735,653,0.4686423,655,0.4544898,657,0.4255737,659,0.4640177,661,0.4711876,663,0.4679153,665,0.4689913,667,0.4592265,669,0.4668144,671,0.4498947,673,0.4629239,675,0.4559567,677,0.4596584,679,0.4549789,681,0.4586439,683,0.4653622,685,0.4543475,687,0.4632128,689,0.4711164,691,0.4709973,693,0.4685415,695,0.4696455,697,0.4769241,699,0.4760169,701,0.4701294,703,0.4815669,705,0.4850302,707,0.4707895,709,0.4570604,711,0.4465777,713,0.4382957,715,0.4379654,717,0.4446168,719,0.4350767,721,0.4466714,723,0.4579113,725,0.4625222,727,0.4669903,729,0.4615551,731,0.4763299,733,0.4793147,735,0.4857778,737,0.4997366,739,0.4915129,741,0.4926212,743,0.5062475,745,0.5072637,747,0.5170334,749,0.5173594,751,0.5244106,753,0.5344788,755,0.5397524,757,0.5387203,759,0.5280215,761,0.5191969,763,0.5085395,765,0.4984095,767,0.4749347,769,0.4878839,771,0.4798119,773,0.4821991,775,0.4799906,777,0.4870453,779,0.4928744,781,0.4934236,783,0.4904677,785,0.4849491,787,0.4947343,789,0.4890020,791,0.4789132,793,0.4822390,795,0.4795733,797,0.4973323,799,0.4988779,801,0.5054210,803,0.5087054,805,0.5103235,807,0.5187602,809,0.5151330,811,0.5223530,813,0.5396030,815,0.5475528,817,0.5543915,819,0.5380259,821,0.5321401,823,0.5366753,825,0.5372011,827,0.5440262,829,0.5390591,831,0.5212784,833,0.5187033,835,0.5197124,837,0.5241092,839,0.5070799,841,0.5253056,843,0.5003658,845,0.4896143,847,0.4910508,849,0.4964088,851,0.4753377,853,0.4986498,855,0.4604553,857,0.5174022,859,0.5105171,861,0.5175606,863,0.5322153,865,0.5335880,867,0.4811849,869,0.5241390,871,0.5458069,873,0.5508025,875,0.5423946,877,0.5580108,879,0.5677047,881,0.5580099,883,0.5649928,885,0.5629494,887,0.5384574,889,0.5523318,891,0.5614248,893,0.5521309,895,0.5550786,897,0.5583751,899,0.5597844,901,0.5394855,903,0.5638478,905,0.5862635,907,0.5877920,909,0.5774965,911,0.5866240,913,0.5989106,915,0.5958623,917,0.5964975,919,0.6041389,921,0.5797449,923,0.5607401,925,0.5640816,927,0.5704267,929,0.5642119,931,0.5694372,933,0.5716141,935,0.5705180,937,0.5618458,939,0.5736730,941,0.5630236,943,0.5796418,945,0.5720721,947,0.5873186,949,0.5896322,951,0.5794164,953,0.5828271,955,0.5692468,957,0.5808756,959,0.5949017,961,0.5875516,963,0.5923656,965,0.5824188,967,0.5838008,969,0.5948942,971,0.5865689,973,0.5818128,975,0.5807992,977,0.5851036,979,0.5775164,981,0.5938626,983,0.5885816,985,0.5943664,987,0.5911885,989,0.5916490,991,0.5868101,993,0.5919505,995,0.5945270,997,0.5960248,999,0.5950870,1003,0.5948938,1007,0.5888742,1013,0.6006343,1017,0.5958836,1022,0.6004154,1028,0.6050616,1032,0.5995678,1038,0.5984462,1043,0.6035475,1048,0.5973678,1052,0.5940806,1058,0.5854267,1063,0.5827191,1068,0.5788137,1072,0.5843356,1078,0.5830553,1082,0.5762549,1087,0.5766769,1092,0.5759526,1098,0.5726978,1102,0.5718654,1108,0.5658845,1113,0.5661672,1117,0.5637793,1122,0.5660178,1128,0.5608876,1133,0.5622964,1138,0.5603359,1143,0.5563605,1147,0.5652205,1153,0.5656560,1157,0.5607483,1162,0.5540304,1167,0.5556068,1173,0.5604768,1177,0.5492890,1183,0.5464411,1187,0.5385652,1192,0.5489344,1198,0.5331419,1203,0.5451093,1207,0.5419047,1212,0.5443417,1218,0.5477119,1223,0.5460783,1227,0.5435469,1232,0.5413216,1237,0.5419156,1243,0.5360791,1248,0.5363784,1253,0.5330056,1258,0.5330475,1262,0.5312735,1267,0.5282075,1272,0.5301258,1278,0.5318302,1283,0.5143390,1288,0.5259125,1292,0.5214670,1298,0.5287547,1302,0.5231621,1308,0.5267800,1313,0.5167545,1318,0.5170787,1323,0.5186867,1328,0.5111090,1332,0.5122823,1338,0.5085013,1343,0.5118057,1347,0.5086671,1352,0.5063367,1357,0.5007655,1363,0.5001648,1367,0.5036531,1373,0.5066053,1377,0.5064235,1382,0.5083958,1388,0.5053201,1393,0.4855558,1397,0.4835752,1402,0.4799809,1408,0.4854351,1412,0.4802711,1418,0.4867642,1423,0.4831264,1428,0.4768633,1433,0.4864127,1438,0.4916220,1442,0.4807589,1448,0.4908799,1452,0.4878666,1457,0.4919060,1462,0.4832121,1467,0.4817380,1472,0.4788120,1477,0.4832511,1483,0.4873623,1488,0.4833546,1492,0.4970729,1498,0.4941945,1503,0.4882672,1507,0.4906435,1512,0.5011545,1517,0.5042579,1522,0.5053326,1528,0.5103188,1533,0.5104235,1537,0.5109443,1543,0.5088747,1548,0.5114602,1552,0.5078479,1557,0.4955375,1562,0.5020681,1567,0.5009384,1572,0.5130484,1578,0.4843262,1583,0.4878957,1587,0.4869790,1593,0.5039261,1598,0.4961504,1605,0.5016433,1615,0.5109383,1625,0.5010374,1635,0.5166810,1645,0.4997573,1655,0.5132085,1665,0.5045445,1675,0.5038381,1685,0.4979366,1695,0.5024966,1705,0.4946397,1715,0.4900714,1725,0.4820987,1735,0.4704836,1745,0.4675962,1755,0.4610580,1765,0.4542064,1775,0.4442880,1785,0.4394009,1795,0.4305704,1805,0.4214249,1815,0.4154385,1825,0.4121445,1835,0.4087068,1845,0.4004347,1855,0.3981439,1865,0.3898276,1875,0.3819086,1885,0.3837946,1895,0.3719080,1905,0.3783857,1915,0.3734775,1925,0.3706359,1935,0.3625896,1945,0.3552610,1955,0.3559292,1965,0.3516581,1975,0.3442642,1985,0.3424439,1995,0.3401458,2005,0.3400624,2015,0.3370426,2025,0.3310865,2035,0.3294150,2045,0.3300824,2055,0.3263510,2065,0.3238343,2075,0.3226433,2085,0.3196882,2095,0.3156795,2105,0.3170735,2115,0.3129192,2125,0.3107151,2135,0.3111934,2145,0.3083829,2155,0.3053164,2165,0.3011248,2175,0.2987932,2185,0.2973707,2195,0.2953015,2205,0.2894185,2215,0.2910636,2225,0.2855524,2235,0.2835412,2245,0.2813240,2255,0.2794243,2265,0.2746838,2275,0.2752567,2285,0.2700351,2295,0.2315953,2305,0.2464873,2315,0.2460988,2325,0.2138361,2335,0.2290047,2345,0.2216595,2355,0.1997312,2365,0.2151513,2375,0.2079374,2385,0.1903472,2395,0.2020694,2405,0.1988067,2415,0.1834113,2425,0.1912983,2435,0.1873909,2445,0.1783537,2455,0.1759682,2465,0.1784857,2475,0.1715942,2485,0.1573562,2495,0.1568707,2505,0.1598265";

    // Other settings
    P.catalogId = "GaiaDR3SP";
    P.autoLimitMagnitude = true;
    P.psfStructureLayers = 5;
    P.saturationThreshold = 0.75;
    P.psfMinSNR = 40.00;

    // Output options
    P.generateGraphs = true; // Set to true to always show graphs
    P.generateStarMaps = false;
    P.generateTextFiles = false;

    // --- Execution ---
    logPath = startConsoleLog();
    P.executeOn(win.mainView);
    stopConsoleLog();
    if (checkLogForFailure(logPath, "<* failed *>")) {
        throw new Error("SPCC failed internally. See console log for details like 'Insufficient data'.");
    }
    sum.spcc = { name: "SPCC", status: "✅", details: "Applied with detailed manual profile" };
    return true;
} catch (e) {
    sum.spcc = { name: "SPCC", status: "❌", details: e.message };
    return false;
}
}
function deblur2(win, sum){try{var P=new BlurXTerminator;P.sharpenStars=0.25;P.adjustStarHalos=0.00;P.psfDiameter=0.00;P.sharpenNonstellar=0.90;P.autoPSF=true;P.correctOnly=false;P.correctFirst=false;P.nonstellarThenStellar=false;P.luminanceOnly=false;P.executeOn(win.mainView);sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};return true;}catch(e){sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message};return false;}}
function denoise(win, sum){try{var P=new NoiseXTerminator;P.intensityColorSeparation=false;P.frequencySeparation=false;P.denoise=0.90;P.iterations=2;P.executeOn(win.mainView);sum.denoising={name:"Denoising",status:"✅",details:"Applied"};return true;}catch(e){sum.denoising={name:"Denoising",status:"❌",details:e.message};return false;}}
function clamp(x,a,b){ return x<a?a:(x>b?b:x); }
function median(v){ var n=v.length; if(!n) return 0; v.sort(function(a,b){return a-b;}); return (n&1)? v[(n-1)>>1] : 0.5*(v[n>>1]+v[(n>>1)-1]); }
function mad(v, med){ var a=new Array(v.length); for (var i=0;i<v.length;++i) a[i]=Math.abs(v[i]-med); return median(a); }
function sampleChannel(img, ch){var W=img.width, H=img.height, tot=W*H;var step=Math.max(1,Math.floor(Math.sqrt(tot/STF_MAX_SAMPLES)));var vals=[];if(img.isColor){for(var y=0;y<H;y+=step)for(var x=0;x<W;x+=step)vals.push(img.sample(x,y,ch));}else{for(var y=0;y<H;y+=step)for(var x=0;x<W;x+=step)vals.push(img.sample(x,y));}return vals;}
function sampleLuminance(img){var W=img.width,H=img.height,tot=W*H;var step=Math.max(1,Math.floor(Math.sqrt(tot/STF_MAX_SAMPLES)));var vals=[];for(var y=0;y<H;y+=step){for(var x=0;x<W;x+=step){if(img.isColor){var r=img.sample(x,y,0),g=img.sample(x,y,1),b=img.sample(x,y,2);vals.push(0.2126*r+0.7152*g+0.0722*b);}else vals.push(img.sample(x,y));}}return vals;}
function mtfMidtonesFromTarget(normBg,target){normBg=clamp(normBg,1e-6,0.999999);target=clamp(target,1e-6,0.999999);return clamp(Math.exp(Math.log(0.5)*Math.log(normBg)/Math.log(target)),0.001,0.999);}
function computeSTF(img){var s=[0,0,0],m=[0.5,0.5,0.5],h=[1,1,1];if(img.isColor&&STF_UNLINKED){for(var c=0;c<3;++c){var v=sampleChannel(img,c);var med=median(v);var sig=1.4826*mad(v,med);var sc=clamp(med+STF_K_SHADOWS*sig,0,0.99);var hc=STF_HIGHLIGHTS;var B=clamp((med-sc)/(hc-sc),1e-6,0.999999);var mc=mtfMidtonesFromTarget(B,STF_TARGET_BG);s[c]=sc;m[c]=mc;h[c]=hc;}}else{var v=sampleLuminance(img);var med=median(v);var sig=1.4826*mad(v,med);var sc=clamp(med+STF_K_SHADOWS*sig,0,0.99);var hc=STF_HIGHLIGHTS;var B=clamp((med-sc)/(hc-sc),1e-6,0.999999);var mc=mtfMidtonesFromTarget(B,STF_TARGET_BG);s=[sc,sc,sc];m=[mc,mc,mc];h=[hc,hc,hc];}return {s:s,m:m,h:h};}
function applyHT(view,stf){var P=new HistogramTransformation;P.H=[[stf.s[0],stf.m[0],stf.h[0],0,1],[stf.s[1],stf.m[1],stf.h[1],0,1],[stf.s[2],stf.m[2],stf.h[2],0,1],[stf.s[0],stf.m[0],stf.h[0],0,1]];P.executeOn(view);}
function stretchImageAtStage(inPath,outPath,saveFlag){var w=ImageWindow.open(inPath);if(!w.length)return null;var win=w[0],img=win.mainView.image;var stf=computeSTF(img);applyHT(win.mainView,stf);if(saveFlag)saveAs16BitTiff(win,outPath);return win;}
function applyColorEnhanceToView(view){var C=new CurvesTransformation;C.R=[[0,0],[1,1]];C.G=[[0,0],[1,1]];C.B=[[0,0],[1,1]];C.K=[[0,0],[1,1]];C.A=[[0,0],[1,1]];C.L=[[0,0],[1,1]];C.a=[[0,0],[1,1]];C.b=[[0,0],[1,1]];C.c=[[0,0],[1,1]];C.H=[[0,0],[1,1]];C.S=[[0,0],[0.1447,0.33247],[1,1]];C.Rt=C.AkimaSubsplines;C.Gt=C.AkimaSubsplines;C.Bt=C.AkimaSubsplines;C.Kt=C.AkimaSubsplines;C.At=C.AkimaSubsplines;C.Lt=C.AkimaSubsplines;C.at=C.AkimaSubsplines;C.bt=C.AkimaSubsplines;C.ct=C.AkimaSubsplines;C.Ht=C.AkimaSubsplines;C.St=C.AkimaSubsplines;C.executeOn(view);var N=new SCNR;N.amount=0.73;N.protectionMethod=SCNR.prototype.AverageNeutral;N.colorToRemove=SCNR.prototype.Green;N.preserveLightness=true;N.executeOn(view);}
function pixelMathSubtract(targetView,aId,bId,newId){var pm=new PixelMath;pm.expression=aId+" - "+bId;pm.useSingleExpression=true;pm.rescale=true;pm.rescaleLower=0;pm.rescaleUpper=1;pm.truncate=false;pm.createNewImage=true;pm.newImageId=newId;pm.newImageWidth=0;pm.newImageHeight=0;pm.newImageSampleFormat=0;pm.executeOn(targetView,false);var outWin=ImageWindow.windowById(newId);if(!outWin)throw new Error("PixelMath subtraction failed: "+newId);var outView=outWin.mainView;outView.beginProcess(UndoFlag_NoSwapFile);outView.image.truncate(0,1);outView.endProcess();return outWin;}
function subtractFilesToPath_StretchedMask(starsPath,starlessPath,outPath){var wA=ImageWindow.open(starsPath);if(!wA.length)throw new Error("Open failed: "+starsPath);var wB=ImageWindow.open(starlessPath);if(!wB.length)throw new Error("Open failed: "+starlessPath);var A=wA[0],B=wB[0];var aimg=A.mainView.image,bimg=B.mainView.image;if(aimg.width!==bimg.width||aimg.height!==bimg.height||aimg.numberOfChannels!==bimg.numberOfChannels||aimg.sampleType!==bimg.sampleType||aimg.bitsPerSample!==bimg.bitsPerSample)throw new Error("Geometry/sample mismatch between stars and starless.");var commonSTF=computeSTF(aimg);applyHT(A.mainView,commonSTF);applyHT(B.mainView,commonSTF);A.mainView.id="StarsObj_A";B.mainView.id="Starless_B";var out=pixelMathSubtract(A.mainView,"StarsObj_A","Starless_B","StarsMinusStarless");saveAs16BitTiff(out,outPath);try{out.forceClose();}catch(_){}try{A.forceClose();}catch(_){}try{B.forceClose();}catch(_){}}
function starSeparationAndStretchedMask(win,sum,finalDir,baseTag,opts,isRGBCombined,enhanceRGBStarsStretched){var needStarlessAny=opts.starless_linear||opts.starless_stretched;var needStarsStretched=opts.stars_stretched;if(!(needStarlessAny||needStarsStretched)){sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};return true;}try{var P=new StarXTerminator;P.generateStarImage=false;P.unscreenStars=false;P.largeOverlap=false;P.executeOn(win.mainView);var pathStarlessL=finalDir+"/Starless_"+baseTag+outputExtension;var pathStarlessS=finalDir+"/Starless_Stretched_"+baseTag;if(opts.starless_linear)win.saveAs(pathStarlessL,false,false,false,false);if(opts.starless_stretched){if(!File.exists(pathStarlessL)){win.saveAs(pathStarlessL,false,false,false,false);}var starlessLinearWinArr=ImageWindow.open(pathStarlessL);if(starlessLinearWinArr.length>0){var wDup=starlessLinearWinArr[0];var pathBaselineL=finalDir+"/Final_Stacked_"+baseTag+outputExtension;var baselineArr=ImageWindow.open(pathBaselineL);var stf1;if(baselineArr.length>0){var baselineW=baselineArr[0];stf1=computeSTF(baselineW.mainView.image);baselineW.forceClose();}else{throw new Error("Failed to open baseline for STF computation.");}applyHT(wDup.mainView,stf1);saveAs16BitTiff(wDup,pathStarlessS);wDup.forceClose();}else{throw new Error("Failed to reopen starless linear image for stretching.");}}if(needStarsStretched){var pathBaselineL=finalDir+"/Final_Stacked_"+baseTag+outputExtension;if(!File.exists(pathStarlessL))win.saveAs(pathStarlessL,false,false,false,false);var outStarsS=finalDir+"/Stars_Stretched_"+baseTag;subtractFilesToPath_StretchedMask(pathBaselineL,pathStarlessL,outStarsS);if(isRGBCombined&&enhanceRGBStarsStretched){var wS=ImageWindow.open(File.changeExtension(outStarsS,".tif"));if(wS.length){applyColorEnhanceToView(wS[0].mainView);saveAs16BitTiff(wS[0],outStarsS);try{wS[0].forceClose();}catch(_){}}}}sum.starSeparation={name:"Star Separation",status:"✅",details:"Starless made with SXT; star mask by stretched subtraction with common STF"};return true;}catch(e){sum.starSeparation={name:"Star Separation",status:"❌",details:e.message};return false;}}
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB = false, spccGraphs = false){
  var sum={};
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);
  var debugDir = rootOut + "/debug";
  if (DEBUG_MODE) ensureDir(debugDir);
  debugStepCounter = 1;

  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};

  closeAllWindowsExcept([win.mainView.id]);

  finalABE(win,sum);
  debugPause(win, "Background Extraction ABE", baseTag, debugDir);

  autoBlackPoint(win,sum);
  debugPause(win, "Initial Black Point", baseTag, debugDir);

  runGradientCorrection(win, sum);
  debugPause(win, "GradientCorrection Tool", baseTag, debugDir);

  runGraXpert(win, sum);
  debugPause(win, "Gradient Removal GraXpert", baseTag, debugDir);

  autoBlackPoint(win,sum);
  debugPause(win, "Final Black Point", baseTag, debugDir);

  var isColor = win.mainView.image.isColor;
  if (isColor) {
    if (isStackedRGB) {
        sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped for pre-stacked color image"};
        sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped for pre-stacked color image"};
    } else {
        if (solveImage(win, sum)) {
            var spccProfile = isRGBCombined ? "RGB" : "OSC";
            performSPCC(win, sum, spccProfile, spccGraphs);
            debugPause(win, "Color Calibration SPCC", baseTag, debugDir);
        } else {
            sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
        }
    }
  } else {
    sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped (mono/NB)"};
    sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (mono/NB)"};
  }

  closeAllWindowsExcept([win.mainView.id]);
  var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};

  if(ai.deblur1){
    let w1 = ImageWindow.open(baseline)[0];
    if (w1) {
        if (deblur1(w1, sum)) {
            debugPause(w1, "Deblur V1 Round Stars", baseTag, debugDir);
            if(saveCfg.deblur1) w1.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
        }
        try{ w1.forceClose(); }catch(_){}
    }
  }

  if(ai.deblur2){
    let w2 = ImageWindow.open(baseline)[0];
    if (w2) {
        if (deblur2(w2, sum)) {
            debugPause(w2, "Deblur V2 Enhance", baseTag, debugDir);
            if(saveCfg.deblur2) w2.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
        }
        try{ w2.forceClose(); }catch(_){}
    }
  }

  var stretchedPath = finalDir+"/Stretched_"+baseTag;
  if(ai.stretch){
    let wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/saveCfg.final_stretched);
    if(wS) {
        debugPause(wS, "Stretch AutoSTF-HT", baseTag, debugDir);
        try{ wS.forceClose(); }catch(_){}
    }
  }

  if(ai.denoise){
    var dnIn = ai.stretch ? File.changeExtension(stretchedPath,".tif") : baseline;
    if(!File.exists(dnIn) && ai.stretch) {
        var temp_wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/true);
        if(temp_wS) try{ temp_wS.forceClose(); }catch(_){}
    }
    var w3 = ImageWindow.open(dnIn)[0];
    if(w3){
        if(denoise(w3,sum) && saveCfg.denoised) {
             var denoiseOutPath = finalDir+"/Denoised_"+baseTag;
             if (ai.stretch) { saveAs16BitTiff(w3, denoiseOutPath); }
             else { w3.saveAs(denoiseOutPath + outputExtension, false, false, false, false); }
        }
        try{ w3.forceClose(); }catch(_){}
    }
  }

  if(ai.starless){
    var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
    if(needAny){
      var w4=ImageWindow.open(baseline)[0];
      if(w4) starSeparationAndStretchedMask(w4,sum,finalDir,baseTag,
        { stars_stretched:saveCfg.stars_stretched, starless_linear:saveCfg.starless_linear, starless_stretched:saveCfg.starless_stretched },
        isRGBCombined, enhanceRGBStarsStretched
      );
      try{ w4.forceClose(); }catch(_){}
    }else{
      sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
    }
  }

  if(!saveCfg.baseline_linear) removeIf(baseline, true);
  if(!saveCfg.integration_linear) removeIf(integrationPath, true);
  if(!saveCfg.final_stretched && !saveCfg.denoised) {
      removeIf(File.changeExtension(stretchedPath, ".tif"), true);
  }

  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "denoising", "starSeparation", "finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}
function showGUI(state){var dlg=new Dialog;dlg.windowTitle="Post-Integration Pipeline (RGB & Mono) - CORRECTED STRETCH";dlg.sizer=new VerticalSizer;dlg.sizer.margin=10;dlg.sizer.spacing=8;var head=new Label(dlg);head.useRichText=true;head.text="<b>Utah Masterclass — Post-Integration Pipeline (CORRECTED STRETCH)</b>";head.textAlignment=TextAlign_Center;dlg.sizer.add(head);var gTop=new GroupBox(dlg);gTop.title="General";gTop.sizer=new VerticalSizer;gTop.sizer.margin=8;gTop.sizer.spacing=6;var rowIn=new HorizontalSizer;rowIn.spacing=6;var labelIn=new Label(dlg);labelIn.text="Input Directory:";labelIn.textAlignment=TextAlign_Right|TextAlign_VertCenter;var editIn=new Edit(dlg);editIn.readOnly=true;editIn.minWidth=560;editIn.text=state.inputDir;var btnIn=new PushButton(dlg);btnIn.text="Browse...";btnIn.icon=dlg.scaledResource(":/icons/select-file.png");btnIn.onClick=function(){var d=new GetDirectoryDialog;d.caption="Select Input Directory";d.initialDirectory=state.inputDir||"";if(d.execute()){state.inputDir=fwd(d.directory).replace(/\/$/,"");editIn.text=state.inputDir;}};rowIn.add(labelIn);rowIn.add(editIn,100);rowIn.add(btnIn);gTop.sizer.add(rowIn);var rowOut=new HorizontalSizer;rowOut.spacing=6;var labelOut=new Label(dlg);labelOut.text="Output Directory:";labelOut.textAlignment=TextAlign_Right|TextAlign_VertCenter;var editOut=new Edit(dlg);editOut.readOnly=true;editOut.minWidth=560;editOut.text=state.outputDir;var btnOut=new PushButton(dlg);btnOut.text="Browse...";btnOut.icon=dlg.scaledResource(":/icons/select-file.png");btnOut.onClick=function(){var d=new GetDirectoryDialog;d.caption="Select Output Base Directory";d.initialDirectory=state.outputDir||"";if(d.execute()){state.outputDir=fwd(d.directory).replace(/\/$/,"");editOut.text=state.outputDir;}};rowOut.add(labelOut);rowOut.add(editOut,100);rowOut.add(btnOut);gTop.sizer.add(rowOut);dlg.sizer.add(gTop);var gAI=new GroupBox(dlg);gAI.title="AI / Processing Steps";gAI.sizer=new VerticalSizer;gAI.sizer.margin=8;gAI.sizer.spacing=6;var rowAI1=new HorizontalSizer;rowAI1.spacing=12;var cbD1=new CheckBox(dlg);cbD1.text="✨ Deblur V1 (round stars)";cbD1.checked=state.ai.deblur1;var cbD2=new CheckBox(dlg);cbD2.text="✨ Deblur V2 (enhance)";cbD2.checked=state.ai.deblur2;var cbST=new CheckBox(dlg);cbST.text="🌀 Stretch (AutoSTF→HT)";cbST.checked=state.ai.stretch;var cbDN=new CheckBox(dlg);cbDN.text="🧼 Denoise";cbDN.checked=state.ai.denoise;var cbSL=new CheckBox(dlg);cbSL.text="✂️ Enable StarX (for starless/stars)";cbSL.checked=state.ai.starless;rowAI1.add(cbD1);rowAI1.add(cbD2);rowAI1.add(cbST);rowAI1.add(cbDN);rowAI1.add(cbSL);rowAI1.addStretch();gAI.sizer.add(rowAI1);var rowAI2=new HorizontalSizer;rowAI2.spacing=12;var cbSPCCGraph=new CheckBox(dlg);cbSPCCGraph.text="📊 Show SPCC Graphs (Advanced)";cbSPCCGraph.checked=state.spccGraphs;rowAI2.add(cbSPCCGraph);rowAI2.addStretch();gAI.sizer.add(rowAI2);dlg.sizer.add(gAI);var gRGB=new GroupBox(dlg);gRGB.title="RGB Outputs";gRGB.sizer=new VerticalSizer;gRGB.sizer.margin=8;gRGB.sizer.spacing=6;var rowRGBMaster=new HorizontalSizer;rowRGBMaster.spacing=10;var chkProcRGB=new CheckBox(gRGB);chkProcRGB.text="✅ Process RGB / Color";chkProcRGB.checked=state.processRGB;chkProcRGB.toolTip="Enable or disable all RGB/Color processing and outputs.";rowRGBMaster.add(chkProcRGB);rowRGBMaster.addStretch();gRGB.sizer.add(rowRGBMaster);var cbCombine=new CheckBox(dlg);cbCombine.text="Auto-detect & combine R+G+B masters";cbCombine.checked=state.combineRGB;gRGB.sizer.add(cbCombine);var rowR1=new HorizontalSizer;rowR1.spacing=10;var lR1=new Label(dlg);lR1.text="🏆 Finals:";lR1.minWidth=120;var rFinalS=new CheckBox(dlg);rFinalS.text="Final (stretched, with stars) (TIFF)";rFinalS.checked=state.save.rgb.final_stretched;var rFinalL=new CheckBox(dlg);rFinalL.text="Final (linear, with stars)";rFinalL.checked=state.save.rgb.final_linear;rowR1.add(lR1);rowR1.add(rFinalS);rowR1.add(rFinalL);rowR1.addStretch();gRGB.sizer.add(rowR1);var rowR2=new HorizontalSizer;rowR2.spacing=10;var lR2=new Label(dlg);lR2.text="🎭 Masks & Starless:";lR2.minWidth=120;var rStarsS=new CheckBox(dlg);rStarsS.text="Stars (stretched mask) (TIFF)";rStarsS.checked=state.save.rgb.stars_stretched;var rgbEnh=new CheckBox(dlg);rgbEnh.text="✨ Enhance with rich star colors";rgbEnh.checked=state.colorEnhanceRGBStarsStretched;var rSLessS=new CheckBox(dlg);rSLessS.text="Starless (stretched) (TIFF)";rSLessS.checked=state.save.rgb.starless_stretched;var rSLessL=new CheckBox(dlg);rSLessL.text="Starless (linear)";rSLessL.checked=state.save.rgb.starless_linear;rowR2.add(lR2);rowR2.add(rStarsS);rowR2.add(rgbEnh);rowR2.add(rSLessS);rowR2.add(rSLessL);rowR2.addStretch();gRGB.sizer.add(rowR2);var rowR3=new VerticalSizer;rowR3.spacing=10;var iInt=new CheckBox(dlg);iInt.text="Integration (linear)";iInt.checked=state.save.rgb.integration_linear;var iBase=new CheckBox(dlg);iBase.text="Baseline (linear)";iBase.checked=state.save.rgb.baseline_linear;var iD1=new CheckBox(dlg);iD1.text="Save Round Stars (Deblur V1)";iD1.checked=state.save.rgb.deblur1;var iD2=new CheckBox(dlg);iD2.text="Save Enhance (Deblur V2)";iD2.checked=state.save.rgb.deblur2;var iDN=new CheckBox(dlg);iDN.text="Save Denoised (if stretched -> TIFF)";iDN.checked=state.save.rgb.denoised;var r3items=new HorizontalSizer;r3items.spacing=10;var lR3=new Label(dlg);lR3.text="Intermediates:";lR3.minWidth=120;r3items.add(lR3);r3items.add(iInt);r3items.add(iBase);r3items.add(iD1);r3items.add(iD2);r3items.add(iDN);r3items.addStretch();rowR3.add(r3items);var btnAdvRgb=new ToolButton(dlg);btnAdvRgb.text="Show Advanced (Intermediates) ▼";gRGB.sizer.add(btnAdvRgb);gRGB.sizer.add(rowR3);dlg.sizer.add(gRGB);var gM=new GroupBox(dlg);gM.title="Monochrome / Narrowband Outputs";gM.sizer=new VerticalSizer;gM.sizer.margin=8;gM.sizer.spacing=6;var rowMonoMaster=new HorizontalSizer;rowMonoMaster.spacing=10;var chkProcMono=new CheckBox(gM);chkProcMono.text="✅ Process Monochrome / Singles";chkProcMono.checked=state.processMonochrome;chkProcMono.toolTip="Enable or disable all Monochrome/single-channel processing and outputs.";rowMonoMaster.add(chkProcMono);rowMonoMaster.addStretch();gM.sizer.add(rowMonoMaster);var rowM1=new HorizontalSizer;rowM1.spacing=10;var lM1=new Label(dlg);lM1.text="🏆 Finals:";lM1.minWidth=120;var mFinalS=new CheckBox(dlg);mFinalS.text="Final (stretched, with stars) (TIFF)";mFinalS.checked=state.save.mono.final_stretched;var mFinalL=new CheckBox(dlg);mFinalL.text="Final (linear, with stars)";mFinalL.checked=state.save.mono.final_linear;rowM1.add(lM1);rowM1.add(mFinalS);rowM1.add(mFinalL);rowM1.addStretch();gM.sizer.add(rowM1);var rowM2=new HorizontalSizer;rowM2.spacing=10;var lM2=new Label(dlg);lM2.text="🎭 Starless & Masks:";lM2.minWidth=120;var mStarsS=new CheckBox(dlg);mStarsS.text="Stars (stretched mask) (TIFF)";mStarsS.checked=state.save.mono.stars_stretched;var mSLessS=new CheckBox(dlg);mSLessS.text="Starless (stretched) (TIFF)";mSLessS.checked=state.save.mono.starless_stretched;var mSLessL=new CheckBox(dlg);mSLessL.text="Starless (linear)";mSLessL.checked=state.save.mono.starless_linear;rowM2.add(lM2);rowM2.add(mStarsS);rowM2.add(mSLessS);rowM2.add(mSLessL);rowM2.addStretch();gM.sizer.add(rowM2);var rowM3=new VerticalSizer;rowM3.spacing=10;var miInt=new CheckBox(dlg);miInt.text="Integration (linear)";miInt.checked=state.save.mono.integration_linear;var miBase=new CheckBox(dlg);miBase.text="Baseline (linear)";miBase.checked=state.save.mono.baseline_linear;var miD1=new CheckBox(dlg);miD1.text="Save Round Stars (Deblur V1)";miD1.checked=state.save.mono.deblur1;var miD2=new CheckBox(dlg);miD2.text="Save Enhance (Deblur V2)";miD2.checked=state.save.mono.deblur2;var miDN=new CheckBox(dlg);miDN.text="Save Denoised (if stretched -> TIFF)";miDN.checked=state.save.mono.denoised;var m3items=new HorizontalSizer;m3items.spacing=10;var lM3=new Label(dlg);lM3.text="Intermediates:";lM3.minWidth=120;m3items.add(lM3);m3items.add(miInt);m3items.add(miBase);m3items.add(miD1);m3items.add(miD2);m3items.add(miDN);m3items.addStretch();rowM3.add(m3items);var btnAdvMono=new ToolButton(dlg);btnAdvMono.text="Show Advanced (Intermediates) ▼";gM.sizer.add(btnAdvMono);gM.sizer.add(rowM3);dlg.sizer.add(gM);var rgbControls=[cbCombine,rFinalS,rFinalL,rStarsS,rgbEnh,rSLessS,rSLessL,btnAdvRgb,iInt,iBase,iD1,iD2,iDN];var monoControls=[mFinalS,mFinalL,mStarsS,mSLessS,mSLessL,btnAdvMono,miInt,miBase,miD1,miD2,miDN];function toggleSection(enabled,controls){for(var i=0;i<controls.length;++i)controls[i].enabled=enabled;}chkProcRGB.onCheck=function(checked){toggleSection(checked,rgbControls);};chkProcMono.onCheck=function(checked){toggleSection(checked,monoControls);};function toggleAdvanced(row,btn){row.visible=!row.visible;btn.text=row.visible?"Hide Advanced (Intermediates) ▲":"Show Advanced (Intermediates) ▼";dlg.adjustToContents();}btnAdvRgb.onClick=function(){toggleAdvanced(rowR3,btnAdvRgb);};btnAdvMono.onClick=function(){toggleAdvanced(rowM3,btnAdvMono);};rowR3.visible=false;rowM3.visible=false;toggleSection(chkProcRGB.checked,rgbControls);toggleSection(chkProcMono.checked,monoControls);var rowBtn=new HorizontalSizer;rowBtn.spacing=8;rowBtn.addStretch();var bStart=new PushButton(dlg);bStart.text="Start";bStart.icon=dlg.scaledResource(":/icons/ok.png");bStart.defaultButton=true;var bCancel=new PushButton(dlg);bCancel.text="Cancel";bCancel.icon=dlg.scaledResource(":/icons/close.png");rowBtn.add(bStart);rowBtn.add(bCancel);dlg.sizer.add(rowBtn);bCancel.onClick=function(){dlg.cancel();};bStart.onClick=function(){if(!state.inputDir||!File.directoryExists(state.inputDir)){(new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();return;}if(!state.outputDir||!File.directoryExists(state.outputDir)){(new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();return;}state.processRGB=chkProcRGB.checked;state.processMonochrome=chkProcMono.checked;state.combineRGB=cbCombine.checked;state.ai.deblur1=cbD1.checked;state.ai.deblur2=cbD2.checked;state.ai.stretch=cbST.checked;state.ai.denoise=cbDN.checked;state.ai.starless=cbSL.checked;state.colorEnhanceRGBStarsStretched=rgbEnh.checked;state.spccGraphs=cbSPCCGraph.checked;state.save.rgb={final_stretched:rFinalS.checked,final_linear:rFinalL.checked,stars_stretched:rStarsS.checked,starless_stretched:rSLessS.checked,starless_linear:rSLessL.checked,integration_linear:iInt.checked,baseline_linear:iBase.checked,deblur1:iD1.checked,deblur2:iD2.checked,denoised:iDN.checked};state.save.mono={final_stretched:mFinalS.checked,final_linear:mFinalL.checked,stars_stretched:mStarsS.checked,starless_stretched:mSLessS.checked,starless_linear:mSLessL.checked,integration_linear:miInt.checked,baseline_linear:miBase.checked,deblur1:miD1.checked,deblur2:miD2.checked,denoised:miDN.checked};dlg.ok();};return dlg.execute();}
function run(){Console.show();Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — CORRECTED Stretched Star Mask ===");var settingsKey="PostIntegrationPipeline";var state={inputDir:Settings.read(settingsKey+"/InputDir",DataType_String)||defaults.inputDir,outputDir:Settings.read(settingsKey+"/OutputDir",DataType_String)||defaults.outputDir,processRGB:defaults.processRGB,processMonochrome:defaults.processMonochrome,combineRGB:defaults.combineRGB,ai:{deblur1:defaults.ai.deblur1,deblur2:defaults.ai.deblur2,stretch:defaults.ai.stretch,denoise:defaults.ai.denoise,starless:defaults.ai.starless},colorEnhanceRGBStarsStretched:defaults.colorEnhanceRGBStarsStretched,spccGraphs:defaults.spccGraphs,save:{rgb:defaults.save.rgb,mono:defaults.save.mono}};if(!showGUI(state)){Console.writeln("Cancelled.");return;}Settings.write(settingsKey+"/InputDir",DataType_String,state.inputDir);Settings.write(settingsKey+"/OutputDir",DataType_String,state.outputDir);Console.writeln("Input dir : "+state.inputDir);Console.writeln("Output dir: "+state.outputDir);var root=tsFolder(state.outputDir);Console.writeln("Output folder: "+root);ensureDir(root+"/5_stacked");ensureDir(root+"/6_final");try{var plan=buildWorkPlan(state.inputDir,state.combineRGB);var doRGB=state.processRGB&&plan.doRGB&&shouldProcessConfig(state.save.rgb);if(!state.processRGB&&!state.processMonochrome){Console.writeln("Both RGB and Monochrome processing are disabled. Exiting.");return;}if(doRGB){Console.writeln("\n→ Building RGB from:");Console.writeln("   R: "+File.extractName(plan.r));Console.writeln("   G: "+File.extractName(plan.g));Console.writeln("   B: "+File.extractName(plan.b));var combo=combineRGB(plan.r,plan.g,plan.b,root);processOne(combo.window,combo.base,root,state.ai,state.save.rgb,true,state.colorEnhanceRGBStarsStretched,false,state.spccGraphs);try{combo.window.forceClose();}catch(_){}}else{Console.writeln("RGB Combination: skipped (no R+G+B set found or option disabled).");}if(plan.singles.length>0){Console.writeln("\n→ Processing mono/narrowband/color singles: "+plan.singles.length);for(var i=0;i<plan.singles.length;++i){var singleInfo=plan.singles[i];var p=singleInfo.path;var tag=singleInfo.tag;var isStackedRGB=singleInfo.isStackedRGB;Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");var w=ImageWindow.open(p);if(w.length===0){Console.writeln("  ⚠️ Could not open, skipping.");continue;}var win=w[0],base=tag+"_"+sanitizeBase(File.extractName(p));var isColor=win.mainView.image.isColor;var saveCfg=isColor?state.save.rgb:state.save.mono;var procEnabled=isColor?state.processRGB:state.processMonochrome;if(!procEnabled||!shouldProcessConfig(saveCfg)){Console.writeln("  ⏭️ Skipped (processing or outputs disabled for this image type).");try{win.forceClose();}catch(_){}continue;}processOne(win,base,root,state.ai,saveCfg,false,isColor&&state.colorEnhanceRGBStarsStretched,isStackedRGB,state.spccGraphs);try{win.forceClose();}catch(_){}closeAllWindowsExcept(null);}}else{Console.writeln("\nNo single channel (Mono/NB/Color) masters found to process.");}Console.writeln("\n=== Done. Output: "+root+" ===");}catch(err){Console.criticalln("Error: "+err.message);if(DEBUG_MODE){(new MessageBox(err.message,"Script Aborted",StdIcon_Error,StdButton_Ok)).execute();}else{throw err;}}}

run();
})(); // IIFE
