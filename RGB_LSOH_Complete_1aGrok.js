/*
 * Post-Integration Pipeline — RGB & Monochrome
 * - Stretched star mask only: Stars = (with-stars − starless) after STF bake.
 * - GUI: Process RGB / Process Monochrome master toggles.
 * - GUI: In RGB, “Enhance star colors” sits on Masks & Starless row, right of Stars (stretched mask).
 * - ImageSolver dialog suppressed where possible; SPCC graphs disabled by default.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
(function(){
// -------------------- AutoSTF constants --------------------
var STF_UNLINKED        = true;
var STF_K_SHADOWS       = -3.0;
var STF_TARGET_BG       = 0.22;
var STF_HIGHLIGHTS      = 1.0;
var STF_MAX_SAMPLES     = 120000;
var outputExtension = ".xisf";
// -------------------- Defaults --------------------
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,   // master gates
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true, // default ON
  save: {
    rgb: {
      final_stretched: false,
      final_linear:    false,
      stars_stretched:       true,   // default ON
      starless_stretched:    false,
      starless_linear:       false,
      integration_linear:    false,
      baseline_linear:       false,
      deblur1:               false,
      deblur2:               false,
      denoised:              false
    },
    mono: {
      final_stretched: false,
      final_linear:    false,
      stars_stretched:       false,
      starless_stretched:    true,   // default ON
      starless_linear:       false,
      integration_linear:    false,
      baseline_linear:       false,
      deblur1:               false,
      deblur2:               false,
      denoised:              false
    }
  }
};
// -------------------- Utils --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!(
    cfg.final_stretched || cfg.final_linear ||
    cfg.stars_stretched ||
    cfg.starless_stretched || cfg.starless_linear ||
    cfg.integration_linear || cfg.baseline_linear ||
    cfg.deblur1 || cfg.deblur2 || cfg.denoised
  );
}
// -------------------- Discovery --------------------
function findAllXISF(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[], ff=new FileFind;
  if(ff.begin(dir+"/*.xisf")){ do{ v.push(dir+"/"+ff.name); }while(ff.next()); }
  return v;
}
function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /\bred\b/.test(s) || (/\br\b/.test(s)&&s.indexOf("rgb")<0)) return "R";
  if (s.indexOf("filter-green")>=0 || /\bgreen\b/.test(s) || (/\bg\b/.test(s)&&s.indexOf("rgb")<0)) return "G";
  if (s.indexOf("filter-blue")>=0 || /\bblue\b/.test(s) || (/\bb\b/.test(s)&&s.indexOf("rgb")<0)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}
function buildWorkPlan(dir, combineRGB){
  var files = findAllXISF(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }
  var haveR=!!(byFilter.R&&byFilter.R.length),
      haveG=!!(byFilter.G&&byFilter.G.length),
      haveB=!!(byFilter.B&&byFilter.B.length);
  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };
  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }
  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) plan.singles.push({ path:unknownSingles[k], tag:"Single" });
  return plan;
}
// -------------------- RGB Combination --------------------
function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}
// -------------------- ABE & BlackPoint --------------------
function finalABE(win, sum){
  try{
    var P=new AutomaticBackgroundExtractor();
    P.targetCorrection=1; P.normalize=true; P.discardBackground=true;
    var before=ImageWindow.windows; P.executeOn(win.mainView); var after=ImageWindow.windows;
    for(var k=0;k<after.length;++k){
      var isNew=true; for(var j=0;j<before.length;++j){ if(after[k].mainView.id===before[j].mainView.id){ isNew=false; break; } }
      if(isNew && after[k].mainView.id.toLowerCase().indexOf("background")!==-1){ after[k].forceClose(); break; }
    }
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE complete"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}
function autoBlackPoint(win, sum){
  try{
    var img=win.mainView.image;
    if(!img.isColor || img.numberOfChannels<3){
      var AH=new AutoHistogram(); AH.auto=true; AH.clipLow=0.1; AH.clipHigh=0.1; AH.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"AutoHistogram (mono/NB)"}; return true;
    }
    var w=img.width,h=img.height, sampleSize=20, numSamples=20;
    var rs=[],gs=[],bs=[];
    for(var i=0;i<numSamples;++i){
      var x=Math.floor(Math.random()*(w-sampleSize)), y=Math.floor(Math.random()*(h-sampleSize));
      var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;
      for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }
      rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);
    }
    rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});
    var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0; for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; } R/=n; G/=n; B/=n;
    try{
      var L=new Levels(); L.redBlack=Math.min(R*0.8,0.02); L.greenBlack=Math.min(G*0.9,0.03); L.blueBlack=Math.min(B*0.8,0.02);
      L.redWhite=0.98; L.greenWhite=0.97; L.blueWhite=0.98; L.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Levels"};
    }catch(e2){
      var AH2=new AutoHistogram(); AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1; AH2.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};
    }
    return true;
  }catch(e){ sum.blackPoint={name:"Black Point",status:"❌",details:e.message}; return false; }
}
// -------------------- ImageSolver + SPCC --------------------
function solveImage(win, sum){
  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    var solver = new ImageSolver();
    // Suppress UI where possible (defensive; properties may not exist in all versions)
    try{ solver.silent = true; }catch(_){}
    try{ solver.showInterface = false; }catch(_){}
    try{ solver.forceDialog = false; }catch(_){}
    try{ solver.noGUIMode = true; }catch(_){}
    try{ solver.noGUIMessages = true; }catch(_){}
    solver.Init(win, /*verbose*/false);
    solver.useMetadata = true;
    solver.catalog = "GAIA DR3";
    solver.useDistortionCorrection = false;
    solver.generateErrorMaps = false;
    solver.showStars = false;
    solver.showDistortion = false;
    solver.generateDistortionMaps = false;
    solver.sensitivity = 0.1;
    if (!solver.SolveImage(win)){
      Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");
      solver.useMetadata = false;
      if (!solver.SolveImage(win)) throw new Error("Plate solution not found.");
    }
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}
function performSPCC(win, sum, profileSelector){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();
    // Disable graphs/plots by default
    try{ P.generateGraphs=false; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = +2.00; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}
    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied (graphs disabled)"};
    return true;
  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}
// -------------------- AI steps --------------------
function deblur1(win, sum){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
    P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV1={name:"Deblur V1",status:"✅",details:"Round stars"};
    return true;
  }catch(e){ sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message}; return false; }
}
function deblur2(win, sum){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
    P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};
    return true;
  }catch(e){ sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message}; return false; }
}
function denoise(win, sum){
  try{
    var P=new NoiseXTerminator();
    P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
    P.executeOn(win.mainView);
    sum.denoising={name:"Denoising",status:"✅",details:"Applied"};
    return true;
  }catch(e){ sum.denoising={name:"Denoising",status:"❌",details:e.message}; return false; }
}
// -------------------- AutoSTF→HT helpers --------------------
function clamp(x,a,b){ return x<a?a:(x>b?b:x); }
function median(v){ var n=v.length; if(!n) return 0; v.sort(function(a,b){return a-b;}); return (n&1)? v[(n-1)>>1] : 0.5*(v[n>>1]+v[(n>>1)-1]); }
function mad(v, med){ var a=new Array(v.length); for (var i=0;i<v.length;++i) a[i]=Math.abs(v[i]-med); return median(a); }
function sampleChannel(img, ch){
  var W=img.width, H=img.height, tot=W*H;
  var step = Math.max(1, Math.floor(Math.sqrt(tot/ STF_MAX_SAMPLES)));
  var vals=[];
  if (img.isColor){
    for (var y=0; y<H; y+=step)
      for (var x=0; x<W; x+=step)
        vals.push( img.sample(x, y, ch) );
  } else {
    for (var y=0; y<H; y+=step)
      for (var x=0; x<W; x+=step)
        vals.push( img.sample(x, y) );
  }
  return vals;
}
function sampleLuminance(img){
  var W=img.width, H=img.height, tot=W*H;
  var step = Math.max(1, Math.floor(Math.sqrt(tot/ STF_MAX_SAMPLES)));
  var vals=[];
  for (var y=0; y<H; y+=step){
    for (var x=0; x<W; x+=step){
      if (img.isColor){
        var r = img.sample(x,y,0), g = img.sample(x,y,1), b = img.sample(x,y,2);
        vals.push( 0.2126*r + 0.7152*g + 0.0722*b );
      } else vals.push( img.sample(x,y) );
    }
  }
  return vals;
}
function mtfMidtonesFromTarget(normBg, target){
  normBg = clamp(normBg, 1e-6, 0.999999);
  target = clamp(target, 1e-6, 0.999999);
  return clamp( Math.exp( Math.log(0.5)*Math.log(normBg)/Math.log(target) ), 0.001, 0.999 );
}
function computeSTF(img){
  var s=[0,0,0], m=[0.5,0.5,0.5], h=[1,1,1];
  if (img.isColor && STF_UNLINKED){
    for (var c=0;c<3;++c){
      var v   = sampleChannel(img, c);
      var med = median(v);
      var sig = 1.4826*mad(v, med);
      var sc  = clamp(med + STF_K_SHADOWS*sig, 0, 0.99);
      var hc  = STF_HIGHLIGHTS;
      var B   = clamp( (med - sc)/(hc - sc), 1e-6, 0.999999 );
      var mc  = mtfMidtonesFromTarget(B, STF_TARGET_BG);
      s[c]=sc; m[c]=mc; h[c]=hc;
    }
  } else {
    var v   = sampleLuminance(img);
    var med = median(v);
    var sig = 1.4826*mad(v, med);
    var sc  = clamp(med + STF_K_SHADOWS*sig, 0, 0.99);
    var hc  = STF_HIGHLIGHTS;
    var B   = clamp( (med - sc)/(hc - sc), 1e-6, 0.999999 );
    var mc  = mtfMidtonesFromTarget(B, STF_TARGET_BG);
    s=[sc,sc,sc]; m=[mc,mc,mc]; h=[hc,hc,hc];
  }
  return {s:s,m:m,h:h};
}
function applyHT(view, stf){
  var P=new HistogramTransformation;
  P.H = [
    [ stf.s[0], stf.m[0], stf.h[0], 0, 1 ],
    [ stf.s[1], stf.m[1], stf.h[1], 0, 1 ],
    [ stf.s[2], stf.m[2], stf.h[2], 0, 1 ],
    [ stf.s[0], stf.m[0], stf.h[0], 0, 1 ]
  ];
  P.executeOn(view);
}
function stretchImageAtStage(inPath, outPath, saveFlag){
  var w = ImageWindow.open(inPath);
  if(!w.length) return null;
  var win=w[0], img=win.mainView.image;
  var stf = computeSTF(img);
  applyHT(win.mainView, stf);
  if(saveFlag) win.saveAs(outPath,false,false,false,false);
  return win;
}
// -------------------- Color Enhance helpers (RGB Stars stretched only) --------------------
function applyColorEnhanceToView(view){
  var C=new CurvesTransformation;
  C.R = [[0,0],[1,1]]; C.G = [[0,0],[1,1]]; C.B = [[0,0],[1,1]];
  C.K = [[0,0],[1,1]]; C.A = [[0,0],[1,1]]; C.L = [[0,0],[1,1]];
  C.a = [[0,0],[1,1]]; C.b = [[0,0],[1,1]]; C.c = [[0,0],[1,1]]; C.H = [[0,0],[1,1]];
  C.S = [[0.00000,0.00000],[0.14470,0.33247],[1.00000,1.00000]];
  C.Rt=C.AkimaSubsplines; C.Gt=C.AkimaSubsplines; C.Bt=C.AkimaSubsplines;
  C.Kt=C.AkimaSubsplines; C.At=C.AkimaSubsplines; C.Lt=C.AkimaSubsplines;
  C.at=C.AkimaSubsplines; C.bt=C.AkimaSubsplines; C.ct=C.AkimaSubsplines; C.Ht=C.AkimaSubsplines; C.St=C.AkimaSubsplines;
  C.executeOn(view);
  var N=new SCNR; N.amount=0.73; N.protectionMethod=SCNR.prototype.AverageNeutral; N.colorToRemove=SCNR.prototype.Green; N.preserveLightness=true;
  N.executeOn(view);
}
// -------------------- PixelMath subtraction --------------------
function pixelMathSubtract(targetView, aId, bId, newId){
  var pm = new PixelMath();
  pm.expression = aId + " - " + bId;
  pm.useSingleExpression = true;
  pm.rescale = true; pm.rescaleLower=0.0; pm.rescaleUpper=1.0;
  pm.truncate=false; pm.createNewImage=true;
  pm.newImageId = newId; pm.newImageWidth=0; pm.newImageHeight=0; pm.newImageSampleFormat=0;
  pm.executeOn(targetView, false);
  var outWin = ImageWindow.windowById(newId);
  if(!outWin) throw new Error("PixelMath subtraction failed: "+newId);
  var outView = outWin.mainView;
  outView.beginProcess(UndoFlag_NoSwapFile); outView.image.truncate(0.0,1.0); outView.endProcess();
  return outWin;
}
function subtractFilesToPath_StretchedMask(starsPath, starlessPath, outPath){
  var wA = ImageWindow.open(starsPath); if(!wA.length) throw new Error("Open failed: "+starsPath);
  var wB = ImageWindow.open(starlessPath); if(!wB.length) throw new Error("Open failed: "+starlessPath);
  var A = wA[0], B = wB[0];
  var aimg=A.mainView.image, bimg=B.mainView.image;
  if(aimg.width!==bimg.width || aimg.height!==bimg.height || aimg.numberOfChannels!==bimg.numberOfChannels ||
     aimg.sampleType!==bimg.sampleType || aimg.bitsPerSample!==bimg.bitsPerSample)
    throw new Error("Geometry/sample mismatch between stars and starless.");
  var stA=computeSTF(aimg); applyHT(A.mainView, stA);
  var stB=computeSTF(bimg); applyHT(B.mainView, stB);
  A.mainView.id = "StarsObj_A"; B.mainView.id = "Starless_B";
  var out = pixelMathSubtract(A.mainView, "StarsObj_A", "Starless_B", "StarsMinusStarless");
  out.saveAs(outPath,false,false,false,false);
  try{ out.forceClose(); }catch(_){}
  try{ A.forceClose(); }catch(_){}
  try{ B.forceClose(); }catch(_){}
}
// -------------------- StarX + starless; then TRUE star mask (stretched only) --------------------
function starSeparationAndStretchedMask(win, sum, finalDir, baseTag, opts, isRGBCombined, enhanceRGBStarsStretched){
  var needStarlessAny   = opts.starless_linear || opts.starless_stretched;
  var needStarsStretched= opts.stars_stretched;
  if(!(needStarlessAny || needStarsStretched)){
    sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
    return true;
  }
  try{
    var P=new StarXTerminator();
    P.generateStarImage=false;
    P.unscreenStars=false;
    P.largeOverlap=false;
    P.executeOn(win.mainView); // now 'win' is STARLESS
    var pathStarlessL = finalDir+"/Starless_"+baseTag+outputExtension;
    var pathStarlessS = finalDir+"/Starless_Stretched_"+baseTag+outputExtension;
    if(opts.starless_linear)   win.saveAs(pathStarlessL,false,false,false,false);
    if(opts.starless_stretched){ var stf1 = computeSTF(win.mainView.image); applyHT(win.mainView, stf1); win.saveAs(pathStarlessS,false,false,false,false); }
    if(needStarsStretched){
      var pathBaselineL = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
      if(!File.exists(pathStarlessL)) win.saveAs(pathStarlessL,false,false,false,false); // temp linear starless
      var outStarsS = finalDir+"/Stars_Stretched_"+baseTag+outputExtension;
      subtractFilesToPath_StretchedMask(pathBaselineL, pathStarlessL, outStarsS);
      if(isRGBCombined && enhanceRGBStarsStretched){
        var wS=ImageWindow.open(outStarsS); if(wS.length){ applyColorEnhanceToView(wS[0].mainView); wS[0].saveAs(outStarsS,false,false,false,false); try{ wS[0].forceClose(); }catch(_){ } }
      }
    }
    sum.starSeparation={name:"Star Separation",status:"✅",details:"Starless made with SXT; star mask by stretched subtraction"};
    return true;
  }catch(e){
    sum.starSeparation={name:"Star Separation",status:"❌",details:e.message}; return false;
  }
}
// -------------------- Per-Image Pipeline --------------------
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched){
  var sum={};
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);
  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};
  closeAllWindowsExcept([win.mainView.id]);
  finalABE(win,sum);             closeAllWindowsExcept([win.mainView.id]);
  autoBlackPoint(win,sum);       closeAllWindowsExcept([win.mainView.id]);
  var isColor = win.mainView.image.isColor;
  if(isColor){
    var solved = solveImage(win, sum);
    if(solved){
      var spccProfile = isRGBCombined ? "RGB" : "OSC";
      performSPCC(win, sum, spccProfile);
    } else {
      sum.spcc = {name:"SPCC",status:"⏭️",details:"Skipped (no WCS)"};
    }
  } else {
    sum.solver = {name:"ImageSolver",status:"⏭️",details:"Skipped (mono/NB)"};
    sum.spcc   = {name:"SPCC",status:"⏭️",details:"Skipped (mono/NB)"};
  }
  closeAllWindowsExcept([win.mainView.id]);
  var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};
  if(ai.deblur1){
    var w1=ImageWindow.open(baseline)[0];
    if(w1 && saveCfg.deblur1){ if(deblur1(w1,sum)) w1.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false); }
    else { if(w1) deblur1(w1,sum); }
    try{ w1.forceClose(); }catch(_){}
  }
  if(ai.deblur2){
    var w2=ImageWindow.open(baseline)[0];
    if(w2 && saveCfg.deblur2){ if(deblur2(w2,sum)) w2.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false); }
    else { if(w2) deblur2(w2,sum); }
    try{ w2.forceClose(); }catch(_){}
  }
  var stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
  if(ai.stretch){
    var wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/true);
    if(wS) try{ wS.forceClose(); }catch(_){}
  }
  if(ai.denoise){
    var dnIn = ai.stretch ? stretchedPath : baseline;
    var w3 = ImageWindow.open(dnIn)[0];
    if(w3 && saveCfg.denoised){ if(denoise(w3,sum)) w3.saveAs(finalDir+"/Denoised_"+baseTag+outputExtension,false,false,false,false); }
    else { if(w3) denoise(w3,sum); }
    try{ w3.forceClose(); }catch(_){}
  }
  if(ai.starless){
    var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
    if(needAny){
      var w4=ImageWindow.open(baseline)[0];
      if(w4) starSeparationAndStretchedMask(
        w4,sum,finalDir,baseTag,
        {
          stars_stretched:    saveCfg.stars_stretched,
          starless_linear:    saveCfg.starless_linear,
          starless_stretched: saveCfg.starless_stretched
        },
        isRGBCombined, enhanceRGBStarsStretched
      );
      try{ w4.forceClose(); }catch(_){}
    }else{
      sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
    }
  }
  if(saveCfg.final_linear){
    // keep baseline
  } else {
    removeIf(baseline, saveCfg.baseline_linear===true);
  }
  if(saveCfg.final_stretched!==true && saveCfg.denoised!==true)
    removeIf(stretchedPath, /*keep*/false);
  removeIf(integrationPath, saveCfg.integration_linear===true);
  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction","blackPoint","solver","spcc","deblurV1","deblurV2","denoising","starSeparation","finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}
// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog; dlg.windowTitle="Post-Integration Pipeline (RGB & Mono)";
  dlg.sizer=new VerticalSizer; dlg.sizer.margin=10; dlg.sizer.spacing=8;
  var head=new Label(dlg); head.useRichText = true; head.text="<b>Utah Masterclass — Post-Integration Pipeline</b>"; head.textAlignment=TextAlign_Center; dlg.sizer.add(head);
  var gTop=new GroupBox(dlg); gTop.title="General"; gTop.sizer=new VerticalSizer; gTop.sizer.margin=8; gTop.sizer.spacing=6;
  // Process gates
  var rowGate=new HorizontalSizer; rowGate.spacing=12;
  var chkProcRGB=new CheckBox(dlg); chkProcRGB.text="Process RGB"; chkProcRGB.checked=state.processRGB;
  var chkProcMono=new CheckBox(dlg); chkProcMono.text="Process Monochrome"; chkProcMono.checked=state.processMonochrome;
  rowGate.add(chkProcRGB); rowGate.add(chkProcMono); rowGate.addStretch(); gTop.sizer.add(rowGate);
  // I/O
  var rowIn=new HorizontalSizer; rowIn.spacing=6;
  var editIn=new Edit(dlg); editIn.readOnly=true; editIn.minWidth=560; editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg); btnIn.text="Browse Input..."; btnIn.icon = dlg.scaledResource(":/icons/select-file.png");
  btnIn.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Input Directory"; d.initialDirectory=state.inputDir||""; if(d.execute()){ state.inputDir=fwd(d.directory).replace(/\/$/,""); editIn.text=state.inputDir; } };
  rowIn.add(new Label(dlg)); rowIn.add(editIn,100); rowIn.add(btnIn); gTop.sizer.add(rowIn);
  var rowOut=new HorizontalSizer; rowOut.spacing=6;
  var editOut=new Edit(dlg); editOut.readOnly=true; editOut.minWidth=560; editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg); btnOut.text="Browse Output..."; btnOut.icon = dlg.scaledResource(":/icons/select-file.png");
  btnOut.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Output Base Directory"; d.initialDirectory=state.outputDir||""; if(d.execute()){ state.outputDir=fwd(d.directory).replace(/\/$/,""); editOut.text=state.outputDir; } };
  rowOut.add(new Label(dlg)); rowOut.add(editOut,100); rowOut.add(btnOut); gTop.sizer.add(rowOut);
  dlg.sizer.add(gTop);
  // Processing toggles
  var gAI=new GroupBox(dlg); gAI.title="AI / Processing Steps"; gAI.sizer=new VerticalSizer; gAI.sizer.margin=8; gAI.sizer.spacing=6;
  var rowAI1=new HorizontalSizer; rowAI1.spacing=12;
  var cbD1=new CheckBox(dlg); cbD1.text="Deblur V1 (round stars)"; cbD1.checked=state.ai.deblur1;
  var cbD2=new CheckBox(dlg); cbD2.text="Deblur V2 (enhance)";   cbD2.checked=state.ai.deblur2;
  var cbST=new CheckBox(dlg); cbST.text="Stretch (AutoSTF→HT)";  cbST.checked=state.ai.stretch;
  var cbDN=new CheckBox(dlg); cbDN.text="Denoise";               cbDN.checked=state.ai.denoise;
  var cbSL=new CheckBox(dlg); cbSL.text="Enable StarX (required for starless/stars)"; cbSL.checked=state.ai.starless;
  rowAI1.add(cbD1); rowAI1.add(cbD2); rowAI1.add(cbST); rowAI1.add(cbDN); rowAI1.add(cbSL); rowAI1.addStretch(); gAI.sizer.add(rowAI1);
  dlg.sizer.add(gAI);
  // ===== RGB SECTION =====
  var gRGB=new GroupBox(dlg); gRGB.title="RGB Outputs"; gRGB.sizer=new VerticalSizer; gRGB.sizer.margin=8; gRGB.sizer.spacing=6;
  var rowRGB0=new HorizontalSizer; rowRGB0.spacing=10;
  var cbCombine=new CheckBox(dlg); cbCombine.text="Auto-detect & combine R+G+B masters into RGB"; cbCombine.checked=state.combineRGB;
  rowRGB0.add(cbCombine); rowRGB0.addStretch(); gRGB.sizer.add(rowRGB0);
  var rowR1=new HorizontalSizer; rowR1.spacing=10;
  var lR1=new Label(dlg); lR1.text="Finals:"; lR1.minWidth=120;
  var rFinalS=new CheckBox(dlg); rFinalS.text="Final (stretched, with stars)"; rFinalS.checked=state.save.rgb.final_stretched;
  var rFinalL=new CheckBox(dlg); rFinalL.text="Final (linear, with stars)";   rFinalL.checked=state.save.rgb.final_linear;
  rowR1.add(lR1); rowR1.add(rFinalS); rowR1.add(rFinalL); rowR1.addStretch(); gRGB.sizer.add(rowR1);
  var rowR2=new HorizontalSizer; rowR2.spacing=10;
  var lR2=new Label(dlg); lR2.text="Masks & Starless:"; lR2.minWidth=120;
  var rStarsS=new CheckBox(dlg); rStarsS.text="Stars (stretched mask)";     rStarsS.checked=state.save.rgb.stars_stretched;
  var rgbEnh=new CheckBox(dlg); rgbEnh.text="Enhance star colors";          rgbEnh.checked=state.colorEnhanceRGBStarsStretched; // MOVED next to Stars
  var rSLessS=new CheckBox(dlg); rSLessS.text="Starless (stretched)";       rSLessS.checked=state.save.rgb.starless_stretched;
  var rSLessL=new CheckBox(dlg); rSLessL.text="Starless (linear)";          rSLessL.checked=state.save.rgb.starless_linear;
  rowR2.add(lR2); rowR2.add(rStarsS); rowR2.add(rgbEnh); rowR2.add(rSLessS); rowR2.add(rSLessL); rowR2.addStretch(); gRGB.sizer.add(rowR2);
  var rowR3=new HorizontalSizer; rowR3.spacing=10;
  var lR3=new Label(dlg); lR3.text="Intermediates:"; lR3.minWidth=120;
  var iInt = new CheckBox(dlg); iInt.text="Integration (linear)"; iInt.checked=state.save.rgb.integration_linear;
  var iBase= new CheckBox(dlg); iBase.text="Baseline (linear)";    iBase.checked=state.save.rgb.baseline_linear;
  var iD1  = new CheckBox(dlg); iD1.text="Save Round Stars (Deblur V1)";  iD1.checked=state.save.rgb.deblur1;
  var iD2  = new CheckBox(dlg); iD2.text="Save Enhance (Deblur V2)";      iD2.checked=state.save.rgb.deblur2;
  var iDN  = new CheckBox(dlg); iDN.text="Save Denoised";                 iDN.checked=state.save.rgb.denoised;
  rowR3.add(lR3); rowR3.add(iInt); rowR3.add(iBase); rowR3.add(iD1); rowR3.add(iD2); rowR3.add(iDN); rowR3.addStretch(); gRGB.sizer.add(rowR3);
  dlg.sizer.add(gRGB);
  // ===== MONO SECTION =====
  var gM=new GroupBox(dlg); gM.title="Monochrome / Narrowband Outputs"; gM.sizer=new VerticalSizer; gM.sizer.margin=8; gM.sizer.spacing=6;
  var rowM1=new HorizontalSizer; rowM1.spacing=10;
  var lM1=new Label(dlg); lM1.text="Finals:"; lM1.minWidth=120;
  var mFinalS=new CheckBox(dlg); mFinalS.text="Final (stretched, with stars)"; mFinalS.checked=state.save.mono.final_stretched;
  var mFinalL=new CheckBox(dlg); mFinalL.text="Final (linear, with stars)";   mFinalL.checked=state.save.mono.final_linear;
  rowM1.add(lM1); rowM1.add(mFinalS); rowM1.add(mFinalL); rowM1.addStretch(); gM.sizer.add(rowM1);
  var rowM2=new HorizontalSizer; rowM2.spacing=10;
  var lM2=new Label(dlg); lM2.text="Starless & Masks:"; lM2.minWidth=120;
  var mStarsS=new CheckBox(dlg); mStarsS.text="Stars (stretched mask)";      mStarsS.checked=state.save.mono.stars_stretched;
  var mSLessS=new CheckBox(dlg); mSLessS.text="Starless (stretched)";        mSLessS.checked=state.save.mono.starless_stretched;
  var mSLessL=new CheckBox(dlg); mSLessL.text="Starless (linear)";           mSLessL.checked=state.save.mono.starless_linear;
  rowM2.add(lM2); rowM2.add(mStarsS); rowM2.add(mSLessS); rowM2.add(mSLessL); rowM2.addStretch(); gM.sizer.add(rowM2);
  var rowM3=new HorizontalSizer; rowM3.spacing=10;
  var lM3=new Label(dlg); lM3.text="Intermediates:"; lM3.minWidth=120;
  var miInt = new CheckBox(dlg); miInt.text="Integration (linear)"; miInt.checked=state.save.mono.integration_linear;
  var miBase= new CheckBox(dlg); miBase.text="Baseline (linear)";    miBase.checked=state.save.mono.baseline_linear;
  var miD1  = new CheckBox(dlg); miD1.text="Save Round Stars (Deblur V1)";  miD1.checked=state.save.mono.deblur1;
  var miD2  = new CheckBox(dlg); miD2.text="Save Enhance (Deblur V2)";      miD2.checked=state.save.mono.deblur2;
  var miDN  = new CheckBox(dlg); miDN.text="Save Denoised";                 miDN.checked=state.save.mono.denoised;
  rowM3.add(lM3); rowM3.add(miInt); rowM3.add(miBase); rowM3.add(miD1); rowM3.add(miD2); rowM3.add(miDN); rowM3.addStretch(); gM.sizer.add(rowM3);
  dlg.sizer.add(gM);
  // Start / Cancel
  var rowBtn=new HorizontalSizer; rowBtn.spacing=8; rowBtn.addStretch();
  var bStart=new PushButton(dlg); bStart.text="Start"; bStart.icon = dlg.scaledResource(":/icons/ok.png"); bStart.defaultButton=true;
  var bCancel=new PushButton(dlg); bCancel.text="Cancel"; bCancel.icon = dlg.scaledResource(":/icons/close.png");
  rowBtn.add(bStart); rowBtn.add(bCancel); dlg.sizer.add(rowBtn);
  bCancel.onClick=function(){ dlg.cancel(); };
  bStart.onClick=function(){
    if(!state.inputDir || !File.directoryExists(state.inputDir)){ (new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute(); return; }
    if(!state.outputDir || !File.directoryExists(state.outputDir)){ (new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute(); return; }
    state.processRGB        = chkProcRGB.checked;
    state.processMonochrome = chkProcMono.checked;
    state.combineRGB = cbCombine.checked;
    state.ai.deblur1=cbD1.checked; state.ai.deblur2=cbD2.checked; state.ai.stretch=cbST.checked;
    state.ai.denoise=cbDN.checked; state.ai.starless=cbSL.checked;
    state.colorEnhanceRGBStarsStretched = rgbEnh.checked;
    state.save.rgb = {
      final_stretched:rFinalS.checked, final_linear:rFinalL.checked,
      stars_stretched:rStarsS.checked,
      starless_stretched:rSLessS.checked, starless_linear:rSLessL.checked,
      integration_linear:iInt.checked, baseline_linear:iBase.checked,
      deblur1:iD1.checked, deblur2:iD2.checked, denoised:iDN.checked
    };
    state.save.mono = {
      final_stretched:mFinalS.checked, final_linear:mFinalL.checked,
      stars_stretched:mStarsS.checked,
      starless_stretched:mSLessS.checked, starless_linear:mSLessL.checked,
      integration_linear:miInt.checked, baseline_linear:miBase.checked,
      deblur1:miD1.checked, deblur2:miD2.checked, denoised:miDN.checked
    };
    dlg.ok();
  };
  return dlg.execute();
}
// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — TRUE Stretched Star Mask ===");
  var state={ inputDir:defaults.inputDir, outputDir:defaults.outputDir,
              processRGB:defaults.processRGB, processMonochrome:defaults.processMonochrome,
              combineRGB:defaults.combineRGB,
              ai:{ deblur1:defaults.ai.deblur1, deblur2:defaults.ai.deblur2, stretch:defaults.ai.stretch, denoise:defaults.ai.denoise, starless:defaults.ai.starless },
              colorEnhanceRGBStarsStretched: defaults.colorEnhanceRGBStarsStretched,
              save:{ rgb:defaults.save.rgb, mono:defaults.save.mono } };
  if(!showGUI(state)){ Console.writeln("Cancelled."); return; }
  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);
  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder: "+root);
  ensureDir(root+"/5_stacked"); ensureDir(root+"/6_final");
  try{
    var plan = buildWorkPlan(state.inputDir, state.combineRGB);
    // Respect master gates AND per-class outputs
    var doRGB  = state.processRGB && plan.doRGB && shouldProcessConfig(state.save.rgb);
    var doMono = state.processMonochrome && shouldProcessConfig(state.save.mono);
    if (!state.processRGB)        Console.writeln("RGB: skipped (Process RGB unchecked).");
    else if (!plan.doRGB)         Console.writeln("RGB: skipped (no R+G+B set found).");
    else if (!shouldProcessConfig(state.save.rgb)) Console.writeln("RGB: skipped (all RGB outputs disabled).");
    if (!state.processMonochrome) Console.writeln("Mono: skipped (Process Monochrome unchecked).");
    else if (!shouldProcessConfig(state.save.mono)) Console.writeln("Mono: skipped (all mono outputs disabled).");
    if (doRGB){
      Console.writeln("\n→ Building RGB from:");
      Console.writeln("   R: "+File.extractName(plan.r));
      Console.writeln("   G: "+File.extractName(plan.g));
      Console.writeln("   B: "+File.extractName(plan.b));
      var combo = combineRGB(plan.r, plan.g, plan.b, root);
      processOne(combo.window, combo.base, root, state.ai, state.save.rgb, /*isRGBCombined*/true, state.colorEnhanceRGBStarsStretched);
      try{ combo.window.forceClose(); }catch(_){}
    }
    if (doMono){
      if (plan.singles.length===0){
        Console.writeln("\nNo mono/narrowband masters found.");
      } else {
        Console.writeln("\n→ Processing mono/narrowband singles: "+plan.singles.length);
        for (var i=0;i<plan.singles.length;++i){
          var p = plan.singles[i].path;
          var tag = plan.singles[i].tag;
          Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");
          var w=ImageWindow.open(p);
          if(w.length===0){ Console.writeln("  ⚠️ Could not open, skipping."); continue; }
          var win=w[0], base= tag + "_" + sanitizeBase(File.extractName(p));
          var saveCfg = (win.mainView.image.isColor ? state.save.rgb : state.save.mono);
          if (!shouldProcessConfig(saveCfg)){ Console.writeln("  ⏭️ Skipped (outputs disabled for this class)."); try{ win.forceClose(); }catch(_){ } continue; }
          processOne(win, base, root, state.ai, saveCfg, /*isRGBCombined*/false, /*enhanceStars*/false);
          try{ win.forceClose(); }catch(_){}
          closeAllWindowsExcept(null);
        }
      }
    }
    Console.writeln("\n=== Done. Output: "+root+" ===");
  }catch(err){
    Console.criticalln("Error: "+err.message); throw err;
  }
}
run();
})(); // IIFE
