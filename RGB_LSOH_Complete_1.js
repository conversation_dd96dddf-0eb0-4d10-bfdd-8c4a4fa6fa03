/*
 * Post-Integration Pipeline — Mixed Sets (RGB if present + all others)
 * Adds final, *proper* star-mask generation via subtraction:
 *   Stars = Final_Stacked_<tag>  −  Starless_<tag>
 * For stretched stars: we STF both inputs, then subtract.
 */

#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>

(function(){

// -------------------- AutoSTF constants (tighter/darker) --------------------
var STF_UNLINKED        = true;   // nuclear per-channel
var STF_K_SHADOWS       = -3.0;   // darker black point
var STF_TARGET_BG       = 0.22;   // lower overall brightness
var STF_HIGHLIGHTS      = 1.0;
var STF_MAX_SAMPLES     = 120000;

var outputExtension = ".xisf";

// -------------------- Defaults (empty paths for cross-OS) --------------------
var defaults = {
  inputDir:  "",
  outputDir: "",
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },

  // Color enhance only for RGB Stars (stretched)
  colorEnhanceRGBStarsStretched: true,

  // Save options — lean defaults
  save: {
    // RGB (color)
    rgb: {
      // Finals
      final_stretched: true,   // Final (stretched, with stars)
      final_linear:    false,  // Final (linear, with stars)
      // Stars/Starless
      stars_linear:          true,   // <-- drives linear subtraction mask
      stars_stretched:       true,   // <-- drives stretched subtraction mask
      starless_linear:       false,
      starless_stretched:    false,
      // Intermediates
      integration_linear:    false,
      baseline_linear:       false,
      deblur1:               false,
      deblur2:               false,
      denoised:              false
    },
    // Mono/NB
    mono: {
      final_stretched: false,
      final_linear:    false,
      stars_linear:          false,
      stars_stretched:       false,
      starless_linear:       false,
      starless_stretched:    true,   // default mono deliverable
      integration_linear:    false,
      baseline_linear:       false,
      deblur1:               false,
      deblur2:               false,
      denoised:              false
    }
  }
};

// -------------------- Utils --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){
  try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){}
}

// -------------------- Discovery --------------------
function findAllXISF(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[], ff=new FileFind;
  if(ff.begin(dir+"/*.xisf")){ do{ v.push(dir+"/"+ff.name); }while(ff.next()); }
  return v;
}

function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /\bred\b/.test(s) || (/\br\b/.test(s)&&s.indexOf("rgb")<0)) return "R";
  if (s.indexOf("filter-green")>=0 || /\bgreen\b/.test(s) || (/\bg\b/.test(s)&&s.indexOf("rgb")<0)) return "G";
  if (s.indexOf("filter-blue")>=0 || /\bblue\b/.test(s) || (/\bb\b/.test(s)&&s.indexOf("rgb")<0)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}

function buildWorkPlan(dir, combineRGB){
  var files = findAllXISF(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }

  var haveR=!!(byFilter.R&&byFilter.R.length),
      haveG=!!(byFilter.G&&byFilter.G.length),
      haveB=!!(byFilter.B&&byFilter.B.length);

  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };

  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }

  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) plan.singles.push({ path:unknownSingles[k], tag:"Single" });

  return plan;
}

// -------------------- RGB Combination --------------------
function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");

  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();

  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");

  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}

// -------------------- ABE & BlackPoint --------------------
function finalABE(win, sum){
  try{
    var P=new AutomaticBackgroundExtractor();
    P.targetCorrection=1; P.normalize=true; P.discardBackground=true;
    var before=ImageWindow.windows; P.executeOn(win.mainView); var after=ImageWindow.windows;
    for(var k=0;k<after.length;++k){
      var isNew=true; for(var j=0;j<before.length;++j){ if(after[k].mainView.id===before[j].mainView.id){ isNew=false; break; } }
      if(isNew && after[k].mainView.id.toLowerCase().indexOf("background")!==-1){ after[k].forceClose(); break; }
    }
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE complete"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}

function autoBlackPoint(win, sum){
  try{
    var img=win.mainView.image;
    if(!img.isColor || img.numberOfChannels<3){
      var AH=new AutoHistogram(); AH.auto=true; AH.clipLow=0.1; AH.clipHigh=0.1; AH.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"AutoHistogram (mono/NB)"}; return true;
    }
    var w=img.width,h=img.height, sampleSize=20, numSamples=20;
    var rs=[],gs=[],bs=[];
    for(var i=0;i<numSamples;++i){
      var x=Math.floor(Math.random()*(w-sampleSize)), y=Math.floor(Math.random()*(h-sampleSize));
      var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;
      for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }
      rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);
    }
    rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});
    var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0; for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; } R/=n; G/=n; B/=n;

    try{
      var L=new Levels(); L.redBlack=Math.min(R*0.8,0.02); L.greenBlack=Math.min(G*0.9,0.03); L.blueBlack=Math.min(B*0.8,0.02);
      L.redWhite=0.98; L.greenWhite=0.97; L.blueWhite=0.98; L.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Levels"};
    }catch(e2){
      var AH2=new AutoHistogram(); AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1; AH2.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};
    }
    return true;
  }catch(e){ sum.blackPoint={name:"Black Point",status:"❌",details:e.message}; return false; }
}

// -------------------- ImageSolver + SPCC --------------------
function solveImage(win, sum){
  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    var solver = new ImageSolver();
    solver.Init(win, false);
    solver.useMetadata = true;
    solver.catalog = "GAIA DR3";
    solver.useDistortionCorrection = false;
    solver.generateErrorMaps = false;
    solver.showStars = false;
    solver.showDistortion = false;
    solver.generateDistortionMaps = false;
    solver.sensitivity = 0.1;

    if (!solver.SolveImage(win)){
      Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");
      solver.useMetadata = false;
      if (!solver.SolveImage(win)) throw new Error("Plate solution not found.");
    }
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}

// profileSelector: "OSC" or "RGB"
function performSPCC(win, sum, profileSelector){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();

    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.generateGraphs=true; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = +2.00; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}

    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied"};
    return true;

  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}

// -------------------- AI steps --------------------
function deblur1(win, sum){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
    P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV1={name:"Deblur V1",status:"✅",details:"Round stars"};
    return true;
  }catch(e){ sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message}; return false; }
}
function deblur2(win, sum){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
    P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};
    return true;
  }catch(e){ sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message}; return false; }
}
function denoise(win, sum){
  try{
    var P=new NoiseXTerminator();
    P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
    P.executeOn(win.mainView);
    sum.denoising={name:"Denoising",status:"✅",details:"Applied"};
    return true;
  }catch(e){ sum.denoising={name:"Denoising",status:"❌",details:e.message}; return false; }
}

// -------------------- AutoSTF→HT helpers --------------------
function clamp(x,a,b){ return x<a?a:(x>b?b:x); }
function median(v){ var n=v.length; if(!n) return 0; v.sort(function(a,b){return a-b;}); return (n&1)? v[(n-1)>>1] : 0.5*(v[n>>1]+v[(n>>1)-1]); }
function mad(v, med){ var a=new Array(v.length); for (var i=0;i<v.length;++i) a[i]=Math.abs(v[i]-med); return median(a); }

function sampleChannel(img, ch){
  var W=img.width, H=img.height, tot=W*H;
  var step = Math.max(1, Math.floor(Math.sqrt(tot/ STF_MAX_SAMPLES)));
  var vals=[];
  if (img.isColor){
    for (var y=0; y<H; y+=step)
      for (var x=0; x<W; x+=step)
        vals.push( img.sample(x, y, ch) );
  } else {
    for (var y=0; y<H; y+=step)
      for (var x=0; x<W; x+=step)
        vals.push( img.sample(x, y) );
  }
  return vals;
}
function sampleLuminance(img){
  var W=img.width, H=img.height, tot=W*H;
  var step = Math.max(1, Math.floor(Math.sqrt(tot/ STF_MAX_SAMPLES)));
  var vals=[];
  for (var y=0; y<H; y+=step){
    for (var x=0; x<W; x+=step){
      if (img.isColor){
        var r = img.sample(x,y,0), g = img.sample(x,y,1), b = img.sample(x,y,2);
        vals.push( 0.2126*r + 0.7152*g + 0.0722*b );
      } else vals.push( img.sample(x,y) );
    }
  }
  return vals;
}
function mtfMidtonesFromTarget(normBg, target){
  normBg = clamp(normBg, 1e-6, 0.999999);
  target = clamp(target, 1e-6, 0.999999);
  return clamp( Math.exp( Math.log(0.5)*Math.log(normBg)/Math.log(target) ), 0.001, 0.999 );
}
function computeSTF(img){
  var s=[0,0,0], m=[0.5,0.5,0.5], h=[1,1,1];
  if (img.isColor && STF_UNLINKED){
    for (var c=0;c<3;++c){
      var v   = sampleChannel(img, c);
      var med = median(v);
      var sig = 1.4826*mad(v, med);
      var sc  = clamp(med + STF_K_SHADOWS*sig, 0, 0.99);
      var hc  = STF_HIGHLIGHTS;
      var B   = clamp( (med - sc)/(hc - sc), 1e-6, 0.999999 );
      var mc  = mtfMidtonesFromTarget(B, STF_TARGET_BG);
      s[c]=sc; m[c]=mc; h[c]=hc;
    }
  } else {
    var v   = sampleLuminance(img);
    var med = median(v);
    var sig = 1.4826*mad(v, med);
    var sc  = clamp(med + STF_K_SHADOWS*sig, 0, 0.99);
    var hc  = STF_HIGHLIGHTS;
    var B   = clamp( (med - sc)/(hc - sc), 1e-6, 0.999999 );
    var mc  = mtfMidtonesFromTarget(B, STF_TARGET_BG);
    s=[sc,sc,sc]; m=[mc,mc,mc]; h=[hc,hc,hc];
  }
  return {s:s,m:m,h:h};
}
function applyHT(view, stf){
  var P=new HistogramTransformation;
  P.H = [
    [ stf.s[0], stf.m[0], stf.h[0], 0, 1 ],
    [ stf.s[1], stf.m[1], stf.h[1], 0, 1 ],
    [ stf.s[2], stf.m[2], stf.h[2], 0, 1 ],
    [ stf.s[0], stf.m[0], stf.h[0], 0, 1 ]
  ];
  P.executeOn(view);
}
function stretchImageAtStage(inPath, outPath, saveFlag){
  var w = ImageWindow.open(inPath);
  if(!w.length) return null;
  var win=w[0], img=win.mainView.image;
  var stf = computeSTF(img);
  applyHT(win.mainView, stf);
  if(saveFlag) win.saveAs(outPath,false,false,false,false);
  return win;
}

// -------------------- Color Enhance helpers (RGB Stars stretched only) --------------------
function applyColorEnhanceToView(view){
  var C=new CurvesTransformation;
  C.R = [[0,0],[1,1]]; C.G = [[0,0],[1,1]]; C.B = [[0,0],[1,1]];
  C.K = [[0,0],[1,1]]; C.A = [[0,0],[1,1]]; C.L = [[0,0],[1,1]];
  C.a = [[0,0],[1,1]]; C.b = [[0,0],[1,1]]; C.c = [[0,0],[1,1]]; C.H = [[0,0],[1,1]];
  C.S = [[0.00000,0.00000],[0.14470,0.33247],[1.00000,1.00000]];
  C.Rt=C.AkimaSubsplines; C.Gt=C.AkimaSubsplines; C.Bt=C.AkimaSubsplines;
  C.Kt=C.AkimaSubsplines; C.At=C.AkimaSubsplines; C.Lt=C.AkimaSubsplines;
  C.at=C.AkimaSubsplines; C.bt=C.AkimaSubsplines; C.ct=C.AkimaSubsplines; C.Ht=C.AkimaSubsplines; C.St=C.AkimaSubsplines;
  C.executeOn(view);

  var N=new SCNR; N.amount=0.73; N.protectionMethod=SCNR.prototype.AverageNeutral; N.colorToRemove=SCNR.prototype.Green; N.preserveLightness=true;
  N.executeOn(view);
}

// -------------------- NEW: Core star-mask subtraction helpers --------------------
function pixelMathSubtract(targetView, aId, bId, newId){
  var pm = new PixelMath();
  pm.expression = aId + " - " + bId;
  pm.useSingleExpression = true;
  pm.rescale = true; pm.rescaleLower=0.0; pm.rescaleUpper=1.0;
  pm.truncate=false; pm.createNewImage=true;
  pm.newImageId = newId; pm.newImageWidth=0; pm.newImageHeight=0; pm.newImageSampleFormat=0;
  pm.executeOn(targetView, false);

  var outWin = ImageWindow.windowById(newId);
  if(!outWin) throw new Error("PixelMath subtraction failed: "+newId);
  var outView = outWin.mainView;
  outView.beginProcess(UndoFlag_NoSwapFile); outView.image.truncate(0.0,1.0); outView.endProcess();
  return outWin;
}

// subtract two *files*; optionally bake STF to both before subtraction
function subtractFilesToPath(starsPath, starlessPath, outPath, bakeSTF){
  var wA = ImageWindow.open(starsPath); if(!wA.length) throw new Error("Open failed: "+starsPath);
  var wB = ImageWindow.open(starlessPath); if(!wB.length) throw new Error("Open failed: "+starlessPath);
  var A = wA[0], B = wB[0];

  // Geometry guard
  var aimg=A.mainView.image, bimg=B.mainView.image;
  if(aimg.width!==bimg.width || aimg.height!==bimg.height || aimg.numberOfChannels!==bimg.numberOfChannels ||
     aimg.sampleType!==bimg.sampleType || aimg.bitsPerSample!==bimg.bitsPerSample)
    throw new Error("Geometry/sample mismatch between stars and starless.");

  // Optional STF bake (for stretched masks)
  if(bakeSTF){
    var stA=computeSTF(aimg); applyHT(A.mainView, stA);
    var stB=computeSTF(bimg); applyHT(B.mainView, stB);
  }

  // Set temporary ids
  A.mainView.id = "StarsObj_A"; B.mainView.id = "Starless_B";
  var out = pixelMathSubtract(A.mainView, "StarsObj_A", "Starless_B", "StarsMinusStarless");
  out.saveAs(outPath,false,false,false,false);

  // cleanup
  try{ out.forceClose(); }catch(_){}
  try{ A.forceClose(); }catch(_){}
  try{ B.forceClose(); }catch(_){}
}

// -------------------- StarX + save starless; then subtraction masks --------------------
function starSeparationAndTrueMasks(win, sum, finalDir, baseTag, opts, isRGBCombined, enhanceRGBStarsStretched){
  var needStarless = opts.starless_linear || opts.starless_stretched;
  var needStarsLinear   = opts.stars_linear;
  var needStarsStretched= opts.stars_stretched;

  if(!(needStarless || needStarsLinear || needStarsStretched)){
    sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
    return true;
  }

  try{
    // Run SXT to create STARLESS ONLY (no screened stars image!)
    var P=new StarXTerminator();
    P.generateStarImage=false; // <- disable screened/unscreened stars image generation
    P.unscreenStars=false;     // <- irrelevant if generateStarImage=false; ensures nothing extra
    P.largeOverlap=false;
    P.executeOn(win.mainView); // replaces current 'win' image with STARLESS content

    var pathStarlessL = finalDir+"/Starless_"+baseTag+outputExtension;
    var pathStarlessS = finalDir+"/Starless_Stretched_"+baseTag+outputExtension;

    // Save requested starless versions
    if(opts.starless_linear)   win.saveAs(pathStarlessL,false,false,false,false);
    if(opts.starless_stretched){
      var stf1 = computeSTF(win.mainView.image); applyHT(win.mainView, stf1);
      win.saveAs(pathStarlessS,false,false,false,false);
    }

    // Build TRUE star masks by subtraction from the *baseline with stars*
    var pathBaselineL = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
    var pathStretched = finalDir+"/Stretched_"+baseTag+outputExtension; // may or may not exist

    // Linear star mask (no stretch baked)
    if(needStarsLinear){
      if(!File.exists(pathStarlessL)){
        // create a temporary linear starless file (we're already linear here)
        win.saveAs(pathStarlessL,false,false,false,false);
      }
      var outStarsL = finalDir+"/Stars_"+baseTag+outputExtension;
      subtractFilesToPath(pathBaselineL, pathStarlessL, outStarsL, /*bakeSTF*/false);
    }

    // Stretched star mask (STF baked to both before subtraction)
    if(needStarsStretched){
      // Ensure we have a stretched "with stars" frame to use as *source* (we only need its data extent/format)
      var haveStretched = File.exists(pathStretched);
      // Ensure we have a stretched starless file for reference/inspection (not strictly required for subtraction below)
      if(!File.exists(pathStarlessS)){
        // bake stretch to the starless (from current starless 'win')
        var tmpS = finalDir+"/__tmp_starless_for_mask"+outputExtension;
        var tmpWin = ImageWindow.open(pathBaselineL)[0]; // reopen baseline only to get a target for PixelMath; we'll close it
        try{ tmpWin.forceClose(); }catch(_){}
        var stf2 = computeSTF(win.mainView.image); applyHT(win.mainView, stf2);
        win.saveAs(pathStarlessS,false,false,false,false);
        removeIf(tmpS,false);
      }
      var outStarsS = finalDir+"/Stars_Stretched_"+baseTag+outputExtension;
      // We do *not* need the stretched-with-stars file on disk; we bake STF on the baseline and on the starless and subtract.
      subtractFilesToPath(pathBaselineL, pathStarlessL, outStarsS, /*bakeSTF*/true);
      // optional RGB-only color enhance of stretched star mask (matches previous behavior if you want it colorful)
      if(isRGBCombined && enhanceRGBStarsStretched){
        var wS=ImageWindow.open(outStarsS); if(wS.length){ applyColorEnhanceToView(wS[0].mainView); wS[0].saveAs(outStarsS,false,false,false,false); try{ wS[0].forceClose(); }catch(_){ } }
      }
      if(!haveStretched) removeIf(pathStretched,false);
    }

    sum.starSeparation={name:"Star Separation",status:"✅",details:"Starless made with SXT; star masks by subtraction"};
    return true;
  }catch(e){
    sum.starSeparation={name:"Star Separation",status:"❌",details:e.message}; return false;
  }
}

// -------------------- Per-Image Pipeline --------------------
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched){
  var sum={};

  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);

  // Save integration (linear) — always save for pipeline, remove later if not requested
  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};
  closeAllWindowsExcept([win.mainView.id]);

  // Linear steps
  finalABE(win,sum);             closeAllWindowsExcept([win.mainView.id]);
  autoBlackPoint(win,sum);       closeAllWindowsExcept([win.mainView.id]);

  // ----- Solve + SPCC on COLOR only -----
  var isColor = win.mainView.image.isColor;
  if(isColor){
    var solved = solveImage(win, sum);
    if(solved){
      var spccProfile = isRGBCombined ? "RGB" : "OSC";
      performSPCC(win, sum, spccProfile);
    } else {
      sum.spcc = {name:"SPCC",status:"⏭️",details:"Skipped (no WCS)"};
    }
  } else {
    sum.solver = {name:"ImageSolver",status:"⏭️",details:"Skipped (mono/NB)"};
    sum.spcc   = {name:"SPCC",status:"⏭️",details:"Skipped (mono/NB)"};
  }
  closeAllWindowsExcept([win.mainView.id]);

  // Baseline (linear with stars)
  var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};

  // Deblur V1 (linear)
  if(ai.deblur1){
    var w1=ImageWindow.open(baseline)[0];
    if(w1 && deblur1(w1,sum) && saveCfg.deblur1)
      w1.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
    try{ w1.forceClose(); }catch(_){}
  }

  // Deblur V2 (linear)
  if(ai.deblur2){
    var w2=ImageWindow.open(baseline)[0];
    if(w2 && deblur2(w2,sum) && saveCfg.deblur2)
      w2.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
    try{ w2.forceClose(); }catch(_){}
  }

  // Stretch (after Deblur V2)
  var stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
  if(ai.stretch){
    var wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/true);
    if(wS) try{ wS.forceClose(); }catch(_){}
  }

  // Denoise (prefer stretched if exists)
  if(ai.denoise){
    var dnIn = ai.stretch ? stretchedPath : baseline;
    var w3 = ImageWindow.open(dnIn)[0];
    if(w3 && denoise(w3,sum) && saveCfg.denoised)
      w3.saveAs(finalDir+"/Denoised_"+baseTag+outputExtension,false,false,false,false);
    try{ w3.forceClose(); }catch(_){}
  }

  // StarX → starless; then TRUE star masks by subtraction (final stage)
  if(ai.starless){
    var needAny = (saveCfg.stars_linear||saveCfg.stars_stretched||saveCfg.starless_linear||saveCfg.starless_stretched);
    if(needAny){
      var w4=ImageWindow.open(baseline)[0];
      if(w4) starSeparationAndTrueMasks(
        w4,sum,finalDir,baseTag,
        {
          stars_linear:       saveCfg.stars_linear,
          stars_stretched:    saveCfg.stars_stretched,
          starless_linear:    saveCfg.starless_linear,
          starless_stretched: saveCfg.starless_stretched
        },
        isRGBCombined, enhanceRGBStarsStretched
      );
      try{ w4.forceClose(); }catch(_){}
    }else{
      sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
    }
  }

  // Clean up intermediates if user doesn't want them
  removeIf(integrationPath, saveCfg.integration_linear===true);
  removeIf(baseline,        saveCfg.baseline_linear===true);

  if(ai.stretch){
    var needStretchedFinal = saveCfg.final_stretched===true;
    if(!needStretchedFinal && saveCfg.denoised!==true)
      removeIf(stretchedPath, /*keep*/false);
  }
  if(!saveCfg.final_linear)
    removeIf(baseline, saveCfg.baseline_linear===true);

  // Summary
  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction","blackPoint","solver","spcc","deblurV1","deblurV2","denoising","starSeparation","finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}

// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog; dlg.windowTitle="Post-Integration Pipeline (Mixed Sets)";
  dlg.sizer=new VerticalSizer; dlg.sizer.margin=10; dlg.sizer.spacing=8;

  var head=new Label(dlg); head.text="Utah Masterclass — Post-Integration Pipeline"; head.textAlignment=TextAlign_Center; dlg.sizer.add(head);

  var gIn=new GroupBox(dlg); gIn.title="Input Directory"; gIn.sizer=new VerticalSizer; gIn.sizer.margin=8; gIn.sizer.spacing=6;
  var rowIn=new HorizontalSizer; rowIn.spacing=6;
  var editIn=new Edit(dlg); editIn.readOnly=true; editIn.minWidth=560; editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg); btnIn.text="Browse...";
  btnIn.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Input Directory"; d.initialDirectory=state.inputDir||""; if(d.execute()){ state.inputDir=fwd(d.directory).replace(/\/$/,""); editIn.text=state.inputDir; } };
  rowIn.add(editIn,100); rowIn.add(btnIn); gIn.sizer.add(rowIn); dlg.sizer.add(gIn);

  var gOut=new GroupBox(dlg); gOut.title="Output Base Directory (a timestamped subfolder will be created)"; gOut.sizer=new VerticalSizer; gOut.sizer.margin=8; gOut.sizer.spacing=6;
  var rowOut=new HorizontalSizer; rowOut.spacing=6;
  var editOut=new Edit(dlg); editOut.readOnly=true; editOut.minWidth=560; editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg); btnOut.text="Browse...";
  btnOut.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Output Base Directory"; d.initialDirectory=state.outputDir||""; if(d.execute()){ state.outputDir=fwd(d.directory).replace(/\/$/,""); editOut.text=state.outputDir; } };
  rowOut.add(editOut,100); rowOut.add(btnOut); gOut.sizer.add(rowOut); dlg.sizer.add(gOut);

  var gOpt=new GroupBox(dlg); gOpt.title="Processing Options"; gOpt.sizer=new VerticalSizer; gOpt.sizer.margin=8; gOpt.sizer.spacing=6;
  var cbCombine=new CheckBox(dlg); cbCombine.text="Combine RGB if R+G+B masters are found (also process all other masters individually)"; cbCombine.checked=state.combineRGB; gOpt.sizer.add(cbCombine);
  var rowAI1=new HorizontalSizer; rowAI1.spacing=12;
  var cbD1=new CheckBox(dlg); cbD1.text="Deblur V1 (round stars)"; cbD1.checked=state.ai.deblur1;
  var cbD2=new CheckBox(dlg); cbD2.text="Deblur V2 (enhance)";   cbD2.checked=state.ai.deblur2;
  var cbST=new CheckBox(dlg); cbST.text="Stretch (AutoSTF→HT)";  cbST.checked=state.ai.stretch;
  rowAI1.add(cbD1); rowAI1.add(cbD2); rowAI1.add(cbST); rowAI1.addStretch(); gOpt.sizer.add(rowAI1);
  var rowAI2=new HorizontalSizer; rowAI2.spacing=12;
  var cbDN=new CheckBox(dlg); cbDN.text="Denoise"; cbDN.checked=state.ai.denoise;
  var cbSL=new CheckBox(dlg); cbSL.text="Enable StarX (only runs if star/starless outputs requested)"; cbSL.checked=state.ai.starless;
  rowAI2.add(cbDN); rowAI2.add(cbSL); rowAI2.addStretch(); gOpt.sizer.add(rowAI2);
  dlg.sizer.add(gOpt);

  var gSave=new GroupBox(dlg); gSave.title="Save Options (defaults reduce clutter)"; gSave.sizer=new VerticalSizer; gSave.sizer.margin=8; gSave.sizer.spacing=6;

  // RGB finals
  var rowR1=new HorizontalSizer; rowR1.spacing=10;
  var lR1=new Label(dlg); lR1.text="RGB finals:"; lR1.minWidth=120;
  var rFinalS=new CheckBox(dlg); rFinalS.text="Final (stretched, with stars)"; rFinalS.checked=state.save.rgb.final_stretched;
  var rFinalL=new CheckBox(dlg); rFinalL.text="Final (linear, with stars)";   rFinalL.checked=state.save.rgb.final_linear;
  rowR1.add(lR1); rowR1.add(rFinalS); rowR1.add(rFinalL); rowR1.addStretch(); gSave.sizer.add(rowR1);

  // RGB star/starless (+ enhance toggle)
  var rowR2=new HorizontalSizer; rowR2.spacing=10;
  var lR2=new Label(dlg); lR2.text="RGB stars/starless:"; lR2.minWidth=120;
  var rStarsL=new CheckBox(dlg); rStarsL.text="Stars (linear)";        rStarsL.checked=state.save.rgb.stars_linear;
  var rStarsS=new CheckBox(dlg); rStarsS.text="Stars (stretched)";     rStarsS.checked=state.save.rgb.stars_stretched;
  var rSLessL=new CheckBox(dlg); rSLessL.text="Starless (linear)";     rSLessL.checked=state.save.rgb.starless_linear;
  var rSLessS=new CheckBox(dlg); rSLessS.text="Starless (stretched)";  rSLessS.checked=state.save.rgb.starless_stretched;
  var rStarsEnh=new CheckBox(dlg); rStarsEnh.text="Color enhance Stars (stretched) [RGB only]"; rStarsEnh.checked=defaults.colorEnhanceRGBStarsStretched;
  rowR2.add(lR2); rowR2.add(rStarsL); rowR2.add(rStarsS); rowR2.add(rSLessL); rowR2.add(rSLessS); rowR2.add(rStarsEnh); rowR2.addStretch(); gSave.sizer.add(rowR2);

  // Mono finals
  var rowM1=new HorizontalSizer; rowM1.spacing=10;
  var lM1=new Label(dlg); lM1.text="Mono/NB finals:"; lM1.minWidth=120;
  var mFinalS=new CheckBox(dlg); mFinalS.text="Final (stretched, with stars)"; mFinalS.checked=state.save.mono.final_stretched;
  var mFinalL=new CheckBox(dlg); mFinalL.text="Final (linear, with stars)";   mFinalL.checked=state.save.mono.final_linear;
  var mSLessS=new CheckBox(dlg); mSLessS.text="Starless (stretched)";         mSLessS.checked=state.save.mono.starless_stretched;
  var mSLessL=new CheckBox(dlg); mSLessL.text="Starless (linear)";            mSLessL.checked=state.save.mono.starless_linear;
  var mStarsS=new CheckBox(dlg); mStarsS.text="Stars (stretched)";            mStarsS.checked=state.save.mono.stars_stretched;
  var mStarsL=new CheckBox(dlg); mStarsL.text="Stars (linear)";               mStarsL.checked=state.save.mono.stars_linear;
  rowM1.add(lM1); rowM1.add(mFinalS); rowM1.add(mFinalL); rowM1.add(mSLessS); rowM1.add(mSLessL); rowM1.add(mStarsS); rowM1.add(mStarsL); rowM1.addStretch(); gSave.sizer.add(rowM1);

  // Intermediates
  var rowI=new HorizontalSizer; rowI.spacing=10;
  var lI=new Label(dlg); lI.text="Intermediates:"; lI.minWidth=120;
  var iInt = new CheckBox(dlg); iInt.text="Integration (linear)"; iInt.checked=state.save.rgb.integration_linear;
  var iBase= new CheckBox(dlg); iBase.text="Baseline (linear)";    iBase.checked=state.save.rgb.baseline_linear;
  var iD1  = new CheckBox(dlg); iD1.text="Save Round Stars (Deblur V1)";  iD1.checked=state.save.rgb.deblur1;
  var iD2  = new CheckBox(dlg); iD2.text="Save Enhance (Deblur V2)";      iD2.checked=state.save.rgb.deblur2;
  var iDN  = new CheckBox(dlg); iDN.text="Save Denoised";                 iDN.checked=state.save.rgb.denoised;
  rowI.add(lI); rowI.add(iInt); rowI.add(iBase); rowI.add(iD1); rowI.add(iD2); rowI.add(iDN); rowI.addStretch(); gSave.sizer.add(rowI);

  dlg.sizer.add(gSave);

  var rowBtn=new HorizontalSizer; rowBtn.spacing=8; rowBtn.addStretch();
  var bStart=new PushButton(dlg); bStart.text="Start"; bStart.defaultButton=true;
  var bCancel=new PushButton(dlg); bCancel.text="Cancel";
  rowBtn.add(bStart); rowBtn.add(bCancel); dlg.sizer.add(rowBtn);

  bCancel.onClick=function(){ dlg.cancel(); };
  bStart.onClick=function(){
    if(!state.inputDir || !File.directoryExists(state.inputDir)){ (new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute(); return; }
    if(!state.outputDir || !File.directoryExists(state.outputDir)){ (new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute(); return; }
    state.combineRGB = cbCombine.checked;
    state.ai.deblur1=cbD1.checked; state.ai.deblur2=cbD2.checked; state.ai.stretch=cbST.checked; state.ai.denoise=cbDN.checked; state.ai.starless=cbSL.checked;

    defaults.colorEnhanceRGBStarsStretched = rStarsEnh.checked;

    state.save.rgb = {
      final_stretched:rFinalS.checked, final_linear:rFinalL.checked,
      stars_linear:rStarsL.checked, stars_stretched:rStarsS.checked,
      starless_linear:rSLessL.checked, starless_stretched:rSLessS.checked,
      integration_linear:iInt.checked, baseline_linear:iBase.checked,
      deblur1:iD1.checked, deblur2:iD2.checked, denoised:iDN.checked
    };
    state.save.mono = {
      final_stretched:mFinalS.checked, final_linear:mFinalL.checked,
      stars_linear:mStarsL.checked, stars_stretched:mStarsS.checked,
      starless_linear:mSLessL.checked, starless_stretched:mSLessS.checked,
      integration_linear:iInt.checked, baseline_linear:iBase.checked,
      deblur1:iD1.checked, deblur2:iD2.checked, denoised:iDN.checked
    };
    dlg.ok();
  };

  return dlg.execute();
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (Mixed Sets) — ImageSolver before SPCC + TRUE Star Masks ===");

  var state={ inputDir:defaults.inputDir, outputDir:defaults.outputDir, combineRGB:defaults.combineRGB,
              ai:{ deblur1:defaults.ai.deblur1, deblur2:defaults.ai.deblur2, stretch:defaults.ai.stretch, denoise:defaults.ai.denoise, starless:defaults.ai.starless },
              save:{ rgb:defaults.save.rgb, mono:defaults.save.mono } };

  if(!showGUI(state)){ Console.writeln("Cancelled."); return; }

  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);

  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder (single): "+root);
  ensureDir(root+"/5_stacked"); ensureDir(root+"/6_final");

  try{
    var plan = buildWorkPlan(state.inputDir, state.combineRGB);

    // RGB first if planned
    if (plan.doRGB){
      Console.writeln("\n→ Building RGB from:");
      Console.writeln("   R: "+File.extractName(plan.r));
      Console.writeln("   G: "+File.extractName(plan.g));
      Console.writeln("   B: "+File.extractName(plan.b));
      var combo = combineRGB(plan.r, plan.g, plan.b, root);
      processOne(combo.window, combo.base, root, state.ai, state.save.rgb, /*isRGBCombined*/true, /*enhanceStars*/defaults.colorEnhanceRGBStarsStretched);
      try{ combo.window.forceClose(); }catch(_){}
    } else {
      Console.writeln("\n(No full RGB set found or combining disabled)");
    }

    // Singles
    if (plan.singles.length===0){
      Console.writeln("\nNo remaining masters found to process.");
    } else {
      Console.writeln("\n→ Processing singles: "+plan.singles.length);
      for (var i=0;i<plan.singles.length;++i){
        var p = plan.singles[i].path;
        var tag = plan.singles[i].tag;
        Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");
        var w=ImageWindow.open(p);
        if(w.length===0){ Console.writeln("  ⚠️ Could not open, skipping."); continue; }
        var win=w[0], base= tag + "_" + sanitizeBase(File.extractName(p));
        var saveCfg = (win.mainView.image.isColor ? state.save.rgb : state.save.mono);
        processOne(win, base, root, state.ai, saveCfg, /*isRGBCombined*/false, /*enhanceStars*/false);
        try{ win.forceClose(); }catch(_){}
        closeAllWindowsExcept(null);
      }
    }

    Console.writeln("\n=== Done. Output: "+root+" ===");
  }catch(err){
    Console.criticalln("Error: "+err.message); throw err;
  }
}

run();

})(); // IIFE
