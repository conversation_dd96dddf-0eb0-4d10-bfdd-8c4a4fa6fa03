// =================================================================
// PixInsight Script to Load R, G, B XISF Images Based on FILTER Header
// Then Combine Using ChannelCombination
// =================================================================

#include <pjsr/DataType.jsh>

function getFilterName(filePath) {
    var file = new File;
    if (!file.openForReading(filePath))
        return "";

    var header = file.readFITSHeader();
    file.close();

    // Search for FILTER keyword
    var match = header.match(/FILTER\s*=\s*'([^']+)'/i);
    if (match && match.length > 1)
        return match[1].trim();
    else
        return "";
}

function loadImageByFilter(folder, filterName) {
    var files = new Array;
    var ff = new FileFind;

    // Find all XISF files in the directory
    if (ff.begin(folder + "/*.xisf")) {
        do {
            files.push(folder + "/" + ff.name);
        } while (ff.next());
        ff.end();
    }

    for (var i = 0; i < files.length; ++i) {
        var filePath = files[i];
        var filter = getFilterName(filePath);

        if (filter && filter.toLowerCase().indexOf(filterName.toLowerCase()) !== -1) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0)
                return windowArray[0];
        }
    }

    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("Loading Red image...");
    var redWindow = loadImageByFilter(inputDir, "R");
    if (!redWindow) {
        console.criticalln("Failed to load red channel image.");
        return;
    }

    console.writeln("Loading Green image...");
    var greenWindow = loadImageByFilter(inputDir, "G");
    if (!greenWindow) {
        console.criticalln("Failed to load green channel image.");
        return;
    }

    console.writeln("Loading Blue image...");
    var blueWindow = loadImageByFilter(inputDir, "B");
    if (!blueWindow) {
        console.criticalln("Failed to load blue channel image.");
        return;
    }

    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    var combo = new ChannelCombination;
    combo.channels = [
        [redView],
        [greenView],
        [blueView]
    ];

    // Create a new RGB image
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,
        BitDepth_IEEE32,
        true,
        false,
        "Combined_RGB_Image"
    );

    combo.executeOn(outputImage.mainView);

    outputImage.show();

    console.writeln("RGB channels successfully combined.");
}

main();
