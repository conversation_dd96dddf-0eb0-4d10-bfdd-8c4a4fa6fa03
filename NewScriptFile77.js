#include <pjsr/Sizer.jsh>
#include <pjsr/FrameStyle.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/StdIcon.jsh>

/*
 * RGB Channel Combination Script for PixInsight
 * Automatically combines integrated R, G, B master frames using ChannelCombination
 * Reads filter information from FITS headers and file names
 */

// Configuration
var INPUT_DIRECTORY = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";
var OUTPUT_DIRECTORY = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master"; // Can be changed if needed

function findMasterFrames() {
    var dir = new FileFind;
    var files = {
        red: "",
        green: "",
        blue: ""
    };

    if (!dir.begin(INPUT_DIRECTORY + "/*.xisf")) {
        throw new Error("Cannot access input directory: " + INPUT_DIRECTORY);
    }

    do {
        var fileName = dir.name;
        var fullPath = INPUT_DIRECTORY + "/" + fileName;

        // Check if it's a master light frame
        if (fileName.indexOf("masterLight") >= 0 && fileName.indexOf(".xisf") >= 0) {
            // Try to determine filter from filename first
            var filterFromName = "";
            if (fileName.indexOf("FILTER-Red") >= 0 || fileName.indexOf("Red") >= 0) {
                filterFromName = "Red";
            } else if (fileName.indexOf("FILTER-Green") >= 0 || fileName.indexOf("Green") >= 0) {
                filterFromName = "Green";
            } else if (fileName.indexOf("FILTER-Blue") >= 0 || fileName.indexOf("Blue") >= 0) {
                filterFromName = "Blue";
            }

            // If we found a filter in the filename, assign it
            if (filterFromName == "Red") {
                files.red = fullPath;
                console.writeln("Found Red frame: " + fileName);
            } else if (filterFromName == "Green") {
                files.green = fullPath;
                console.writeln("Found Green frame: " + fileName);
            } else if (filterFromName == "Blue") {
                files.blue = fullPath;
                console.writeln("Found Blue frame: " + fileName);
            }
        }
    } while (dir.next());

    return files;
}

function combineRGBChannels(redPath, greenPath, bluePath) {
    console.writeln("\n=== Starting RGB Channel Combination ===");

    // Open the source images
    console.writeln("Opening source images...");
    var redWindows = ImageWindow.open(redPath);
    var greenWindows = ImageWindow.open(greenPath);
    var blueWindows = ImageWindow.open(bluePath);

    if (redWindows.length == 0 || greenWindows.length == 0 || blueWindows.length == 0) {
        throw new Error("Failed to open one or more source images");
    }

    var redWindow = redWindows[0];
    var greenWindow = greenWindows[0];
    var blueWindow = blueWindows[0];

    console.writeln("Red: " + redWindow.mainView.id);
    console.writeln("Green: " + greenWindow.mainView.id);
    console.writeln("Blue: " + blueWindow.mainView.id);

    // Store the number of windows before ChannelCombination
    var windowsBefore = ImageWindow.windows.length;

    // Create ChannelCombination instance
    var CC = new ChannelCombination;

    // Configure ChannelCombination for RGB
    CC.colorSpace = ChannelCombination.prototype.RGB;
    CC.channels = [
        [true, redWindow.mainView.id],      // Red channel
        [true, greenWindow.mainView.id],    // Green channel
        [true, blueWindow.mainView.id]      // Blue channel
    ];

    // Execute ChannelCombination in global context to create new image
    console.writeln("Executing ChannelCombination...");
    CC.executeGlobal();

    // Find the newly created RGB image by looking for new windows
    var rgbWindow = null;
    var windowsAfter = ImageWindow.windows;

    // Look for the newest color window
    for (var i = 0; i < windowsAfter.length; i++) {
        if (windowsAfter[i].mainView.image.isColor) {
            rgbWindow = windowsAfter[i];
            console.writeln("Found RGB window: " + rgbWindow.mainView.id);
            break;
        }
    }

    if (rgbWindow == null) {
        throw new Error("Could not find the RGB combined image");
    }

    // Generate output filename with timestamp
    var timestamp = new Date().toISOString().replace(/[:.]/g, "-").slice(0, 19);
    var outputFilename = "RGB_Combined_" + timestamp + ".xisf";
    var outputPath = OUTPUT_DIRECTORY + "/" + outputFilename;

    // Copy some FITS keywords from the red source image (avoiding FILTER conflicts)
    try {
        var sourceKeywords = redWindow.keywords;
        var newKeywords = [];

        // Copy relevant keywords, excluding problematic ones
        for (var i = 0; i < sourceKeywords.length; i++) {
            var keyword = sourceKeywords[i];
            if (keyword.name != "FILTER" &&
                keyword.name != "IMAGETYP" &&
                keyword.name != "INSTRUME" &&
                keyword.name.indexOf("FILTER") < 0) {  // Exclude any filter-related keywords
                newKeywords.push(new FITSKeyword(keyword.name, keyword.value, keyword.comment));
            }
        }

        // Add new keywords for the RGB combination
        newKeywords.push(new FITSKeyword("IMAGETYP", "'RGB Combined'", "Type of image"));
        newKeywords.push(new FITSKeyword("FILTER", "'RGB'", "Combined RGB filters"));
        newKeywords.push(new FITSKeyword("COMMENT", "", "RGB combination created by PixInsight script"));
        newKeywords.push(new FITSKeyword("RGBRED", "'" + File.extractName(redPath) + "'", "Red channel source"));
        newKeywords.push(new FITSKeyword("RGBGREEN", "'" + File.extractName(greenPath) + "'", "Green channel source"));
        newKeywords.push(new FITSKeyword("RGBBLUE", "'" + File.extractName(bluePath) + "'", "Blue channel source"));
        newKeywords.push(new FITSKeyword("COMBDATE", "'" + new Date().toISOString() + "'", "RGB combination date"));

        rgbWindow.keywords = newKeywords;
        console.writeln("FITS keywords updated successfully");
    } catch (e) {
        console.warningln("Could not copy FITS keywords: " + e.message);
    }

    // Rename the window for clarity
    try {
        var newId = "RGB_Combined";
        // Make sure the ID is unique
        var counter = 1;
        var originalNewId = newId;
        while (ImageWindow.windowById(newId) != null) {
            newId = originalNewId + "_" + counter;
            counter++;
        }
        rgbWindow.mainView.id = newId;
        console.writeln("RGB window renamed to: " + newId);
    } catch (e) {
        console.warningln("Could not rename RGB window: " + e.message);
    }

    // Save the result
    console.writeln("Saving RGB image to: " + outputPath);
    try {
        rgbWindow.saveAs(outputPath, false, false, false, false);
        console.writeln("RGB combination saved successfully!");
    } catch (e) {
        console.warningln("Could not save RGB image: " + e.message);
        // Try saving without FITS keywords
        try {
            rgbWindow.keywords = [];
            rgbWindow.saveAs(outputPath, false, false, false, false);
            console.writeln("RGB combination saved successfully (without FITS keywords)!");
        } catch (e2) {
            throw new Error("Failed to save RGB image: " + e2.message);
        }
    }

    // Close source windows
    console.writeln("Closing source windows...");
    redWindow.close();
    greenWindow.close();
    blueWindow.close();

    console.writeln("RGB combination completed successfully!");
    console.writeln("Output: " + outputPath);

    return rgbWindow;
}

function main() {
    console.writeln("=== RGB Channel Combination Script ===");
    console.writeln("Input directory: " + INPUT_DIRECTORY);
    console.writeln("Output directory: " + OUTPUT_DIRECTORY);

    try {
        // Find master frames
        console.writeln("\nSearching for master frames...");
        var files = findMasterFrames();

        // Check if all files were found
        if (files.red == "" || files.green == "" || files.blue == "") {
            var missing = [];
            if (files.red == "") missing.push("Red");
            if (files.green == "") missing.push("Green");
            if (files.blue == "") missing.push("Blue");

            throw new Error("Missing master frames for: " + missing.join(", ") +
                          "\nFound:\nRed: " + files.red +
                          "\nGreen: " + files.green +
                          "\nBlue: " + files.blue);
        }

        console.writeln("\nAll RGB master frames found:");
        console.writeln("Red: " + File.extractName(files.red));
        console.writeln("Green: " + File.extractName(files.green));
        console.writeln("Blue: " + File.extractName(files.blue));

        // Perform RGB combination
        var rgbWindow = combineRGBChannels(files.red, files.green, files.blue);

        console.writeln("\n=== Script completed successfully! ===");
        console.writeln("RGB image window: " + rgbWindow.mainView.id);

    } catch (error) {
        console.criticalln("Error: " + error.message);
        throw error;
    }
}

// Execute the script
main();
