// =================================================================
// CombineRGBFromFilename.js
// Loads Red/Green/Blue XISF images based on filename filter indicators
// Then combines them into a color image using ChannelCombination
// Designed to work with WeightedBatchPreprocessing output filenames
// =================================================================

#include <pjsr/DataType.jsh>

function getChannelFromFilename(filePath) {
    var filename = File.extractName(filePath); // e.g. 'masterLight_FILTER-Red_mono'
    var lc = filename.toLowerCase();

    if (lc.indexOf("filter-red") !== -1 || lc.indexOf("filter-r") !== -1)
        return "R";
    if (lc.indexOf("filter-green") !== -1 || lc.indexOf("filter-g") !== -1)
        return "G";
    if (lc.indexOf("filter-blue") !== -1 || lc.indexOf("filter-b") !== -1)
        return "B";

    return "";
}

function loadImageByChannel(folder, targetChannel) {
    var files = new Array;
    var ff = new FileFind;

    if (!ff.begin(folder + "/*.xisf")) {
        console.criticalln("No .xisf files found in: " + folder);
        return null;
    }

    do {
        var filePath = folder + "/" + ff.name;
        files.push(filePath);
    } while (ff.next());
    ff.end();

    for (var i = 0; i < files.length; ++i) {
        var filePath = files[i];
        var channel = getChannelFromFilename(filePath);
        console.writeln("File: " + filePath + " => Detected channel: '" + channel + "'");

        if (channel === targetChannel) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0) {
                console.writeln("Loaded '" + channel + "' channel image: " + filePath);
                return windowArray[0];
            } else {
                console.warningln("Failed to load window from file: " + filePath);
            }
        }
    }

    console.criticalln("No matching image found for channel: " + targetChannel);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("Scanning directory for RGB XISF files: " + inputDir);

    console.writeln("Loading Red channel...");
    var redWindow = loadImageByChannel(inputDir, "R");
    if (!redWindow) { console.criticalln("Red channel image not loaded."); return; }

    console.writeln("Loading Green channel...");
    var greenWindow = loadImageByChannel(inputDir, "G");
    if (!greenWindow) { console.criticalln("Green channel image not loaded."); return; }

    console.writeln("Loading Blue channel...");
    var blueWindow = loadImageByChannel(inputDir, "B");
    if (!blueWindow) { console.criticalln("Blue channel image not loaded."); return; }

    // Access views
    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    // Set up ChannelCombination
    var combo = new ChannelCombination;
    combo.channels = [
        [redView],    // Red
        [greenView],  // Green
        [blueView]    // Blue
    ];

    // Create RGB output image
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,              // RGB = 3 channels
        BitDepth_IEEE32, // High dynamic range
        true,           // Color
        false,          // Not temporary
        "Combined_RGB"  // Window ID
    );

    // Execute combination
    combo.executeOn(outputImage.mainView);
    outputImage.show();

    console.writeln("✅ RGB image combined successfully!");
}

main();
