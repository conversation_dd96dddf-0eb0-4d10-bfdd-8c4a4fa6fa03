// =================================================================
// PixInsight Script - Combine RGB channels from monochrome XISF files
// Channel determined by filename substring
// Outputs color RGB image in a new window
// =================================================================

#include <pjsr/DataType.jsh>

function getChannelFromFilename(filePath) {
    var filename = File.extractName(filePath).toLowerCase();

    if (filename.indexOf("filter-red") !== -1 || filename.indexOf("filter-r") !== -1)
        return "R";
    if (filename.indexOf("filter-green") !== -1 || filename.indexOf("filter-g") !== -1)
        return "G";
    if (filename.indexOf("filter-blue") !== -1 || filename.indexOf("filter-b") !== -1)
        return "B";

    return "";
}

function loadImageByChannel(folder, targetChannel) {
    var ff = new FileFind;
    if (!ff.begin(folder + "/*.xisf")) {
        console.criticalln("❌ No .xisf files found in: " + folder);
        return null;
    }

    do {
        var filePath = folder + "/" + ff.name;
        var channel = getChannelFromFilename(filePath);
        console.writeln("Inspecting file: " + filePath + " | Detected channel: '" + channel + "'");

        if (channel === targetChannel) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0) {
                console.writeln("✅ Loaded '" + channel + "' channel image.");
                return windowArray[0];
            } else {
                console.warningln("⚠️ Failed to load image from: " + filePath);
            }
        }
    } while (ff.next());
    ff.end();

    console.criticalln("🚫 No matching image found for channel: " + targetChannel);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("🔍 Loading R, G, and B channel images...");

    var redWindow = loadImageByChannel(inputDir, "R");
    if (!redWindow) return;

    var greenWindow = loadImageByChannel(inputDir, "G");
    if (!greenWindow) return;

    var blueWindow = loadImageByChannel(inputDir, "B");
    if (!blueWindow) return;

    // Get image views
    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    // Configure ChannelCombination
    var combo = new ChannelCombination;
    combo.channels = [
        [redView.id],
        [greenView.id],
        [blueView.id]
    ];

    // Output image dimensions same as inputs
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,                // Channels for RGB
        BitDepth_IEEE32,  // 32-bit float
        true,             // isColor = true
        false,            // canDelete
        "RGB_Combined"    // Window ID
    );

    // Execute the combination
    combo.executeOn(outputImage.mainView, false);

    // Show the result
    outputImage.show();

    console.writeln("✅ Successfully combined RGB channels into a color image.");
}

main();
