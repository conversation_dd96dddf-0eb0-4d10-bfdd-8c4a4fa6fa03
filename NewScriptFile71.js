// =================================================================
// PixInsight Script - Combine RGB channels from monochrome XISF files
// Channel assignment based on filename substring
// Output is an RGB color image shown in main view
// =================================================================

#include <pjsr/DataType.jsh>

function getChannelFromFilename(filePath) {
    var filename = File.extractName(filePath).toLowerCase();

    // Match substrings like filter-r, filter-red, filter-b, etc.
    if (filename.includes("filter-red") || filename.includes("filter-r")) return "R";
    if (filename.includes("filter-green") || filename.includes("filter-g")) return "G";
    if (filename.includes("filter-blue") || filename.includes("filter-b")) return "B";

    return "";
}

function loadImageByChannel(folder, targetChannel) {
    var ff = new FileFind;
    if (!ff.begin(folder + "/*.xisf")) {
        console.criticalln("❌ No .xisf files found in: " + folder);
        return null;
    }

    do {
        var filePath = folder + "/" + ff.name;
        var channel = getChannelFromFilename(filePath);
        console.writeln("Inspecting file: " + filePath + " | Detected channel: '" + channel + "'");

        if (channel === targetChannel) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0) {
                console.writeln("✅ Loaded '" + channel + "' channel image.");
                return windowArray[0];
            } else {
                console.warningln("⚠️ Failed to load image from: " + filePath);
            }
        }
    } while (ff.next());
    ff.end();

    console.criticalln("🚫 No matching image found for channel: " + targetChannel);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    // Load each mono image into separate windows/views
    var redWindow = loadImageByChannel(inputDir, "R");
    if (!redWindow) return;

    var greenWindow = loadImageByChannel(inputDir, "G");
    if (!greenWindow) return;

    var blueWindow = loadImageByChannel(inputDir, "B");
    if (!blueWindow) return;

    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    // Channel combination
    var combo = new ChannelCombination;

    // Pass the view references directly as arrays
    combo.channels = [
        [redView.id],     // Channel 0 (Red)
        [greenView.id],   // Channel 1 (Green)
        [blueView.id]     // Channel 2 (Blue)
    ];

    // Create output RGB image window
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,                 // 3 channels for RGB
        BitDepth_IEEE32,   // High dynamic range
        true,              // isColor = true
        false,             // canDelete
        "Combined_RGB"     // window ID
    );

    // Apply channel combination and write to output
    combo.executeOn(outputImage.mainView, false); // false = replace contents

    // Display result
    outputImage.show();

    console.writeln("✅ Successfully combined R, G, and B channels into one RGB image.");
}

main();
