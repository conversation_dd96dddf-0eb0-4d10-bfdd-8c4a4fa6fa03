/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * FINAL VERSION
 * - Uses a direct library call to <PERSON>ra<PERSON><PERSON> for maximum reliability.
 * - Uses custom parameters for ABE and GradientCorrection.
 * - Chains ABE -> BlackPoint -> GradientCorrection -> GraX<PERSON> -> BlackPoint.
 * - Includes robust file-based debugger.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- Including the GraXpert library directly ---
#include "$PXI_SRCDIR/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Debugger Control --------------------
var DEBUG_MODE = true;
var debugStepCounter = 1;

// -------------------- AutoSTF constants --------------------
var STF_UNLINKED        = true;
var STF_K_SHADOWS       = -3.0;
var STF_TARGET_BG       = 0.22;
var STF_HIGHLIGHTS      = 1.0;
var STF_MAX_SAMPLES     = 120000;
var outputExtension = ".xisf";

// -------------------- Defaults --------------------
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

// -------------------- Utils --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}

function debugPause(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Pause Warning: Invalid window provided for step '", stepName, "'");
        return;
    }
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let fileName = counterStr + "_" + baseTag + "_" + sanitizedStepName + ".fit";
    let filePath = debugDir + "/" + fileName;
    debugStepCounter++;
    Console.writeln("DEBUG: Saving intermediate file: ", filePath);
    win.saveAs(filePath, false, false, false, true);
    var msgBox = new MessageBox( "<h2>Debug Pause</h2><p><b>Paused after:</b> " + stepName + "</p><p>A snapshot has been saved as a FITS file in the 'debug' subfolder:</p><p><b>" + fileName + "</b></p><p>Click <b>Continue</b> to proceed, or <b>Stop</b> to abort the script.</p>", "Debug Pause", StdIcon_Information, StdButton_Ok, StdButton_Cancel );
    msgBox.okButtonText = "Continue";
    msgBox.cancelButtonText = "Stop";
    if (msgBox.execute() == StdButton_Cancel) {
        throw new Error("Script execution aborted by user after '" + stepName + "'.");
    }
}

function startConsoleLog() {
    let path = File.systemTempDirectory + "/pixinsight_console_log_" + Date.now() + ".txt";
    let P = new ProcessInstance("Console");
    P.arg = "-r=" + path;
    P.executeGlobal();
    return path;
}
function stopConsoleLog() {
    let P = new ProcessInstance("Console");
    P.arg = "-r=";
    P.executeGlobal();
}
function checkLogForFailure(filePath, searchText) {
    if (!filePath || !File.exists(filePath)) return false;
    var failureFound = false;
    try {
      var logFile = new File;
      logFile.openForReading(filePath);
      if (logFile.isOpen) {
        var s = logFile.read(DataType_ByteArray);
        logFile.close();
        var logContent = s.toString();
        if (logContent.indexOf(searchText) !== -1) failureFound = true;
      }
    } catch(e) {
      Console.writeln("Warning: Could not read console log file: " + e.message);
    } finally {
      try { File.remove(filePath); } catch(_) {}
    }
    return failureFound;
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents();
    }
}

// -------------------- Process Functions --------------------
function saveAs16BitTiff(win, path) {
  var tifPath = File.changeExtension(path, ".tif");
  win.saveAs(tifPath, false, false, true, false);
  Console.writeln("  Saved 16-bit TIFF: ", File.extractName(tifPath));
}

function findAllInputImages(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[];
  var ff=new FileFind;
  var supportedExtensions = [".xisf", ".fit", ".fits", ".tif", ".tiff"];
  if(ff.begin(dir+"/*.*")){
    do {
      var nameLower = ff.name.toLowerCase();
      for (var i = 0; i < supportedExtensions.length; ++i) {
        if (nameLower.endsWith(supportedExtensions[i])) {
          v.push(dir+"/"+ff.name);
          break;
        }
      }
    } while(ff.next());
  }
  return v;
}

function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /filter-r(?![a-z])/.test(s) || /\bred\b/.test(s)) return "R";
  if (s.indexOf("filter-green")>=0 || /filter-g(?![a-z])/.test(s) || /\bgreen\b/.test(s)) return "G";
  if (s.indexOf("filter-blue")>=0 || /filter-b(?![a-z])/.test(s) || /\bblue\b/.test(s)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}

function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }
  var haveR=!!(byFilter.R&&byFilter.R.length), haveG=!!(byFilter.G&&byFilter.G.length), haveB=!!(byFilter.B&&byFilter.B.length);
  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };
  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }
  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k, isStackedRGB: false });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) {
    var filePath = unknownSingles[k];
    var isColorStack = false;
    var tempWinArr = ImageWindow.open(filePath);
    if (tempWinArr.length > 0) {
        var tempWin = tempWinArr[0];
        if (tempWin.mainView.image.isColor) isColorStack = true;
        tempWin.forceClose();
    }
    plan.singles.push({ path: filePath, tag: isColorStack ? "Color" : "Single", isStackedRGB: isColorStack });
  }
  return plan;
}

function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}

function finalABE(win, sum){
  try{
    Console.writeln("\n=== Running Automatic Background Extractor (ABE) ===");
    var P = new AutomaticBackgroundExtractor;
    P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050; P.maxBackground = 1.0000; P.minBackground = 0.0000; P.useBrightnessLimits = false; P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5; P.modelImageSampleFormat = AutomaticBackgroundExtractor.prototype.f32; P.abeDownsample = 2.00; P.writeSampleBoxes = false; P.justTrySamples = false;
    P.targetCorrection = AutomaticBackgroundExtractor.prototype.Subtraction; P.normalize = true; P.replaceTarget = true; P.discardModel = true;
    P.correctedImageId = ""; P.correctedImageSampleFormat = AutomaticBackgroundExtractor.prototype.SameAsTarget; P.verboseCoefficients = false; P.compareModel = false; P.compareFactor = 10.00;
    P.executeOn(win.mainView);
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE with custom settings applied"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}

function runGradientCorrection(win, sum) {
    try {
        Console.writeln("\n=== Running GradientCorrection Tool ===");
        var P = new GradientCorrection;
        P.reference = 0.50; P.lowThreshold = 0.20; P.lowTolerance = 0.50; P.highThreshold = 0.05; P.highTolerance = 0.00; P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.protectionThreshold = 0.10; P.protectionAmount = 0.50; P.protectionSmoothingFactor = 16; P.lowClippingLevel = 0.000076; P.automaticConvergence = true; P.convergenceLimit = 0.00001000; P.maxIterations = 10; P.useSimplification = true; P.simplificationDegree = 1; P.simplificationScale = 1024; P.generateSimpleModel = false; P.generateGradientModel = false; P.generateProtectionMasks = false; P.gridSamplingDelta = 16;
        P.executeOn(win.mainView);
        sum.gradientCorrection = {name: "GradientCorrection", status: "✅", details: "Applied with custom settings"};
    } catch (e) {
        sum.gradientCorrection = {name: "GradientCorrection", status: "❌", details: e.message};
    }
}

function runGraXpert(win, sum) {
    try {
        Console.writeln("\n=== Running GraXpert via Library Call ===");
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0;
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = win.mainView;
        gxp.process();
        sum.graxpert = {name: "GraXpert", status: "✅", details: "Applied via library call"};
    } catch (e) {
        sum.graxpert = {name: "GraXpert", status: "❌", details: e.message};
    }
}

function autoBlackPoint(win, sum){try{var img = win.mainView.image;if (!img.readSamples) {var AH_fallback = new AutoHistogram(); AH_fallback.auto=true; AH_fallback.clipLow=0.1; AH_fallback.clipHigh=0.1; AH_fallback.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};return true;}if(!img.isColor || img.numberOfChannels<3){var AH=new AutoHistogram(); AH.auto=true; AH_fallback.clipLow=0.1; AH_fallback.clipHigh=0.1; AH_fallback.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"AutoHistogram (mono/NB)"}; return true;}var w=img.width,h=img.height, sampleSize=20, numSamples=20;var rs=[],gs=[],bs=[];for(var i=0;i<numSamples;++i){var x=Math.floor(Math.random()*(w-sampleSize)), y=Math.floor(Math.random()*(h-sampleSize));var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);}rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0; for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; } R/=n; G/=n; B/=n;try{var L=new Levels(); L.redBlack=Math.min(R*0.8,0.02); L.greenBlack=Math.min(G*0.9,0.03); L.blueBlack=Math.min(B*0.8,0.02);L.redWhite=0.98; L.greenWhite=0.97; L.blueWhite=0.98; L.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"Levels"};}catch(e2){var AH2=new AutoHistogram(); AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1; AH2.executeOn(win.mainView);sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};}return true;}catch(e){ sum.blackPoint={name:"Black Point",status:"❌",details:e.message}; return false; }}
function solveImage(win, sum){try{Console.writeln("\n=== ImageSolver (for SPCC) ===");var solver=new ImageSolver;try{solver.silent=true;}catch(_){}try{solver.showInterface=false;}catch(_){}try{solver.forceDialog=false;}catch(_){}try{solver.noGUIMode=true;}catch(_){}try{solver.noGUIMessages=true;}catch(_){}solver.Init(win,false);solver.useMetadata=true;solver.catalog="GAIA DR3";solver.useDistortionCorrection=false;solver.generateErrorMaps=false;solver.showStars=false;solver.showDistortion=false;solver.generateDistortionMaps=false;solver.sensitivity=0.1;if(!solver.SolveImage(win)){Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");solver.useMetadata=false;if(!solver.SolveImage(win))throw new Error("Plate solution not found.");}sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};return true;}catch(e){sum.solver={name:"ImageSolver",status:"❌",details:e.message};return false;}}
function performSPCC(win, sum, profileSelector, showGraphs){let logPath=null;try{Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");var P=new SpectrophotometricColorCalibration;try{P.generateGraphs=showGraphs;}catch(_){}try{P.generateStarMaps=false;}catch(_){}try{P.generateTextFiles=false;}catch(_){}try{P.whiteReferenceId="Average Spiral Galaxy";}catch(_){try{P.whiteReferenceId="AVG_G2V";}catch(_){}}try{P.qeCurve="Sony IMX411/455/461/533/571";}catch(_){}try{if(profileSelector==="RGB"){P.redFilter="Astronomik Typ 2c R";P.greenFilter="Astronomik Typ 2c G";P.blueFilter="Astronomik Typ 2c B";}else{P.redFilter="Sony Color Sensor R";P.greenFilter="Sony Color Sensor G";P.blueFilter="Sony Color Sensor B";}}catch(_){}try{P.narrowbandMode=false;}catch(_){}try{P.applyCalibration=true;}catch(_){}try{P.catalog="Gaia DR3/SP";}catch(_){}try{P.automaticLimitMagnitude=true;}catch(_){}try{P.backgroundNeutralization=true;P.lowerLimit=-2.80;P.upperLimit=*****;}catch(_){}try{P.structureLayers=5;P.saturationThreshold=0.99;P.backgroundReferenceViewId="";P.limitMagnitude=16.0;}catch(_){}try{logPath=startConsoleLog();P.executeOn(win.mainView);}finally{stopConsoleLog();}if(checkLogForFailure(logPath,"<* failed *>")){throw new Error("SPCC failed internally. See console log for details like 'Insufficient data'.");}sum.spcc={name:"SPCC",status:"✅",details:"Applied (graphs disabled)"};return true;}catch(e){sum.spcc={name:"SPCC",status:"❌",details:e.message};return false;}}
function deblur1(win, sum){try{var P=new BlurXTerminator;P.sharpenStars=0.00;P.adjustStarHalos=0.00;P.psfDiameter=0.00;P.sharpenNonstellar=0.00;P.autoPSF=true;P.correctOnly=true;P.correctFirst=false;P.nonstellarThenStellar=false;P.luminanceOnly=false;P.executeOn(win.mainView);sum.deblurV1={name:"Deblur V1",status:"✅",details:"Round stars"};return true;}catch(e){sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message};return false;}}
function deblur2(win, sum){try{var P=new BlurXTerminator;P.sharpenStars=0.25;P.adjustStarHalos=0.00;P.psfDiameter=0.00;P.sharpenNonstellar=0.90;P.autoPSF=true;P.correctOnly=false;P.correctFirst=false;P.nonstellarThenStellar=false;P.luminanceOnly=false;P.executeOn(win.mainView);sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};return true;}catch(e){sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message};return false;}}
function denoise(win, sum){try{var P=new NoiseXTerminator;P.intensityColorSeparation=false;P.frequencySeparation=false;P.denoise=0.90;P.iterations=2;P.executeOn(win.mainView);sum.denoising={name:"Denoising",status:"✅",details:"Applied"};return true;}catch(e){sum.denoising={name:"Denoising",status:"❌",details:e.message};return false;}}
function clamp(x,a,b){ return x<a?a:(x>b?b:x); }
function median(v){ var n=v.length; if(!n) return 0; v.sort(function(a,b){return a-b;}); return (n&1)? v[(n-1)>>1] : 0.5*(v[n>>1]+v[(n>>1)-1]); }
function mad(v, med){ var a=new Array(v.length); for (var i=0;i<v.length;++i) a[i]=Math.abs(v[i]-med); return median(a); }
function sampleChannel(img, ch){var W=img.width, H=img.height, tot=W*H;var step=Math.max(1,Math.floor(Math.sqrt(tot/STF_MAX_SAMPLES)));var vals=[];if(img.isColor){for(var y=0;y<H;y+=step)for(var x=0;x<W;x+=step)vals.push(img.sample(x,y,ch));}else{for(var y=0;y<H;y+=step)for(var x=0;x<W;x+=step)vals.push(img.sample(x,y));}return vals;}
function sampleLuminance(img){var W=img.width,H=img.height,tot=W*H;var step=Math.max(1,Math.floor(Math.sqrt(tot/STF_MAX_SAMPLES)));var vals=[];for(var y=0;y<H;y+=step){for(var x=0;x<W;x+=step){if(img.isColor){var r=img.sample(x,y,0),g=img.sample(x,y,1),b=img.sample(x,y,2);vals.push(0.2126*r+0.7152*g+0.0722*b);}else vals.push(img.sample(x,y));}}return vals;}
function mtfMidtonesFromTarget(normBg,target){normBg=clamp(normBg,1e-6,0.999999);target=clamp(target,1e-6,0.999999);return clamp(Math.exp(Math.log(0.5)*Math.log(normBg)/Math.log(target)),0.001,0.999);}
function computeSTF(img){var s=[0,0,0],m=[0.5,0.5,0.5],h=[1,1,1];if(img.isColor&&STF_UNLINKED){for(var c=0;c<3;++c){var v=sampleChannel(img,c);var med=median(v);var sig=1.4826*mad(v,med);var sc=clamp(med+STF_K_SHADOWS*sig,0,0.99);var hc=STF_HIGHLIGHTS;var B=clamp((med-sc)/(hc-sc),1e-6,0.999999);var mc=mtfMidtonesFromTarget(B,STF_TARGET_BG);s[c]=sc;m[c]=mc;h[c]=hc;}}else{var v=sampleLuminance(img);var med=median(v);var sig=1.4826*mad(v,med);var sc=clamp(med+STF_K_SHADOWS*sig,0,0.99);var hc=STF_HIGHLIGHTS;var B=clamp((med-sc)/(hc-sc),1e-6,0.999999);var mc=mtfMidtonesFromTarget(B,STF_TARGET_BG);s=[sc,sc,sc];m=[mc,mc,mc];h=[hc,hc,hc];}return {s:s,m:m,h:h};}
function applyHT(view,stf){var P=new HistogramTransformation;P.H=[[stf.s[0],stf.m[0],stf.h[0],0,1],[stf.s[1],stf.m[1],stf.h[1],0,1],[stf.s[2],stf.m[2],stf.h[2],0,1],[stf.s[0],stf.m[0],stf.h[0],0,1]];P.executeOn(view);}
function stretchImageAtStage(inPath,outPath,saveFlag){var w=ImageWindow.open(inPath);if(!w.length)return null;var win=w[0],img=win.mainView.image;var stf=computeSTF(img);applyHT(win.mainView,stf);if(saveFlag)saveAs16BitTiff(win,outPath);return win;}
function applyColorEnhanceToView(view){var C=new CurvesTransformation;C.R=[[0,0],[1,1]];C.G=[[0,0],[1,1]];C.B=[[0,0],[1,1]];C.K=[[0,0],[1,1]];C.A=[[0,0],[1,1]];C.L=[[0,0],[1,1]];C.a=[[0,0],[1,1]];C.b=[[0,0],[1,1]];C.c=[[0,0],[1,1]];C.H=[[0,0],[1,1]];C.S=[[0,0],[0.1447,0.33247],[1,1]];C.Rt=C.AkimaSubsplines;C.Gt=C.AkimaSubsplines;C.Bt=C.AkimaSubsplines;C.Kt=C.AkimaSubsplines;C.At=C.AkimaSubsplines;C.Lt=C.AkimaSubsplines;C.at=C.AkimaSubsplines;C.bt=C.AkimaSubsplines;C.ct=C.AkimaSubsplines;C.Ht=C.AkimaSubsplines;C.St=C.AkimaSubsplines;C.executeOn(view);var N=new SCNR;N.amount=0.73;N.protectionMethod=SCNR.prototype.AverageNeutral;N.colorToRemove=SCNR.prototype.Green;N.preserveLightness=true;N.executeOn(view);}
function pixelMathSubtract(targetView,aId,bId,newId){var pm=new PixelMath;pm.expression=aId+" - "+bId;pm.useSingleExpression=true;pm.rescale=true;pm.rescaleLower=0;pm.rescaleUpper=1;pm.truncate=false;pm.createNewImage=true;pm.newImageId=newId;pm.newImageWidth=0;pm.newImageHeight=0;pm.newImageSampleFormat=0;pm.executeOn(targetView,false);var outWin=ImageWindow.windowById(newId);if(!outWin)throw new Error("PixelMath subtraction failed: "+newId);var outView=outWin.mainView;outView.beginProcess(UndoFlag_NoSwapFile);outView.image.truncate(0,1);outView.endProcess();return outWin;}
function subtractFilesToPath_StretchedMask(starsPath,starlessPath,outPath){var wA=ImageWindow.open(starsPath);if(!wA.length)throw new Error("Open failed: "+starsPath);var wB=ImageWindow.open(starlessPath);if(!wB.length)throw new Error("Open failed: "+starlessPath);var A=wA[0],B=wB[0];var aimg=A.mainView.image,bimg=B.mainView.image;if(aimg.width!==bimg.width||aimg.height!==bimg.height||aimg.numberOfChannels!==bimg.numberOfChannels||aimg.sampleType!==bimg.sampleType||aimg.bitsPerSample!==bimg.bitsPerSample)throw new Error("Geometry/sample mismatch between stars and starless.");var commonSTF=computeSTF(aimg);applyHT(A.mainView,commonSTF);applyHT(B.mainView,commonSTF);A.mainView.id="StarsObj_A";B.mainView.id="Starless_B";var out=pixelMathSubtract(A.mainView,"StarsObj_A","Starless_B","StarsMinusStarless");saveAs16BitTiff(out,outPath);try{out.forceClose();}catch(_){}try{A.forceClose();}catch(_){}try{B.forceClose();}catch(_){}}
function starSeparationAndStretchedMask(win,sum,finalDir,baseTag,opts,isRGBCombined,enhanceRGBStarsStretched){var needStarlessAny=opts.starless_linear||opts.starless_stretched;var needStarsStretched=opts.stars_stretched;if(!(needStarlessAny||needStarsStretched)){sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};return true;}try{var P=new StarXTerminator;P.generateStarImage=false;P.unscreenStars=false;P.largeOverlap=false;P.executeOn(win.mainView);var pathStarlessL=finalDir+"/Starless_"+baseTag+outputExtension;var pathStarlessS=finalDir+"/Starless_Stretched_"+baseTag;if(opts.starless_linear)win.saveAs(pathStarlessL,false,false,false,false);if(opts.starless_stretched){if(!File.exists(pathStarlessL)){win.saveAs(pathStarlessL,false,false,false,false);}var starlessLinearWinArr=ImageWindow.open(pathStarlessL);if(starlessLinearWinArr.length>0){var wDup=starlessLinearWinArr[0];var pathBaselineL=finalDir+"/Final_Stacked_"+baseTag+outputExtension;var baselineArr=ImageWindow.open(pathBaselineL);var stf1;if(baselineArr.length>0){var baselineW=baselineArr[0];stf1=computeSTF(baselineW.mainView.image);baselineW.forceClose();}else{throw new Error("Failed to open baseline for STF computation.");}applyHT(wDup.mainView,stf1);saveAs16BitTiff(wDup,pathStarlessS);wDup.forceClose();}else{throw new Error("Failed to reopen starless linear image for stretching.");}}if(needStarsStretched){var pathBaselineL=finalDir+"/Final_Stacked_"+baseTag+outputExtension;if(!File.exists(pathStarlessL))win.saveAs(pathStarlessL,false,false,false,false);var outStarsS=finalDir+"/Stars_Stretched_"+baseTag;subtractFilesToPath_StretchedMask(pathBaselineL,pathStarlessL,outStarsS);if(isRGBCombined&&enhanceRGBStarsStretched){var wS=ImageWindow.open(File.changeExtension(outStarsS,".tif"));if(wS.length){applyColorEnhanceToView(wS[0].mainView);saveAs16BitTiff(wS[0],outStarsS);try{wS[0].forceClose();}catch(_){}}}}sum.starSeparation={name:"Star Separation",status:"✅",details:"Starless made with SXT; star mask by stretched subtraction with common STF"};return true;}catch(e){sum.starSeparation={name:"Star Separation",status:"❌",details:e.message};return false;}}
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB = false, spccGraphs = false){
  var sum={};
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);
  var debugDir = rootOut + "/debug";
  if (DEBUG_MODE) ensureDir(debugDir);
  debugStepCounter = 1;

  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};

  closeAllWindowsExcept([win.mainView.id]);

  finalABE(win,sum);
  debugPause(win, "Background Extraction ABE", baseTag, debugDir);

  autoBlackPoint(win,sum);
  debugPause(win, "Initial Black Point", baseTag, debugDir);

  runGradientCorrection(win, sum);
  debugPause(win, "GradientCorrection Tool", baseTag, debugDir);

  runGraXpert(win, sum);
  debugPause(win, "Gradient Removal GraXpert", baseTag, debugDir);

  autoBlackPoint(win,sum);
  debugPause(win, "Final Black Point", baseTag, debugDir);

  var isColor = win.mainView.image.isColor;
  if (isColor) {
    if (isStackedRGB) {
        sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped for pre-stacked color image"};
        sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped for pre-stacked color image"};
    } else {
        if (solveImage(win, sum)) {
            var spccProfile = isRGBCombined ? "RGB" : "OSC";
            performSPCC(win, sum, spccProfile, spccGraphs);
            debugPause(win, "Color Calibration SPCC", baseTag, debugDir);
        } else {
            sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
        }
    }
  } else {
    sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped (mono/NB)"};
    sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (mono/NB)"};
  }

  closeAllWindowsExcept([win.mainView.id]);
  var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};

  if(ai.deblur1){
    let w1 = ImageWindow.open(baseline)[0];
    if (w1) {
        if (deblur1(w1, sum)) {
            debugPause(w1, "Deblur V1 Round Stars", baseTag, debugDir);
            if(saveCfg.deblur1) w1.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
        }
        try{ w1.forceClose(); }catch(_){}
    }
  }

  if(ai.deblur2){
    let w2 = ImageWindow.open(baseline)[0];
    if (w2) {
        if (deblur2(w2, sum)) {
            debugPause(w2, "Deblur V2 Enhance", baseTag, debugDir);
            if(saveCfg.deblur2) w2.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
        }
        try{ w2.forceClose(); }catch(_){}
    }
  }

  var stretchedPath = finalDir+"/Stretched_"+baseTag;
  if(ai.stretch){
    let wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/saveCfg.final_stretched);
    if(wS) {
        debugPause(wS, "Stretch AutoSTF-HT", baseTag, debugDir);
        try{ wS.forceClose(); }catch(_){}
    }
  }

  if(ai.denoise){
    var dnIn = ai.stretch ? File.changeExtension(stretchedPath,".tif") : baseline;
    if(!File.exists(dnIn) && ai.stretch) {
        var temp_wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/true);
        if(temp_wS) try{ temp_wS.forceClose(); }catch(_){}
    }
    var w3 = ImageWindow.open(dnIn)[0];
    if(w3){
        if(denoise(w3,sum) && saveCfg.denoised) {
             var denoiseOutPath = finalDir+"/Denoised_"+baseTag;
             if (ai.stretch) { saveAs16BitTiff(w3, denoiseOutPath); }
             else { w3.saveAs(denoiseOutPath + outputExtension, false, false, false, false); }
        }
        try{ w3.forceClose(); }catch(_){}
    }
  }

  if(ai.starless){
    var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
    if(needAny){
      var w4=ImageWindow.open(baseline)[0];
      if(w4) starSeparationAndStretchedMask(w4,sum,finalDir,baseTag,
        { stars_stretched:saveCfg.stars_stretched, starless_linear:saveCfg.starless_linear, starless_stretched:saveCfg.starless_stretched },
        isRGBCombined, enhanceRGBStarsStretched
      );
      try{ w4.forceClose(); }catch(_){}
    }else{
      sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
    }
  }

  if(!saveCfg.baseline_linear) removeIf(baseline, true);
  if(!saveCfg.integration_linear) removeIf(integrationPath, true);
  if(!saveCfg.final_stretched && !saveCfg.denoised) {
      removeIf(File.changeExtension(stretchedPath, ".tif"), true);
  }

  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "denoising", "starSeparation", "finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}
function showGUI(state){var dlg=new Dialog;dlg.windowTitle="Post-Integration Pipeline (RGB & Mono) - CORRECTED STRETCH";dlg.sizer=new VerticalSizer;dlg.sizer.margin=10;dlg.sizer.spacing=8;var head=new Label(dlg);head.useRichText=true;head.text="<b>Utah Masterclass — Post-Integration Pipeline (CORRECTED STRETCH)</b>";head.textAlignment=TextAlign_Center;dlg.sizer.add(head);var gTop=new GroupBox(dlg);gTop.title="General";gTop.sizer=new VerticalSizer;gTop.sizer.margin=8;gTop.sizer.spacing=6;var rowIn=new HorizontalSizer;rowIn.spacing=6;var labelIn=new Label(dlg);labelIn.text="Input Directory:";labelIn.textAlignment=TextAlign_Right|TextAlign_VertCenter;var editIn=new Edit(dlg);editIn.readOnly=true;editIn.minWidth=560;editIn.text=state.inputDir;var btnIn=new PushButton(dlg);btnIn.text="Browse...";btnIn.icon=dlg.scaledResource(":/icons/select-file.png");btnIn.onClick=function(){var d=new GetDirectoryDialog;d.caption="Select Input Directory";d.initialDirectory=state.inputDir||"";if(d.execute()){state.inputDir=fwd(d.directory).replace(/\/$/,"");editIn.text=state.inputDir;}};rowIn.add(labelIn);rowIn.add(editIn,100);rowIn.add(btnIn);gTop.sizer.add(rowIn);var rowOut=new HorizontalSizer;rowOut.spacing=6;var labelOut=new Label(dlg);labelOut.text="Output Directory:";labelOut.textAlignment=TextAlign_Right|TextAlign_VertCenter;var editOut=new Edit(dlg);editOut.readOnly=true;editOut.minWidth=560;editOut.text=state.outputDir;var btnOut=new PushButton(dlg);btnOut.text="Browse...";btnOut.icon=dlg.scaledResource(":/icons/select-file.png");btnOut.onClick=function(){var d=new GetDirectoryDialog;d.caption="Select Output Base Directory";d.initialDirectory=state.outputDir||"";if(d.execute()){state.outputDir=fwd(d.directory).replace(/\/$/,"");editOut.text=state.outputDir;}};rowOut.add(labelOut);rowOut.add(editOut,100);rowOut.add(btnOut);gTop.sizer.add(rowOut);dlg.sizer.add(gTop);var gAI=new GroupBox(dlg);gAI.title="AI / Processing Steps";gAI.sizer=new VerticalSizer;gAI.sizer.margin=8;gAI.sizer.spacing=6;var rowAI1=new HorizontalSizer;rowAI1.spacing=12;var cbD1=new CheckBox(dlg);cbD1.text="✨ Deblur V1 (round stars)";cbD1.checked=state.ai.deblur1;var cbD2=new CheckBox(dlg);cbD2.text="✨ Deblur V2 (enhance)";cbD2.checked=state.ai.deblur2;var cbST=new CheckBox(dlg);cbST.text="🌀 Stretch (AutoSTF→HT)";cbST.checked=state.ai.stretch;var cbDN=new CheckBox(dlg);cbDN.text="🧼 Denoise";cbDN.checked=state.ai.denoise;var cbSL=new CheckBox(dlg);cbSL.text="✂️ Enable StarX (for starless/stars)";cbSL.checked=state.ai.starless;rowAI1.add(cbD1);rowAI1.add(cbD2);rowAI1.add(cbST);rowAI1.add(cbDN);rowAI1.add(cbSL);rowAI1.addStretch();gAI.sizer.add(rowAI1);var rowAI2=new HorizontalSizer;rowAI2.spacing=12;var cbSPCCGraph=new CheckBox(dlg);cbSPCCGraph.text="📊 Show SPCC Graphs (Advanced)";cbSPCCGraph.checked=state.spccGraphs;rowAI2.add(cbSPCCGraph);rowAI2.addStretch();gAI.sizer.add(rowAI2);dlg.sizer.add(gAI);var gRGB=new GroupBox(dlg);gRGB.title="RGB Outputs";gRGB.sizer=new VerticalSizer;gRGB.sizer.margin=8;gRGB.sizer.spacing=6;var rowRGBMaster=new HorizontalSizer;rowRGBMaster.spacing=10;var chkProcRGB=new CheckBox(gRGB);chkProcRGB.text="✅ Process RGB / Color";chkProcRGB.checked=state.processRGB;chkProcRGB.toolTip="Enable or disable all RGB/Color processing and outputs.";rowRGBMaster.add(chkProcRGB);rowRGBMaster.addStretch();gRGB.sizer.add(rowRGBMaster);var cbCombine=new CheckBox(dlg);cbCombine.text="Auto-detect & combine R+G+B masters";cbCombine.checked=state.combineRGB;gRGB.sizer.add(cbCombine);var rowR1=new HorizontalSizer;rowR1.spacing=10;var lR1=new Label(dlg);lR1.text="🏆 Finals:";lR1.minWidth=120;var rFinalS=new CheckBox(dlg);rFinalS.text="Final (stretched, with stars) (TIFF)";rFinalS.checked=state.save.rgb.final_stretched;var rFinalL=new CheckBox(dlg);rFinalL.text="Final (linear, with stars)";rFinalL.checked=state.save.rgb.final_linear;rowR1.add(lR1);rowR1.add(rFinalS);rowR1.add(rFinalL);rowR1.addStretch();gRGB.sizer.add(rowR1);var rowR2=new HorizontalSizer;rowR2.spacing=10;var lR2=new Label(dlg);lR2.text="🎭 Masks & Starless:";lR2.minWidth=120;var rStarsS=new CheckBox(dlg);rStarsS.text="Stars (stretched mask) (TIFF)";rStarsS.checked=state.save.rgb.stars_stretched;var rgbEnh=new CheckBox(dlg);rgbEnh.text="✨ Enhance with rich star colors";rgbEnh.checked=state.colorEnhanceRGBStarsStretched;var rSLessS=new CheckBox(dlg);rSLessS.text="Starless (stretched) (TIFF)";rSLessS.checked=state.save.rgb.starless_stretched;var rSLessL=new CheckBox(dlg);rSLessL.text="Starless (linear)";rSLessL.checked=state.save.rgb.starless_linear;rowR2.add(lR2);rowR2.add(rStarsS);rowR2.add(rgbEnh);rowR2.add(rSLessS);rowR2.add(rSLessL);rowR2.addStretch();gRGB.sizer.add(rowR2);var rowR3=new VerticalSizer;rowR3.spacing=10;var iInt=new CheckBox(dlg);iInt.text="Integration (linear)";iInt.checked=state.save.rgb.integration_linear;var iBase=new CheckBox(dlg);iBase.text="Baseline (linear)";iBase.checked=state.save.rgb.baseline_linear;var iD1=new CheckBox(dlg);iD1.text="Save Round Stars (Deblur V1)";iD1.checked=state.save.rgb.deblur1;var iD2=new CheckBox(dlg);iD2.text="Save Enhance (Deblur V2)";iD2.checked=state.save.rgb.deblur2;var iDN=new CheckBox(dlg);iDN.text="Save Denoised (if stretched -> TIFF)";iDN.checked=state.save.rgb.denoised;var r3items=new HorizontalSizer;r3items.spacing=10;var lR3=new Label(dlg);lR3.text="Intermediates:";lR3.minWidth=120;r3items.add(lR3);r3items.add(iInt);r3items.add(iBase);r3items.add(iD1);r3items.add(iD2);r3items.add(iDN);r3items.addStretch();rowR3.add(r3items);var btnAdvRgb=new ToolButton(dlg);btnAdvRgb.text="Show Advanced (Intermediates) ▼";gRGB.sizer.add(btnAdvRgb);gRGB.sizer.add(rowR3);dlg.sizer.add(gRGB);var gM=new GroupBox(dlg);gM.title="Monochrome / Narrowband Outputs";gM.sizer=new VerticalSizer;gM.sizer.margin=8;gM.sizer.spacing=6;var rowMonoMaster=new HorizontalSizer;rowMonoMaster.spacing=10;var chkProcMono=new CheckBox(gM);chkProcMono.text="✅ Process Monochrome / Singles";chkProcMono.checked=state.processMonochrome;chkProcMono.toolTip="Enable or disable all Monochrome/single-channel processing and outputs.";rowMonoMaster.add(chkProcMono);rowMonoMaster.addStretch();gM.sizer.add(rowMonoMaster);var rowM1=new HorizontalSizer;rowM1.spacing=10;var lM1=new Label(dlg);lM1.text="🏆 Finals:";lM1.minWidth=120;var mFinalS=new CheckBox(dlg);mFinalS.text="Final (stretched, with stars) (TIFF)";mFinalS.checked=state.save.mono.final_stretched;var mFinalL=new CheckBox(dlg);mFinalL.text="Final (linear, with stars)";mFinalL.checked=state.save.mono.final_linear;rowM1.add(lM1);rowM1.add(mFinalS);rowM1.add(mFinalL);rowM1.addStretch();gM.sizer.add(rowM1);var rowM2=new HorizontalSizer;rowM2.spacing=10;var lM2=new Label(dlg);lM2.text="🎭 Starless & Masks:";lM2.minWidth=120;var mStarsS=new CheckBox(dlg);mStarsS.text="Stars (stretched mask) (TIFF)";mStarsS.checked=state.save.mono.stars_stretched;var mSLessS=new CheckBox(dlg);mSLessS.text="Starless (stretched) (TIFF)";mSLessS.checked=state.save.mono.starless_stretched;var mSLessL=new CheckBox(dlg);mSLessL.text="Starless (linear)";mSLessL.checked=state.save.mono.starless_linear;rowM2.add(lM2);rowM2.add(mStarsS);rowM2.add(mSLessS);rowM2.add(mSLessL);rowM2.addStretch();gM.sizer.add(rowM2);var rowM3=new VerticalSizer;rowM3.spacing=10;var miInt=new CheckBox(dlg);miInt.text="Integration (linear)";miInt.checked=state.save.mono.integration_linear;var miBase=new CheckBox(dlg);miBase.text="Baseline (linear)";miBase.checked=state.save.mono.baseline_linear;var miD1=new CheckBox(dlg);miD1.text="Save Round Stars (Deblur V1)";miD1.checked=state.save.mono.deblur1;var miD2=new CheckBox(dlg);miD2.text="Save Enhance (Deblur V2)";miD2.checked=state.save.mono.deblur2;var miDN=new CheckBox(dlg);miDN.text="Save Denoised (if stretched -> TIFF)";miDN.checked=state.save.mono.denoised;var m3items=new HorizontalSizer;m3items.spacing=10;var lM3=new Label(dlg);lM3.text="Intermediates:";lM3.minWidth=120;m3items.add(lM3);m3items.add(miInt);m3items.add(miBase);m3items.add(miD1);m3items.add(miD2);m3items.add(miDN);m3items.addStretch();rowM3.add(m3items);var btnAdvMono=new ToolButton(dlg);btnAdvMono.text="Show Advanced (Intermediates) ▼";gM.sizer.add(btnAdvMono);gM.sizer.add(rowM3);dlg.sizer.add(gM);var rgbControls=[cbCombine,rFinalS,rFinalL,rStarsS,rgbEnh,rSLessS,rSLessL,btnAdvRgb,iInt,iBase,iD1,iD2,iDN];var monoControls=[mFinalS,mFinalL,mStarsS,mSLessS,mSLessL,btnAdvMono,miInt,miBase,miD1,miD2,miDN];function toggleSection(enabled,controls){for(var i=0;i<controls.length;++i)controls[i].enabled=enabled;}chkProcRGB.onCheck=function(checked){toggleSection(checked,rgbControls);};chkProcMono.onCheck=function(checked){toggleSection(checked,monoControls);};function toggleAdvanced(row,btn){row.visible=!row.visible;btn.text=row.visible?"Hide Advanced (Intermediates) ▲":"Show Advanced (Intermediates) ▼";dlg.adjustToContents();}btnAdvRgb.onClick=function(){toggleAdvanced(rowR3,btnAdvRgb);};btnAdvMono.onClick=function(){toggleAdvanced(rowM3,btnAdvMono);};rowR3.visible=false;rowM3.visible=false;toggleSection(chkProcRGB.checked,rgbControls);toggleSection(chkProcMono.checked,monoControls);var rowBtn=new HorizontalSizer;rowBtn.spacing=8;rowBtn.addStretch();var bStart=new PushButton(dlg);bStart.text="Start";bStart.icon=dlg.scaledResource(":/icons/ok.png");bStart.defaultButton=true;var bCancel=new PushButton(dlg);bCancel.text="Cancel";bCancel.icon=dlg.scaledResource(":/icons/close.png");rowBtn.add(bStart);rowBtn.add(bCancel);dlg.sizer.add(rowBtn);bCancel.onClick=function(){dlg.cancel();};bStart.onClick=function(){if(!state.inputDir||!File.directoryExists(state.inputDir)){(new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();return;}if(!state.outputDir||!File.directoryExists(state.outputDir)){(new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();return;}state.processRGB=chkProcRGB.checked;state.processMonochrome=chkProcMono.checked;state.combineRGB=cbCombine.checked;state.ai.deblur1=cbD1.checked;state.ai.deblur2=cbD2.checked;state.ai.stretch=cbST.checked;state.ai.denoise=cbDN.checked;state.ai.starless=cbSL.checked;state.colorEnhanceRGBStarsStretched=rgbEnh.checked;state.spccGraphs=cbSPCCGraph.checked;state.save.rgb={final_stretched:rFinalS.checked,final_linear:rFinalL.checked,stars_stretched:rStarsS.checked,starless_stretched:rSLessS.checked,starless_linear:rSLessL.checked,integration_linear:iInt.checked,baseline_linear:iBase.checked,deblur1:iD1.checked,deblur2:iD2.checked,denoised:iDN.checked};state.save.mono={final_stretched:mFinalS.checked,final_linear:mFinalL.checked,stars_stretched:mStarsS.checked,starless_stretched:mSLessS.checked,starless_linear:mSLessL.checked,integration_linear:miInt.checked,baseline_linear:miBase.checked,deblur1:miD1.checked,deblur2:miD2.checked,denoised:miDN.checked};dlg.ok();};return dlg.execute();}
function run(){Console.show();Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — CORRECTED Stretched Star Mask ===");var settingsKey="PostIntegrationPipeline";var state={inputDir:Settings.read(settingsKey+"/InputDir",DataType_String)||defaults.inputDir,outputDir:Settings.read(settingsKey+"/OutputDir",DataType_String)||defaults.outputDir,processRGB:defaults.processRGB,processMonochrome:defaults.processMonochrome,combineRGB:defaults.combineRGB,ai:{deblur1:defaults.ai.deblur1,deblur2:defaults.ai.deblur2,stretch:defaults.ai.stretch,denoise:defaults.ai.denoise,starless:defaults.ai.starless},colorEnhanceRGBStarsStretched:defaults.colorEnhanceRGBStarsStretched,spccGraphs:defaults.spccGraphs,save:{rgb:defaults.save.rgb,mono:defaults.save.mono}};if(!showGUI(state)){Console.writeln("Cancelled.");return;}Settings.write(settingsKey+"/InputDir",DataType_String,state.inputDir);Settings.write(settingsKey+"/OutputDir",DataType_String,state.outputDir);Console.writeln("Input dir : "+state.inputDir);Console.writeln("Output dir: "+state.outputDir);var root=tsFolder(state.outputDir);Console.writeln("Output folder: "+root);ensureDir(root+"/5_stacked");ensureDir(root+"/6_final");try{var plan=buildWorkPlan(state.inputDir,state.combineRGB);var doRGB=state.processRGB&&plan.doRGB&&shouldProcessConfig(state.save.rgb);if(!state.processRGB&&!state.processMonochrome){Console.writeln("Both RGB and Monochrome processing are disabled. Exiting.");return;}if(doRGB){Console.writeln("\n→ Building RGB from:");Console.writeln("   R: "+File.extractName(plan.r));Console.writeln("   G: "+File.extractName(plan.g));Console.writeln("   B: "+File.extractName(plan.b));var combo=combineRGB(plan.r,plan.g,plan.b,root);processOne(combo.window,combo.base,root,state.ai,state.save.rgb,true,state.colorEnhanceRGBStarsStretched,false,state.spccGraphs);try{combo.window.forceClose();}catch(_){}}else{Console.writeln("RGB Combination: skipped (no R+G+B set found or option disabled).");}if(plan.singles.length>0){Console.writeln("\n→ Processing mono/narrowband/color singles: "+plan.singles.length);for(var i=0;i<plan.singles.length;++i){var singleInfo=plan.singles[i];var p=singleInfo.path;var tag=singleInfo.tag;var isStackedRGB=singleInfo.isStackedRGB;Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");var w=ImageWindow.open(p);if(w.length===0){Console.writeln("  ⚠️ Could not open, skipping.");continue;}var win=w[0],base=tag+"_"+sanitizeBase(File.extractName(p));var isColor=win.mainView.image.isColor;var saveCfg=isColor?state.save.rgb:state.save.mono;var procEnabled=isColor?state.processRGB:state.processMonochrome;if(!procEnabled||!shouldProcessConfig(saveCfg)){Console.writeln("  ⏭️ Skipped (processing or outputs disabled for this image type).");try{win.forceClose();}catch(_){}continue;}processOne(win,base,root,state.ai,saveCfg,false,isColor&&state.colorEnhanceRGBStarsStretched,isStackedRGB,state.spccGraphs);try{win.forceClose();}catch(_){}closeAllWindowsExcept(null);}}else{Console.writeln("\nNo single channel (Mono/NB/Color) masters found to process.");}Console.writeln("\n=== Done. Output: "+root+" ===");}catch(err){Console.criticalln("Error: "+err.message);if(DEBUG_MODE){(new MessageBox(err.message,"Script Aborted",StdIcon_Error,StdButton_Ok)).execute();}else{throw err;}}}

run();
})(); // IIFE
