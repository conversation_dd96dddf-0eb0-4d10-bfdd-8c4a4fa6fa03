// =================================================================
// PixInsight Script - Combine RGB channels from monochrome XISF files
// Filename-based detection, safe error handling, PixInsight v1.8+
// =================================================================

#include <pjsr/DataType.jsh>

function getChannelFromFilename(filePath) {
    var filename = File.extractName(filePath).toLowerCase();

    if (filename.indexOf("filter-red") !== -1 || filename.indexOf("filter-r") !== -1)
        return "R";
    if (filename.indexOf("filter-green") !== -1 || filename.indexOf("filter-g") !== -1)
        return "G";
    if (filename.indexOf("filter-blue") !== -1 || filename.indexOf("filter-b") !== -1)
        return "B";

    return "";
}

function loadImageByChannel(folder, targetChannel) {
    var ff = new FileFind;
    if (!ff.begin(folder + "/*.xisf")) {
        console.criticalln("❌ No .xisf files found in: " + folder);
        return null;
    }

    do {
        var filePath = folder + "/" + ff.name;
        var channel = getChannelFromFilename(filePath);
        console.writeln("🔍 Inspecting file: " + filePath + " | Detected channel: '" + channel + "'");

        if (channel === targetChannel) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0 && windowArray[0].mainView) {
                console.writeln("✅ Loaded '" + channel + "' channel image.");
                return windowArray[0];
            } else {
                console.warningln("⚠️ Failed to load image from: " + filePath);
            }
        }
    } while (ff.next());
    ff.end();

    console.criticalln("🚫 No matching image found for channel: " + targetChannel);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("🚀 Loading R, G, and B channel images...");

    var redWindow = loadImageByChannel(inputDir, "R");
    if (!redWindow) return;

    var greenWindow = loadImageByChannel(inputDir, "G");
    if (!greenWindow) return;

    var blueWindow = loadImageByChannel(inputDir, "B");
    if (!blueWindow) return;

    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    // Validate that views are valid
    if (!redView || !greenView || !blueView) {
        console.criticalln("❌ One or more image views are invalid.");
        return;
    }

    // Create output image
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,
        BitDepth_IEEE32,
        true,
        false,
        "RGB_Combined"
    );

    var outputView = outputImage.mainView;

    // Use ChannelCombination
    var combo = new ChannelCombination;
    combo.channels = [
        [redView.id],     // Red Channel
        [greenView.id],   // Green Channel
        [blueView.id]     // Blue Channel
    ];

    combo.executeOn(outputView, false); // replace = false

    outputImage.show();

    console.writeln("🌈 RGB channels successfully combined into a new image!");
}

main();
