/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * WORK7 VERSION - ROBUST SAVEAS FIX
 * - Fixes critical bug where showStretchedPreview (in previous versions) overwrote the ImageWindow.forceClose method.
 * - Fixes cleanup logic in processOne.
 * - Retains robust auto-revert and window handling.
 * - Corrected TIFF saving logic for Photoshop compatibility.
 * - FIX: Modified saveAs16BitTiff_Photoshop to use the safer 5-argument saveAs signature and robust path construction.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- Ensure this path is correct for your installation ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function() {

    // -------------------- Configuration & Globals --------------------
    var DEBUG_MODE = true;
    var INTERACTIVE_MODE = true; // Enable interactive step-by-step review
    var debugStepCounter = 1;
    var USER_ABORTED = false;

    // Custom Error Types for Flow Control
    const ABORT_PROCESSING = "ABORT_PROCESSING"; // Stop the entire batch
    const ABORT_PROCESSING_IMAGE = "ABORT_PROCESSING_IMAGE"; // Stop current image, continue batch

    var outputExtension = ".xisf";
    var defaults = {
        inputDir: "",
        outputDir: "",
        processRGB: true,
        processMonochrome: true,
        combineRGB: true,
        ai: {
            deblur1: true,
            deblur2: true,
            stretch: true,
            denoise: true,
            starless: true
        },
        colorEnhanceRGBStarsStretched: true,
        spccGraphs: false,
        save: {
            rgb: {
                final_stretched: false,
                final_linear: false,
                stars_stretched: true,
                starless_stretched: false,
                starless_linear: false,
                integration_linear: false,
                baseline_linear: false,
                deblur1: false,
                deblur2: false,
                denoised: false
            },
            mono: {
                final_stretched: false,
                final_linear: false,
                stars_stretched: false,
                starless_stretched: true,
                starless_linear: false,
                integration_linear: false,
                baseline_linear: false,
                deblur1: false,
                deblur2: false,
                denoised: false
            }
        }
    };

    // -------------------- Utility Functions --------------------
    function ensureDir(p) {
        if (!File.directoryExists(p)) File.createDirectory(p);
    }

    function tsFolder(base) {
        function p2(n) {
            return n < 10 ? "0" + n : "" + n;
        }
        var d = new Date();
        var ts = d.getFullYear() + "-" + p2(d.getMonth() + 1) + "-" + p2(d.getDate()) + "T" + p2(d.getHours()) + "-" + p2(d.getMinutes()) + "-" + p2(d.getSeconds());
        var out = base.replace(/\\/g, "/").replace(/\/$/, "") + "/Processed_" + ts;
        ensureDir(out);
        return out;
    }

    function closeAllWindowsExcept(ids) {
        var wins = ImageWindow.windows;
        for (var i = wins.length - 1; i >= 0; --i) {
            var keep = false;
            if (ids) {
                for (var j = 0; j < ids.length; ++j) {
                    if (wins[i].mainView.id === ids[j]) {
                        keep = true;
                        break;
                    }
                }
            }
            if (!keep) {
                try {
                    wins[i].forceClose();
                } catch (_) {}
            }
        }
    }

    function sanitizeBase(name) {
        var b = name.replace(/[^\w\-]+/g, "_");
        return b.length ? b : "image";
    }

    function fwd(p) {
        return p.replace(/\\/g, "/");
    }

    function removeIf(path, keep) {
        try {
            if (!keep && File.exists(path)) File.remove(path);
        } catch (_) {}
    }

    function shouldProcessConfig(cfg) {
        return !!(cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised);
    }

    function wait(ms) {
        var start = Date.now();
        var end = start + ms;
        while (Date.now() < end) {
            processEvents(); // Keep PixInsight responsive
        }
    }

    /*
     * CORRECTED TIFF SAVE FUNCTION (V7 - Robust Path and Signature)
     * Assumes input image is ALREADY STRETCHED.
     * Handles ICC profile embedding and conversion to 16-bit integer format.
     * FIX: Uses string concatenation for the file path for robustness.
     * FIX: Uses the safer 5-argument saveAs signature to resolve "String expected" errors.
     */
    function saveAs16BitTiff_Photoshop(win, path) {
        if (!win || !win.isWindow) {
            Console.writeln("Error: Invalid ImageWindow provided.");
            return;
        }

        // FIX 1: Robust Path Construction. Use string concatenation instead of File.changeExtension.
        // We assume the input 'path' does not already have an extension based on the script's usage.
        var tifPath = path + ".tif";

        var originalView = win.mainView;
        var isColor = originalView.image.isColor;

        Console.writeln("Starting Photoshop-compatible 16-bit TIFF conversion (Image already stretched)...");
        if (DEBUG_MODE) {
            Console.writeln("DEBUG: Target Path: " + tifPath);
        }

        var tempWin = null; // Initialize for cleanup

        try {
            // 1. Duplicate the image (necessary because we modify the duplicate by changing bit depth)
            Console.writeln("Duplicating image...");
            var duplicateId = "temp_ps_export_" + Date.now();
            var P_Clone = new PixelMath;
            P_Clone.expression = originalView.id;
            P_Clone.createNewImage = true;
            P_Clone.newImageId = duplicateId;
            P_Clone.newImageColorSpace = PixelMath.prototype.SameAsTarget;

            if (!P_Clone.executeOn(originalView)) {
                throw new Error("Failed to execute PixelMath for duplication.");
            }

            tempWin = ImageWindow.windowById(duplicateId);
            if (!tempWin) throw new Error("Failed to find the duplicated image window.");
            var tempView = tempWin.mainView;

            // 2. STRETCHING (STF/HT) IS SKIPPED. The main script already performed this.

            // 3. Assign ICC Profile (Crucial for Photoshop/Thumbnails).
            Console.writeln("Assigning ICC Profile...");
            var P_ICC = new ICCProfileTransformation;
            var profileName = "";

            if (isColor) {
                profileName = "sRGB IEC61966-2.1";
            } else {
                // For monochrome images, Gamma 2.2 is standard
                profileName = "Gray Gamma 2.2";
            }

            // Concise syntax to assign the profile by name
            P_ICC.assignProfile = profileName;

            // Try to assign the profile.
            try {
                P_ICC.executeOn(tempView);
            } catch (e) {
                Console.writeln("Warning: Failed to assign ICC profile (" + profileName + "). Ensure ICC profiles are configured in PixInsight settings. Error: " + e.message);
            }

            // 4. Convert to 16-bit unsigned integer.
            Console.writeln("Converting to 16-bit integer...");
            var P_SFC = new SampleFormatConversion;
            P_SFC.bitsPerSample = 16;
            P_SFC.sampleType = SampleFormatConversion.prototype.UnsignedInteger;
            if (!P_SFC.executeOn(tempView)) {
                throw new Error("Failed to execute SampleFormatConversion. Check console logs for details.");
            }

            // 5. Save as TIFF.
            Console.writeln("Saving file...");

            // FIX 2: Use the safer 5-argument signature.
            // (path, confirmOverwrite, signal, verbose, strict)
            // The 6th argument (embedICCProfile) is sometimes problematic and is redundant here
            // as the profile is already assigned to the window and will be embedded automatically.
            tempWin.saveAs(tifPath, false, false, true, true);

            Console.writeln("Successfully saved Photoshop-compatible 16-bit TIFF: ", tifPath);

        } catch (e) {
            Console.writeln("⚠️ Error during conversion and save: " + e.message);
        } finally {
            // 6. Clean up.
            if (tempWin && tempWin.isWindow) {
                tempWin.forceClose();
            }
        }
    }


    // -------------------- Debug and Revert Functions --------------------

    function debugSave(win, stepName, baseTag, debugDir) {
        if (!DEBUG_MODE) return;
        if (!win || !win.isWindow) {
            Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
            return;
        }

        // Create subfolder for this step
        let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
        let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
        let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;

        // Create the step subfolder
        try {
            if (!File.directoryExists(stepFolder)) {
                File.createDirectory(stepFolder, true);
            }
        } catch (e) {
            Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
            stepFolder = debugDir;
        }

        let baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;

        // Save as XISF (critical for revert)
        let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
        try {
            win.saveAs(xisfPath, false, false, false, false);
            Console.writeln("DEBUG: Saved XISF: " + xisfPath);
        } catch (e) {
            Console.writeln("DEBUG: Failed to save XISF: " + e.message);
            if (DEBUG_MODE) {
                Console.criticalln("CRITICAL: Debug save failed. Revert functionality may be compromised. Check disk space/permissions.");
            }
        }

        debugStepCounter++;
    }

    // ############ REVISED REVERT FUNCTION ############
    // Dynamically searches for the previous step's folder and file. Handles window validity.
    function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
        Console.writeln("🔄 REVERT: Looking for previous step before step " + currentStepNumber);

        if (currentStepNumber <= 1) {
            Console.writeln("⚠️ Cannot revert - already at the first step (Initial Integration).");
            return null; // Cannot revert before the start
        }

        var previousStepNumber = currentStepNumber - 1;
        var previousStepStr = (previousStepNumber < 10 ? "0" : "") + previousStepNumber;
        var revertFile = null;

        Console.writeln("🔍 Searching for previous step's folder starting with '" + previousStepStr + "_' in: " + debugDir);

        try {
            var ff = new FileFind();
            if (ff.begin(debugDir + "/" + previousStepStr + "_*")) {
                do {
                    if (ff.isDirectory) {
                        var folderPath = debugDir + "/" + ff.name;
                        // Calculate substring index dynamically based on the length of the step number string.
                        var stepNamePart = ff.name.substring(previousStepStr.length + 1); // Removes the "NN_" prefix

                        // Reconstruct the expected filename
                        var xisfFile = folderPath + "/" + previousStepStr + "_" + baseTag + "_" + stepNamePart + ".xisf";

                        if (File.exists(xisfFile)) {
                            revertFile = xisfFile;
                            Console.writeln("✅ Found revert file: " + revertFile);
                            break;
                        }
                    }
                } while (ff.next());
            }
            ff.end();
        } catch (e) {
            Console.writeln("❌ Error while searching for revert file: " + e.message);
        }

        if (!revertFile) {
            Console.writeln("❌ REVERT FAILED: Could not find the output file from step " + previousStepNumber);
            return null;
        }

        // --- Logic to open the file and handle window replacement ---
        try {
            Console.writeln("🔄 REVERTING: Loading " + revertFile);
            var currentId = "restored_view"; // Default ID

            // Check if the window is still valid before trying to close it
            // We must ensure 'win' is a valid ImageWindow object before accessing properties or methods.
            if (win && win instanceof ImageWindow && win.isWindow) {
                currentId = win.mainView.id;
                // Ensure forceClose is still a function (defensive programming)
                if (typeof win.forceClose === 'function') {
                    win.forceClose();
                } else {
                    // This should theoretically not happen anymore after FIX 1, but we keep the check.
                    Console.criticalln("❌ REVERT FAILED: win.forceClose() method is missing! Cannot close current window.");
                    return null;
                }
            } else {
                // If the window is gone (e.g., process failure closed it), we just proceed to open the restored one.
                Console.writeln("Note: Current window was already closed or invalid.");
            }

            var windows = ImageWindow.open(revertFile);
            if (windows.length > 0) {
                var restoredWin = windows[0];
                restoredWin.mainView.id = currentId; // Preserve the view ID if possible
                restoredWin.show();
                restoredWin.bringToFront();
                Console.writeln("✅ REVERT SUCCESSFUL");
                return restoredWin;
            } else {
                Console.writeln("❌ REVERT FAILED: Could not open file " + revertFile);
                return null;
            }
        } catch (e) {
            Console.writeln("❌ REVERT ERROR during file load: " + e.message);
            return null;
        }
    }

    // -------------------- Interactive Review Functions --------------------

    // ############ FIX #1: Removed the line that overwrote the forceClose method ############
    function showStretchedPreview(win) {
        if (!INTERACTIVE_MODE) return true;
        if (!win || !win.isWindow) return false;

        Console.writeln("📺 Applying auto-stretch for visual review...");

        try {
            win.show();
            win.bringToFront();
            win.zoomToFit();

            var view = win.mainView;

            // Calculate proper auto-stretch parameters
            var median = view.computeOrFetchProperty("Median");
            var mad = view.computeOrFetchProperty("MAD");

            var stf = new ScreenTransferFunction;
            var n = view.image.isColor ? 3 : 1;

            var stfParams = [
                [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1]
            ];

            for (var c = 0; c < n; ++c) {
                var med = median.at(c);
                var madVal = mad.at(c) * 1.4826; // Convert MAD to sigma

                var c0 = Math.max(0, med - 2.8 * madVal); // Shadow clipping
                var m = Math.mtf(0.25, med - c0); // Midtones balance

                stfParams[c] = [c0, 1.0, m, 0, 1];
            }

            // Apply the STF
            stf.STF = stfParams;
            stf.executeOn(view);

            // Force window refresh
            // BUGFIX: Removed `win.forceClose = false;` which was overwriting the method.
            win.show();
            win.bringToFront();

            Console.writeln("✅ Auto-stretch applied for review");
            return true;
        } catch (e) {
            Console.writeln("❌ Auto-stretch failed: " + e.message);
            return false;
        }
    }

    function resetStretch(win) {
        if (!INTERACTIVE_MODE) return true;
        if (!win || !win.isWindow) return false;

        try {
            if (typeof win.disableScreenTransferFunctions === 'function') {
                win.disableScreenTransferFunctions();
            } else {
                // Fallback: apply identity STF
                var stf = new ScreenTransferFunction;
                var identitySTF = [
                    [0, 1, 0.5, 0, 1],
                    [0, 1, 0.5, 0, 1],
                    [0, 1, 0.5, 0, 1],
                    [0, 1, 0.5, 0, 1]
                ];
                stf.STF = identitySTF;
                stf.executeOn(win.mainView);
            }
            Console.writeln("✅ Reset to linear view");
            return true;
        } catch (e) {
            Console.writeln("⚠️ Reset failed: " + e.message + " (continuing anyway)");
            return true;
        }
    }

    function askAcceptStep(stepName, description, stepNumber) {
        if (!INTERACTIVE_MODE) return "accept"; // Auto-accept if not interactive

        // Auto-accept all steps except GraXpert
        if (stepName !== "GraXpert") {
            Console.writeln("✅ AUTO-ACCEPTED Step " + stepNumber + ": " + stepName);
            return "accept";
        }

        // Only show interactive dialog for GraXpert
        Console.writeln("\n🔍 REVIEW STEP " + stepNumber + ": " + stepName);

        var result = (new MessageBox(
            "Step " + stepNumber + ": " + stepName + "\n\n" +
            (description || "Please review the image on screen.") + "\n\n" +
            "Choose your action:\n\n" +
            "YES = Accept and keep this step\n" +
            "NO = Skip this step (revert to previous step)\n" +
            "CANCEL = Stop processing entirely",
            "Step " + stepNumber + " - Accept, Skip, or Stop?",
            StdIcon_Question,
            StdButton_Yes, StdButton_No, StdButton_Cancel
        )).execute();

        if (result == StdButton_Yes) {
            Console.writeln("✅ ACCEPTED Step " + stepNumber + ": " + stepName);
            return "accept";
        } else if (result == StdButton_No) {
            Console.writeln("⏭️ SKIPPED Step " + stepNumber + ": " + stepName + " - reverting to previous step");
            return "skip";
        } else {
            Console.writeln("🛑 STOPPED: Processing aborted by user at Step " + stepNumber + ": " + stepName);
            USER_ABORTED = true;
            throw new Error(ABORT_PROCESSING); // Force immediate stop of the batch
        }
    }

    // -------------------- File System Functions --------------------
    function findAllInputImages(dir) {
        if (!File.directoryExists(dir)) throw new Error("Input directory does not exist: " + dir);
        var v = [];
        var ff = new FileFind;
        var supportedExtensions = [".xisf", ".fit", ".fits", ".tif", ".tiff"];
        if (ff.begin(dir + "/*.*")) {
            do {
                var nameLower = ff.name.toLowerCase();
                for (var i = 0; i < supportedExtensions.length; ++i) {
                    if (nameLower.endsWith(supportedExtensions[i])) {
                        v.push(dir + "/" + ff.name);
                        break;
                    }
                }
            } while (ff.next());
        }
        return v;
    }

    function detectFilterFromName(fileName) {
        var s = fileName.toLowerCase();
        if (s.indexOf("filter-red") >= 0 || /filter-r(?![a-z])/.test(s) || /\bred\b/.test(s)) return "R";
        if (s.indexOf("filter-green") >= 0 || /filter-g(?![a-z])/.test(s) || /\bgreen\b/.test(s)) return "G";
        if (s.indexOf("filter-blue") >= 0 || /filter-b(?![a-z])/.test(s) || /\bblue\b/.test(s)) return "B";
        if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
        if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
        if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
        if (/sii|s2|s\-?ii/.test(s)) return "SII";
        return null;
    }

    function buildWorkPlan(dir, combineRGB) {
        var files = findAllInputImages(dir);
        var byFilter = {},
            unknownSingles = [];
        for (var i = 0; i < files.length; ++i) {
            var name = File.extractName(files[i]);
            var f = detectFilterFromName(name);
            if (f) {
                if (!byFilter[f]) byFilter[f] = [];
                byFilter[f].push(files[i]);
            } else {
                unknownSingles.push(files[i]);
            }
        }

        function pickFirst(arr) {
            if (!arr || !arr.length) return null;
            arr.sort();
            return arr[0];
        }
        var haveR = !!(byFilter.R && byFilter.R.length),
            haveG = !!(byFilter.G && byFilter.G.length),
            haveB = !!(byFilter.B && byFilter.B.length);
        var plan = {
            doRGB: false,
            r: null,
            g: null,
            b: null,
            singles: []
        };
        if (combineRGB && haveR && haveG && haveB) {
            plan.doRGB = true;
            plan.r = pickFirst(byFilter.R);
            plan.g = pickFirst(byFilter.G);
            plan.b = pickFirst(byFilter.B);
        }

        function pushSingles(k) {
            if (byFilter[k])
                for (var i = 0; i < byFilter[k].length; ++i) {
                    var p = byFilter[k][i];
                    if (plan.doRGB && ((k === "R" && p === plan.r) || (k === "G" && p === plan.g) || (k === "B" && p === plan.b))) continue;
                    plan.singles.push({
                        path: p,
                        tag: k,
                        isStackedRGB: false
                    });
                }
        }
        ["L", "Ha", "OIII", "SII"].forEach(pushSingles);
        if (!plan.doRGB) ["R", "G", "B"].forEach(pushSingles);
        for (var k = 0; k < unknownSingles.length; ++k) {
            var filePath = unknownSingles[k];
            var isColorStack = false;
            try {
                var tempWinArr = ImageWindow.open(filePath);
                if (tempWinArr.length > 0) {
                    var tempWin = tempWinArr[0];
                    if (tempWin.mainView.image.isColor) isColorStack = true;
                    tempWin.forceClose();
                }
            } catch (e) {
                Console.writeln("Warning: Could not determine color space for " + File.extractName(filePath) + ". Assuming Mono.");
            }
            plan.singles.push({
                path: filePath,
                tag: isColorStack ? "Color" : "Single",
                isStackedRGB: isColorStack
            });
        }
        return plan;
    }

    // -------------------- Processing Functions (Robust Pattern) --------------------

    /*
     * Generic handler for process execution, interactive review, and auto-revert on failure.
     */
    function handleRobustExecution(win, stepName, sum, sumKey, executionFunc, baseTag, debugDir) {
        if (!win || !win.isWindow) {
            Console.criticalln("❌ CRITICAL: Invalid window passed to " + stepName + ". Aborting image.");
            throw new Error(ABORT_PROCESSING_IMAGE);
        }

        try {
            Console.writeln("\n=== Running " + stepName + " ===");

            // 1. Execute the core logic (provided via executionFunc)
            var details = executionFunc(win);
            sum[sumKey] = {
                name: stepName,
                status: "✅",
                details: details || "Applied successfully"
            };

            // 2. Save successful state
            debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_'), baseTag, debugDir);

            // The step number that was just executed and saved.
            var executedStepNumber = debugStepCounter - 1;

            // 3. Interactive Review
            if (INTERACTIVE_MODE) {
                // Check if image is already stretched (after nuclear stretch)
                var isAlreadyStretched = (sum.stretch && sum.stretch.status === "✅");

                if (isAlreadyStretched) {
                    // Image is already stretched - just show it
                    win.show();
                    win.bringToFront();
                    win.zoomToFit();
                    Console.writeln("📺 Displaying already stretched image for review");
                } else {
                    // Image is linear - apply auto-stretch for preview
                    showStretchedPreview(win);
                }

                processEvents();
                msleep(1000);

                var decision = askAcceptStep(stepName, "Review the result of " + stepName + ".", executedStepNumber);

                if (!isAlreadyStretched) {
                    resetStretch(win);
                }

                if (decision === "skip") {
                    // Revert FROM the executed step
                    var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                    if (restoredWin) {
                        win = restoredWin;
                        sum[sumKey].status = "⏭️";
                        sum[sumKey].details = "Skipped by user";
                    } else {
                        Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                        throw new Error(ABORT_PROCESSING_IMAGE);
                    }
                }
            }

            return win;

        } catch (e) {
            if (e.message === ABORT_PROCESSING || e.message === ABORT_PROCESSING_IMAGE) throw e;

            // 4. Handle Failure and Auto-Revert
            Console.writeln("❌ " + stepName + " FAILED: " + e.message);

            // Save failed state (optional, if the window is still valid)
            if (win && win.isWindow) {
                debugSave(win, "After_" + stepName.replace(/[\s\(\)]+/g, '_') + "_FAILED", baseTag, debugDir);
            }

            // The step number associated with the failure (or the failed save).
            var failedStepNumber = debugStepCounter - 1;

            Console.writeln("🔄 Attempting automatic revert because " + stepName + " failed.");

            // Revert FROM the failed step.
            var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

            if (restoredWin) {
                win = restoredWin;
                sum[sumKey] = {
                    name: stepName,
                    status: "⚠️❌",
                    details: "Failed and reverted. Error: " + e.message
                };
            } else {
                // Critical failure
                // Check if the revert failed critically (returned null)
                if (!restoredWin) {
                    Console.criticalln("❌ CRITICAL: " + stepName + " failed, and revert failed or image window is gone.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
                // If we still have a window but revert failed (unlikely path but handled)
                sum[sumKey] = {
                    name: stepName,
                    status: "❌",
                    details: "Failed, and revert also failed. Error: " + e.message
                };
            }
            return win;
        }
    }

    // --- Specific Implementations using the Robust Pattern ---

    function finalABE(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "ABE (Background Extraction)", sum, "backgroundExtraction", function(w) {
            var P = new AutomaticBackgroundExtractor;
            P.tolerance = 1.000;
            P.deviation = 0.800;
            P.unbalance = 1.800;
            P.minBoxFraction = 0.050;
            P.polyDegree = 4;
            P.boxSize = 5;
            P.boxSeparation = 5;
            P.targetCorrection = 0; // 0 = Subtraction, 1 = Division
            P.normalize = true;
            P.replaceTarget = true;
            P.discardModel = true;
            P.executeOn(w.mainView);
            return "ABE Subtraction applied";
        }, baseTag, debugDir);
    }

    function runGradientCorrection(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "GradientCorrection", sum, "gradientCorrection", function(w) {
            var P = new GradientCorrection;
            P.reference = 0.50;
            P.lowThreshold = 0.20;
            P.lowTolerance = 0.50;
            P.highThreshold = 0.05;
            P.iterations = 15;
            P.scale = 7.60;
            P.smoothness = 0.71;
            P.downsamplingFactor = 16;
            P.protection = true;
            P.automaticConvergence = true;
            P.executeOn(w.mainView);
            return "Applied with custom settings";
        }, baseTag, debugDir);
    }

    function runGraXpert(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "GraXpert", sum, "graxpert", function(w) {
            let gxp = new GraXpertLib;
            gxp.graxpertParameters.correction = 0;
            gxp.graxpertParameters.smoothing = 0.964;
            gxp.graxpertParameters.replaceTarget = true;
            gxp.graxpertParameters.showBackground = false;
            gxp.graxpertParameters.targetView = w.mainView;
            gxp.process();
            return "Applied via library call";
        }, baseTag, debugDir);
    }

    function autoBlackPoint(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "Black Point Adjustment", sum, "blackPoint", function(w) {
            var img = w.mainView.image;
            var details = "";

            if (!img.readSamples) {
                // Fallback 1: Cannot read samples
                var AH_fallback = new AutoHistogram();
                AH_fallback.auto = true;
                AH_fallback.clipLow = 0.1;
                AH_fallback.clipHigh = 0.1;
                AH_fallback.executeOn(w.mainView);
                details = "Fallback AutoHistogram (No sample access)";
            } else if (!img.isColor || img.numberOfChannels < 3) {
                // Mono/NB
                var AH = new AutoHistogram();
                AH.auto = true;
                AH.clipLow = 0.1;
                AH.clipHigh = 0.1;
                AH.executeOn(w.mainView);
                details = "AutoHistogram (Mono/NB)";
            } else {
                // Color: Manual sampling and Levels
                var width = img.width,
                    height = img.height,
                    sampleSize = 20,
                    numSamples = 20;
                var rs = [],
                    gs = [],
                    bs = [];
                for (var i = 0; i < numSamples; ++i) {
                    var x = Math.floor(Math.random() * (width - sampleSize)),
                        y = Math.floor(Math.random() * (height - sampleSize));
                    var rect = new Rect(x, y, x + sampleSize, y + sampleSize),
                        s = img.readSamples(rect),
                        cnt = sampleSize * sampleSize,
                        r = 0,
                        g = 0,
                        b = 0;
                    for (var p = 0; p < cnt; ++p) {
                        r += s[p * 3];
                        g += s[p * 3 + 1];
                        b += s[p * 3 + 2];
                    }
                    rs.push(r / cnt);
                    gs.push(g / cnt);
                    bs.push(b / cnt);
                }
                rs.sort(function(a, b) {
                    return a - b;
                });
                gs.sort(function(a, b) {
                    return a - b;
                });
                bs.sort(function(a, b) {
                    return a - b;
                });
                var n = Math.max(1, Math.floor(numSamples * 0.25)),
                    R = 0,
                    G = 0,
                    B = 0;
                for (var m = 0; m < n; ++m) {
                    R += rs[m];
                    G += gs[m];
                    B += bs[m];
                }
                R /= n;
                G /= n;
                B /= n;

                try {
                    var L = new Levels();
                    L.redBlack = Math.min(R * 0.8, 0.02);
                    L.greenBlack = Math.min(G * 0.9, 0.03);
                    L.blueBlack = Math.min(B * 0.8, 0.02);
                    L.redWhite = 0.98;
                    L.greenWhite = 0.97;
                    L.blueWhite = 0.98;
                    L.executeOn(w.mainView);
                    details = "Levels (Sampled)";
                } catch (e2) {
                    // Fallback 2: Levels failed
                    var AH2 = new AutoHistogram();
                    AH2.auto = true;
                    AH2.clipLow = 0.1;
                    AH2.clipHigh = 0.1;
                    AH2.executeOn(w.mainView);
                    details = "Fallback AutoHistogram (Color)";
                }
            }
            return details;
        }, baseTag, debugDir);
    }

    // -------------------- AI steps --------------------

    function deblur1(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "Deblur V1 (Round Stars)", sum, "deblurV1", function(w) {
            var P = new BlurXTerminator();
            P.sharpenStars = 0.00;
            P.adjustStarHalos = 0.00;
            P.psfDiameter = 0.00;
            P.sharpenNonstellar = 0.00;
            P.autoPSF = true;
            P.correctOnly = true;
            P.correctFirst = false;
            P.nonstellarThenStellar = false;
            P.luminanceOnly = false;
            P.executeOn(w.mainView);
            return "Round stars correction applied";
        }, baseTag, debugDir);
    }

    function deblur2(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "Deblur V2 (Enhance)", sum, "deblurV2", function(w) {
            var P = new BlurXTerminator();
            P.sharpenStars = 0.25;
            P.adjustStarHalos = 0.00;
            P.psfDiameter = 0.00;
            P.sharpenNonstellar = 0.90;
            P.autoPSF = true;
            P.correctOnly = false;
            P.correctFirst = false;
            P.nonstellarThenStellar = false;
            P.luminanceOnly = false;
            P.executeOn(w.mainView);
            return "Enhancement applied";
        }, baseTag, debugDir);
    }

    function denoise(win, sum, baseTag, debugDir) {
        return handleRobustExecution(win, "Denoising (NoiseX)", sum, "denoising", function(w) {
            var P = new NoiseXTerminator();
            P.intensityColorSeparation = false;
            P.frequencySeparation = false;
            P.denoise = 0.90;
            P.iterations = 2;
            P.executeOn(w.mainView);
            return "Denoising applied (0.90)";
        }, baseTag, debugDir);
    }

    // -------------------- ImageSolver + SPCC (Standard Pattern) --------------------
    // Solver/SPCC failure usually means skipping calibration, not reverting the image state.

    function solveImage(win, sum, baseTag, debugDir) {
        try {
            Console.writeln("\n=== ImageSolver (for SPCC) ===");
            var solver = new ImageSolver();
            solver.Init(win, false);
            solver.useMetadata = true;
            solver.catalog = "GAIA DR3";
            solver.useDistortionCorrection = false;
            solver.generateErrorMaps = false;
            solver.showStars = false;
            solver.showDistortion = false;
            solver.generateDistortionMaps = false;
            solver.sensitivity = 0.1;

            if (!solver.SolveImage(win)) {
                Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");
                solver.useMetadata = false;
                if (!solver.SolveImage(win)) throw new Error("Plate solution not found.");
            }
            sum.solver = {
                name: "ImageSolver",
                status: "✅",
                details: "Solved (GAIA DR3)"
            };

            // Debug save after ImageSolver (metadata updated)
            debugSave(win, "After_ImageSolver", baseTag, debugDir);
            return true;
        } catch (e) {
            sum.solver = {
                name: "ImageSolver",
                status: "❌",
                details: e.message
            };
            return false;
        }
    }

    // profileSelector: "OSC" or "RGB"
    function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir) {
        try {
            Console.writeln("\n=== SPCC (using fresh WCS) — profile: " + profileSelector + " ===");
            var P = new SpectrophotometricColorCalibration();

            try {
                P.whiteReferenceId = "Average Spiral Galaxy";
            } catch (_) {
                try {
                    P.whiteReferenceId = "AVG_G2V";
                } catch (_) {}
            }
            try {
                P.qeCurve = "Sony IMX411/455/461/533/571";
            } catch (_) {}
            try {
                if (profileSelector === "RGB") {
                    P.redFilter = "Astronomik Typ 2c R";
                    P.greenFilter = "Astronomik Typ 2c G";
                    P.blueFilter = "Astronomik Typ 2c B";
                } else {
                    P.redFilter = "Sony Color Sensor R";
                    P.greenFilter = "Sony Color Sensor G";
                    P.blueFilter = "Sony Color Sensor B";
                }
            } catch (_) {}
            try {
                P.narrowbandMode = false;
            } catch (_) {}
            try {
                P.generateGraphs = showGraphs || false;
            } catch (_) {}
            try {
                P.generateStarMaps = false;
            } catch (_) {}
            try {
                P.generateTextFiles = false;
            } catch (_) {}
            try {
                P.applyCalibration = true;
            } catch (_) {}
            try {
                P.catalog = "Gaia DR3/SP";
            } catch (_) {}
            try {
                P.automaticLimitMagnitude = true;
            } catch (_) {}
            try {
                P.backgroundNeutralization = true;
                P.lowerLimit = -2.80;
                P.upperLimit = +2.00;
            } catch (_) {}
            try {
                P.structureLayers = 5;
                P.saturationThreshold = 0.99;
                P.backgroundReferenceViewId = "";
                P.limitMagnitude = 16.0;
            } catch (_) {}

            P.executeOn(win.mainView);
            sum.spcc = {
                name: "SPCC",
                status: "✅",
                details: "Applied"
            };

            // Debug save after SPCC
            debugSave(win, "After_SPCC", baseTag, debugDir);
            return true;

        } catch (e) {
            sum.spcc = {
                name: "SPCC",
                status: "❌",
                details: e.message
            };
            return false;
        }
    }

    // -------------------- Stretch Functions --------------------

    /*
     * STF Auto Stretch routine
     */
    function STFAutoStretch(view, shadowsClipping, targetBackground, rgbLinked) {
        if (shadowsClipping === undefined) shadowsClipping = -2.80;
        if (targetBackground === undefined) targetBackground = 0.25;
        if (rgbLinked === undefined) rgbLinked = true;

        var stf = new ScreenTransferFunction;
        var n = view.image.isColor ? 3 : 1;

        // 1. Calculate Statistics - Create copies to prevent corruption
        var median = new Vector(view.computeOrFetchProperty("Median"));
        var mad = new Vector(view.computeOrFetchProperty("MAD"));

        // 2. Normalize MAD (1.4826 * MAD ≈ sigma)
        mad.mul(1.4826);

        // 3. Calculate Stretch Parameters - STF Parameter Order: [c0, c1, m, r0, r1]
        if (rgbLinked && n > 1) {
            // Linked RGB channels
            var invertedChannels = 0;
            for (var c = 0; c < n; ++c)
                if (median.at(c) > 0.5)
                    ++invertedChannels;

            if (invertedChannels < n) {
                // Noninverted image
                var c0_sum = 0,
                    median_sum = 0;
                for (var c = 0; c < n; ++c) {
                    if (1 + mad.at(c) != 1)
                        c0_sum += median.at(c) + shadowsClipping * mad.at(c);
                    median_sum += median.at(c);
                }
                var c0 = Math.range(c0_sum / n, 0.0, 1.0);
                var m = Math.mtf(targetBackground, median_sum / n - c0);

                stf.STF = [
                    [c0, 1, m, 0, 1],
                    [c0, 1, m, 0, 1],
                    [c0, 1, m, 0, 1],
                    [0, 1, 0.5, 0, 1]
                ];
            } else {
                // Inverted image logic
                var c1_sum = 0,
                    median_sum = 0;
                for (var c = 0; c < n; ++c) {
                    if (1 + mad.at(c) != 1)
                        c1_sum += median.at(c) - shadowsClipping * mad.at(c);
                    median_sum += median.at(c);
                }
                var c1 = Math.range(c1_sum / n, 0.0, 1.0);
                var m = Math.mtf(c1 - median_sum / n, targetBackground);

                stf.STF = [
                    [0, c1, m, 0, 1],
                    [0, c1, m, 0, 1],
                    [0, c1, m, 0, 1],
                    [0, 1, 0.5, 0, 1]
                ];
            }
        } else {
            // Unlinked channels (or Grayscale image)
            var A = [
                [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1]
            ];

            for (var c = 0; c < n; ++c) {
                if (median.at(c) < 0.5) {
                    var c0 = (1 + mad.at(c) != 1) ?
                        Math.range(median.at(c) + shadowsClipping * mad.at(c), 0.0, 1.0) : 0.0;
                    var m = Math.mtf(targetBackground, median.at(c) - c0);
                    A[c] = [c0, 1, m, 0, 1];
                } else {
                    var c1 = (1 + mad.at(c) != 1) ?
                        Math.range(median.at(c) - shadowsClipping * mad.at(c), 0.0, 1.0) : 1.0;
                    var m = Math.mtf(c1 - median.at(c), targetBackground);
                    A[c] = [0, c1, m, 0, 1];
                }
            }
            stf.STF = A;
        }

        return stf;
    }

    /*
     * Apply HistogramTransformation
     */
    function ApplyHistogramTransformation(view, stf) {
        var ht = new HistogramTransformation;

        // HT Parameter Order: [c0, m, c1, r0, r1]
        var HT_IDENTITY = [0, 0.5, 1, 0, 1];
        var H = [
            HT_IDENTITY.slice(), HT_IDENTITY.slice(), HT_IDENTITY.slice(),
            HT_IDENTITY.slice(), HT_IDENTITY.slice()
        ];

        var n = view.image.isColor ? 3 : 1;

        // Helper function to map STF array to HT array
        // STF input indices: [0=c0, 1=c1, 2=m, 3=r0, 4=r1]
        // HT output indices: [0=c0, 1=m, 2=c1, 3=r0, 4=r1]
        function mapSTFtoHT(stf_channel) {
            return [
                stf_channel[0], // c0
                stf_channel[2], // m
                stf_channel[1], // c1
                stf_channel[3], // r0
                stf_channel[4] // r1
            ];
        }

        if (view.image.isColor) {
            // Check if the STF channels are linked
            var linked = true;
            for (var c = 1; c < n; ++c) {
                if (stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
                    linked = false;
                    break;
                }
            }

            if (linked) {
                // Apply linked transform to the combined RGB/K channel (Index 3)
                H[3] = mapSTFtoHT(stf.STF[0]);
            } else {
                // Apply unlinked transforms to individual channels (Indices 0, 1, 2)
                for (var c = 0; c < n; ++c) {
                    H[c] = mapSTFtoHT(stf.STF[c]);
                }
            }
        } else {
            // Monochrome image (Apply to Index 3 for K channel)
            H[3] = mapSTFtoHT(stf.STF[0]);
        }

        ht.H = H;
        return ht.executeOn(view);
    }

    function applyPerfectNuclearStretch(view) {
        if (!view || !view.image || view.image.isNull)
            throw new Error("No active view for nuclear stretch.");

        // Use proper linking: RGB linked for color images, unlinked for mono images
        var isColor = view.image.isColor;
        var rgbLinked = isColor; // true for RGB, false for mono

        Console.writeln("Nuclear stretch: " + (isColor ? "RGB linked" : "Mono unlinked") + " mode");

        // Calculate STF Auto Stretch parameters
        var stf = STFAutoStretch(view, -2.80, 0.25, rgbLinked);

        // Apply the calculated parameters using HistogramTransformation
        return ApplyHistogramTransformation(view, stf);
    }

    // -------------------- Color Enhance helpers --------------------
    function applyColorEnhanceToView(view) {
        // Curves: S channel enhancement
        var C = new CurvesTransformation;
        // Initialize all channels to identity
        C.R = C.G = C.B = C.K = C.A = C.L = C.a = C.b = C.c = C.H = [
            [0.00000, 0.00000],
            [1.00000, 1.00000]
        ];
        C.Rt = C.Gt = C.Bt = C.Kt = C.At = C.Lt = C.at = C.bt = C.ct = C.Ht = CurvesTransformation.prototype.AkimaSubsplines;

        // Apply S curve
        C.S = [
            [0.00000, 0.00000],
            [0.14470, 0.33247],
            [1.00000, 1.00000]
        ];
        C.St = CurvesTransformation.prototype.AkimaSubsplines;
        C.executeOn(view);

        // SCNR
        var N = new SCNR;
        N.amount = 0.73;
        N.protectionMethod = SCNR.prototype.AverageNeutral;
        N.colorToRemove = SCNR.prototype.Green;
        N.preserveLightness = true;
        N.executeOn(view);
    }

    // -------------------- Main Processing Steps --------------------

    function combineRGB(redPath, greenPath, bluePath, rootOut) {
        Console.writeln("\n=== RGB Channel Combination ===");
        var r = ImageWindow.open(redPath),
            g = ImageWindow.open(greenPath),
            b = ImageWindow.open(bluePath);
        if (r.length === 0 || g.length === 0 || b.length === 0) throw new Error("Failed to open one or more RGB masters");
        var CC = new ChannelCombination;
        CC.colorSpace = ChannelCombination.prototype.RGB;
        CC.channels = [
            [true, r[0].mainView.id],
            [true, g[0].mainView.id],
            [true, b[0].mainView.id]
        ];
        CC.executeGlobal();
        var rgb = null,
            wins = ImageWindow.windows;
        for (var i = wins.length - 1; i >= 0; --i) {
            if (wins[i].mainView.image.isColor) {
                rgb = wins[i];
                break;
            }
        }
        if (!rgb) throw new Error("Could not find RGB combined image");
        var stackDir = rootOut + "/5_stacked";
        ensureDir(stackDir);
        var outPath = stackDir + "/RGB_Combined" + outputExtension;
        rgb.saveAs(outPath, false, false, false, false);
        try {
            r[0].close();
        } catch (_) {}
        try {
            g[0].close();
        } catch (_) {}
        try {
            b[0].close();
        } catch (_) {}
        return {
            window: rgb,
            path: outPath,
            base: "RGB_Combined"
        };
    }

    // Custom implementation for Star Separation to fit the robust pattern
    function starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir) {
        try {
            Console.writeln("\n=== Running StarXTerminator ===");

            // Create a temporary file to save the original image before StarX
            var tempDir = debugDir + "/temp";
            if (!File.directoryExists(tempDir)) {
                File.createDirectory(tempDir, true);
            }
            var tempOriginalPath = tempDir + "/temp_original_" + baseTag + "_" + Date.now() + ".xisf";

            // Save the current state before applying StarX
            win.saveAs(tempOriginalPath, false, false, false, false);
            Console.writeln("✅ Saved temporary original for star generation");

            var SX = new StarXTerminator();
            SX.generateStarImage = true;
            // Images are already stretched at this point in the pipeline
            SX.unscreenStars = true;
            SX.largeOverlap = false;

            // Execute StarX on the main window 'win' (it becomes starless)
            SX.executeOn(win.mainView);

            // 'win' is now the starless image.
            sum.starSeparation = {
                name: "Star Separation",
                status: "✅",
                details: "StarX applied on stretched image"
            };
            debugSave(win, "After_StarSeparation_Starless", baseTag, debugDir);

            var executedStepNumber = debugStepCounter - 1;

            // --- Interactive Review for Star Separation ---
            var separationAccepted = true;
            if (INTERACTIVE_MODE) {
                win.show();
                win.bringToFront();
                win.zoomToFit();
                processEvents();
                msleep(1000);

                var decision = askAcceptStep("StarX Separation",
                    "StarXTerminator has separated stars from nebulosity.",
                    executedStepNumber);

                if (decision === "skip") {
                    separationAccepted = false;
                    var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                    if (restoredWin) {
                        win = restoredWin;
                        sum.starSeparation.status = "⏭️";
                        sum.starSeparation.details = "Skipped by user";
                        // Cleanup temp file and return early
                        try {
                            File.remove(tempOriginalPath);
                        } catch (_) {}
                        return win;
                    } else {
                        Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                        throw new Error(ABORT_PROCESSING_IMAGE);
                    }
                }
            }

            // --- Generate Stars using PixelMath: max(0, Original - Starless) ---
            var starsWin = null;
            if (separationAccepted && saveCfg.stars_stretched) {
                try {
                    // Load the original image from temp file
                    var originalWinArray = ImageWindow.open(tempOriginalPath);
                    if (originalWinArray.length > 0) {
                        var originalWin = originalWinArray[0];
                        originalWin.mainView.id = "TempOriginal_" + Date.now();

                        var starsId = "Stars_" + baseTag + "_" + Date.now();
                        var exprStars = "max(0, " + originalWin.mainView.id + " - " + win.mainView.id + ")";

                        var PM = new PixelMath();
                        PM.useSingleExpression = true;
                        PM.expression = exprStars;
                        PM.createNewImage = true;
                        PM.newImageId = starsId;
                        PM.newImageColorSpace = PixelMath.prototype.SameAsTarget;
                        PM.rescaleResult = false;
                        PM.truncateResult = false;
                        PM.executeOn(originalWin.mainView);

                        starsWin = ImageWindow.windowById(starsId);
                        if (!starsWin) {
                            Console.writeln("⚠️ Stars image creation failed, but continuing with starless");
                        } else {
                            Console.writeln("✅ Generated stars using PixelMath: Original - Starless");
                        }

                        // Cleanup the temporary original window
                        try {
                            originalWin.forceClose();
                        } catch (_) {}
                    } else {
                        Console.writeln("⚠️ Could not reload original image for star generation");
                    }
                } catch (e) {
                    Console.writeln("⚠️ Stars generation failed: " + e.message + ", but continuing with starless");
                }
            }

            // --- Save Outputs (only if accepted) ---
            if (separationAccepted) {
                // Save starless (stretched)
                if (saveCfg.starless_stretched) {
                    saveAs16BitTiff_Photoshop(win, finalDir + "/Starless_Stretched_" + baseTag);
                    Console.writeln("  Saved Starless (stretched): Starless_Stretched_" + baseTag + ".tif");
                }

                // Save starless (linear) - not applicable since image is already stretched
                if (saveCfg.starless_linear) {
                    Console.writeln("  ⚠️ Starless (linear) requested but image is already stretched - skipping");
                }

                // Save stars (stretched)
                if (saveCfg.stars_stretched && starsWin) {
                    // Enhance star colors if requested
                    if (isColor && enhanceRGBStarsStretched) {
                        Console.writeln("Applying color enhancement to stars...");
                        applyColorEnhanceToView(starsWin.mainView);
                        // Add to summary
                        sum.starColorsEnhanced = {
                            name: "Star colors enhanced",
                            status: "✅",
                            details: "S-curve and SCNR applied"
                        };
                    }
                    saveAs16BitTiff_Photoshop(starsWin, finalDir + "/Stars_Stretched_" + baseTag);
                    Console.writeln("  Saved Stars (stretched): Stars_Stretched_" + baseTag + ".tif");
                }
            }

            // Cleanup temporary files and windows
            try {
                if (starsWin) starsWin.forceClose();
            } catch (_) {}
            try {
                File.remove(tempOriginalPath);
            } catch (_) {}

            return win;

        } catch (e) {
            if (e.message.startsWith("ABORT_")) throw e;

            // Automatic Revert on Failure for Star Separation
            Console.writeln("❌ Star Separation FAILED: " + e.message);
            if (win && win.isWindow) {
                debugSave(win, "After_StarSeparation_FAILED", baseTag, debugDir);
            }

            var failedStepNumber = debugStepCounter - 1;
            Console.writeln("🔄 Attempting automatic revert because Star Separation failed.");

            var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

            if (restoredWin) {
                win = restoredWin;
                sum.starSeparation = {
                    name: "Star Separation",
                    status: "⚠️❌",
                    details: "Failed and reverted. Error: " + e.message
                };
            } else {
                if (!restoredWin) {
                    Console.criticalln("❌ CRITICAL: Star Separation failed, revert failed, or image window is gone.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
                sum.starSeparation = {
                    name: "Star Separation",
                    status: "❌",
                    details: "Failed, and revert also failed. Error: " + e.message
                };
            }
            return win;
        }
    }

    // ############ REVISED processOne FUNCTION ############
    // Manages the workflow, handles window references, and manages error states.
    function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB = false, spccGraphs = false) {
        var sum = {};
        var stackDir = rootOut + "/5_stacked";
        ensureDir(stackDir);
        var finalDir = rootOut + "/6_final";
        ensureDir(finalDir);
        // Shorten debug folder name to avoid Windows path length limits
        var shortTag = baseTag.length > 20 ? baseTag.substring(0, 20) : baseTag;
        var debugDir = rootOut + "/debug_" + shortTag;

        // Initialize counters for the current image processing run
        debugStepCounter = 1;
        USER_ABORTED = false;

        if (DEBUG_MODE) ensureDir(debugDir);

        // --- Initial State Save ---
        // Save the starting state of the image as Step 01. This guarantees a revert point.
        debugSave(win, "Initial_Integration", baseTag, debugDir);

        var integrationPath = stackDir + "/Integration_" + baseTag + outputExtension;
        win.saveAs(integrationPath, false, false, false, false);
        sum.integrationSave = {
            name: "Integration Save",
            status: "✅",
            details: "Integration saved"
        };

        closeAllWindowsExcept([win.mainView.id]);

        // Initialize path variables to null for safe cleanup (FIX #2)
        var baseline = null;
        var stretchedPath = null;

        try {
            // --- LINEAR PROCESSING ---

            // Functions return the active window reference, handling reverts (interactive or auto-failure) internally.
            win = finalABE(win, sum, baseTag, debugDir);
            win = autoBlackPoint(win, sum, baseTag, debugDir);
            win = runGradientCorrection(win, sum, baseTag, debugDir);
            win = runGraXpert(win, sum, baseTag, debugDir);
            win = autoBlackPoint(win, sum, baseTag, debugDir);

            // Step: BlurX Round Stars (if enabled)
            if (ai.deblur1) {
                win = deblur1(win, sum, baseTag, debugDir);
                if (saveCfg.deblur1 && sum.deblurV1.status === "✅") {
                    win.saveAs(finalDir + "/RoundStars_DeblurV1_" + baseTag + outputExtension, false, false, false, false);
                }
            }

            // Step: SPCC (Color calibration)
            var isColor = win.mainView.image.isColor;
            if (isColor) {
                // We attempt to solve and calibrate any color image.
                // If it was combined by this script (isRGBCombined=true), use "RGB" profile.
                // If the user provided a pre-stacked image (isRGBCombined=false), treat it as "OSC".
                var spccProfile = isRGBCombined ? "RGB" : "OSC";

                if (solveImage(win, sum, baseTag, debugDir)) {
                    performSPCC(win, sum, spccProfile, spccGraphs, baseTag, debugDir);
                } else {
                    sum.spcc = {
                        name: "SPCC",
                        status: "⏭️",
                        details: "Skipped (no WCS)"
                    };
                }
            } else {
                sum.solver = {
                    name: "ImageSolver",
                    status: "⏭️",
                    details: "Skipped (mono/NB)"
                };
                sum.spcc = {
                    name: "SPCC",
                    status: "⏭️",
                    details: "Skipped (mono/NB)"
                };
            }

            // Step: BlurX Enhanced
            if (ai.deblur2) {
                win = deblur2(win, sum, baseTag, debugDir);
                if (saveCfg.deblur2 && sum.deblurV2.status === "✅") {
                    win.saveAs(finalDir + "/Enhance_DeblurV2_" + baseTag + outputExtension, false, false, false, false);
                }
            }

            closeAllWindowsExcept([win.mainView.id]);
            baseline = finalDir + "/Final_Stacked_" + baseTag + outputExtension;
            win.saveAs(baseline, false, false, false, false);
            sum.baselineSave = {
                name: "Baseline Save",
                status: "✅",
                details: "Baseline saved"
            };
            debugSave(win, "Baseline_Linear", baseTag, debugDir);


            // --- STRETCHED PROCESSING ---

            // Step: Nuclear Screen Stretch
            stretchedPath = finalDir + "/Stretched_" + baseTag + outputExtension;
            if (ai.stretch) {
                // We handle the stretch manually because the interactive review logic differs slightly (no STF preview needed).
                try {
                    Console.writeln("\n=== Applying Nuclear Stretch ===");
                    applyPerfectNuclearStretch(win.mainView);
                    win.saveAs(stretchedPath, false, false, false, false);
                    debugSave(win, "After_Nuclear_Stretch", baseTag, debugDir);
                    sum.stretch = {
                        name: "Nuclear Stretch",
                        status: "✅",
                        details: "Applied with histogram transformation"
                    };

                    var executedStepNumber = debugStepCounter - 1;

                    if (INTERACTIVE_MODE) {
                        win.show();
                        win.bringToFront();
                        win.zoomToFit();
                        processEvents();
                        msleep(1000);

                        var decision = askAcceptStep("Nuclear Stretch",
                            "Nuclear stretch has been applied.",
                            executedStepNumber);

                        if (decision === "skip") {
                            // Reverting a stretch means going back to the linear baseline.
                            var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                            if (restoredWin) {
                                win = restoredWin;
                                sum.stretch.status = "⏭️";
                                sum.stretch.details = "Skipped by user - reverted to linear";
                                Console.writeln("⏭️ Nuclear Stretch skipped - reverted to linear");
                                stretchedPath = null; // Image is no longer stretched
                            } else {
                                Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                                throw new Error(ABORT_PROCESSING_IMAGE);
                            }
                        }
                    }
                } catch (e) {
                    if (e.message.startsWith("ABORT_")) throw e;
                    // Stretch failure handling
                    Console.writeln("❌ Stretch FAILED: " + e.message);
                    sum.stretch = {
                        name: "Nuclear Stretch",
                        status: "❌",
                        details: e.message
                    };
                }
            } else {
                sum.stretch = {
                    name: "Nuclear Stretch",
                    status: "⏭️",
                    details: "Disabled in settings"
                };
            }

            if (saveCfg.final_stretched && sum.stretch && sum.stretch.status === "✅") {
                saveAs16BitTiff_Photoshop(win, finalDir + "/Final_Stretched_" + baseTag);
                Console.writeln("  Saved Final (stretched, with stars): Final_Stretched_" + baseTag + ".tif");
            }

            // Step: NoiseX (STRETCHED or LINEAR depending on previous steps)
            if (ai.denoise) {
                win = denoise(win, sum, baseTag, debugDir);
                if (saveCfg.denoised && sum.denoising.status === "✅") {
                    var denoiseOutPath = finalDir + "/Denoised_" + baseTag + outputExtension;
                    win.saveAs(denoiseOutPath, false, false, false, false);
                }
            }

            // Step: StarX
            if (ai.starless) {
                var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
                if (needAny) {
                    // Star Separation requires custom handling because it generates multiple outputs (stars/starless)
                    win = starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir);
                } else {
                    sum.starSeparation = {
                        name: "Star Separation",
                        status: "⏭️",
                        details: "No star/starless outputs requested"
                    };
                }
            }

        } catch (e) {
            if (e.message === ABORT_PROCESSING) {
                Console.writeln("🛑 Processing stopped by user (Batch Abort)");
                Console.writeln("📁 Partial results available in: " + debugDir);
                throw e; // Propagate batch abort to the main run loop
            } else if (e.message === ABORT_PROCESSING_IMAGE) {
                Console.writeln("🛑 Processing stopped for this image due to critical failure or failed revert.");
                Console.writeln("📁 Partial results available in: " + debugDir);
                // Continue to cleanup phase
            } else {
                Console.criticalln("💥 Unhandled error during processOne: " + e.message);
                throw e; // Re-throw unexpected errors
            }
        }

        // ############ FIX #2: Safe Cleanup ############
        // Final cleanup and summary
        // BUGFIX: Added checks (e.g., && baseline) to ensure variables are not null before use.
        // Note: The original logic used 'true' for the 'keep' parameter in removeIf, meaning these files weren't actually deleted.
        // We preserve this behavior but make it safe against null paths.
        if (!saveCfg.baseline_linear && baseline) removeIf(baseline, true);
        if (!saveCfg.integration_linear && integrationPath) removeIf(integrationPath, true);

        if (!saveCfg.final_stretched && !saveCfg.denoised && stretchedPath) {
            try {
                // This prevents the crash when stretchedPath is null.
                removeIf(File.changeExtension(stretchedPath, ".tif"), true);
            } catch (e) {
                // Should only happen if stretchedPath is somehow not a valid string despite the check
                Console.writeln("Warning during cleanup (stretchedPath): " + e.message);
            }
        }

        // Return the final state and summary for collection
        return {
            win: win,
            summary: sum,
            baseTag: baseTag
        };
    }

    // -------------------- GUI --------------------
    function showGUI(state) {
        var dlg = new Dialog;
        dlg.windowTitle = "Post-Integration Pipeline (RGB & Mono) - V7 Robust SaveAs Fix";
        dlg.sizer = new VerticalSizer;
        dlg.sizer.margin = 10;
        dlg.sizer.spacing = 8;

        var head = new Label(dlg);
        head.useRichText = true;
        head.text = "<b>Utah Masterclass — Post-Integration Pipeline (V7 Robust SaveAs Fix)</b>";
        head.textAlignment = TextAlign_Center;
        dlg.sizer.add(head);

        var gTop = new GroupBox(dlg);
        gTop.title = "General";
        gTop.sizer = new VerticalSizer;
        gTop.sizer.margin = 8;
        gTop.sizer.spacing = 6;

        var rowIn = new HorizontalSizer;
        rowIn.spacing = 6;
        var labelIn = new Label(dlg);
        labelIn.text = "Input Directory:";
        labelIn.textAlignment = TextAlign_Right | TextAlign_VertCenter;
        var editIn = new Edit(dlg);
        editIn.readOnly = true;
        editIn.minWidth = 560;
        editIn.text = state.inputDir;
        var btnIn = new PushButton(dlg);
        btnIn.text = "Browse...";
        btnIn.icon = dlg.scaledResource(":/icons/select-file.png");
        btnIn.onClick = function() {
            var d = new GetDirectoryDialog;
            d.caption = "Select Input Directory";
            d.initialDirectory = state.inputDir || "";
            if (d.execute()) {
                state.inputDir = fwd(d.directory).replace(/\/$/, "");
                editIn.text = state.inputDir;
            }
        };
        rowIn.add(labelIn);
        rowIn.add(editIn, 100);
        rowIn.add(btnIn);
        gTop.sizer.add(rowIn);

        var rowOut = new HorizontalSizer;
        rowOut.spacing = 6;
        var labelOut = new Label(dlg);
        labelOut.text = "Output Directory:";
        labelOut.textAlignment = TextAlign_Right | TextAlign_VertCenter;
        var editOut = new Edit(dlg);
        editOut.readOnly = true;
        editOut.minWidth = 560;
        editOut.text = state.outputDir;
        var btnOut = new PushButton(dlg);
        btnOut.text = "Browse...";
        btnOut.icon = dlg.scaledResource(":/icons/select-file.png");
        btnOut.onClick = function() {
            var d = new GetDirectoryDialog;
            d.caption = "Select Output Base Directory";
            d.initialDirectory = state.outputDir || "";
            if (d.execute()) {
                state.outputDir = fwd(d.directory).replace(/\/$/, "");
                editOut.text = state.outputDir;
            }
        };
        rowOut.add(labelOut);
        rowOut.add(editOut, 100);
        rowOut.add(btnOut);
        gTop.sizer.add(rowOut);
        dlg.sizer.add(gTop);

        var gAI = new GroupBox(dlg);
        gAI.title = "AI / Processing Steps";
        gAI.sizer = new VerticalSizer;
        gAI.sizer.margin = 8;
        gAI.sizer.spacing = 6;

        var rowAI1 = new HorizontalSizer;
        rowAI1.spacing = 12;
        var cbD1 = new CheckBox(dlg);
        cbD1.text = "✨ Deblur V1 (round stars)";
        cbD1.checked = state.ai.deblur1;
        var cbD2 = new CheckBox(dlg);
        cbD2.text = "✨ Deblur V2 (enhance)";
        cbD2.checked = state.ai.deblur2;
        var cbST = new CheckBox(dlg);
        cbST.text = "🌀 Stretch (AutoSTF→HT)";
        cbST.checked = state.ai.stretch;
        var cbDN = new CheckBox(dlg);
        cbDN.text = "🧼 Denoise";
        cbDN.checked = state.ai.denoise;
        var cbSL = new CheckBox(dlg);
        cbSL.text = "✂️ Enable StarX (for starless/stars)";
        cbSL.checked = state.ai.starless;
        rowAI1.add(cbD1);
        rowAI1.add(cbD2);
        rowAI1.add(cbST);
        rowAI1.add(cbDN);
        rowAI1.add(cbSL);
        rowAI1.addStretch();
        gAI.sizer.add(rowAI1);

        var rowAI2 = new HorizontalSizer;
        rowAI2.spacing = 12;
        var cbSPCCGraph = new CheckBox(dlg);
        cbSPCCGraph.text = "📊 Show SPCC Graphs (Advanced)";
        cbSPCCGraph.checked = state.spccGraphs;
        rowAI2.add(cbSPCCGraph);
        rowAI2.addStretch();
        gAI.sizer.add(rowAI2);
        dlg.sizer.add(gAI);

        var gRGB = new GroupBox(dlg);
        gRGB.title = "RGB Outputs";
        gRGB.sizer = new VerticalSizer;
        gRGB.sizer.margin = 8;
        gRGB.sizer.spacing = 6;

        var rowRGBMaster = new HorizontalSizer;
        rowRGBMaster.spacing = 10;
        var chkProcRGB = new CheckBox(gRGB);
        chkProcRGB.text = "✅ Process RGB / Color";
        chkProcRGB.checked = state.processRGB;
        chkProcRGB.toolTip = "Enable or disable all RGB/Color processing and outputs.";
        rowRGBMaster.add(chkProcRGB);
        rowRGBMaster.addStretch();
        gRGB.sizer.add(rowRGBMaster);

        var cbCombine = new CheckBox(dlg);
        cbCombine.text = "Auto-detect & combine R+G+B masters";
        cbCombine.checked = state.combineRGB;
        gRGB.sizer.add(cbCombine);

        var rowR1 = new HorizontalSizer;
        rowR1.spacing = 10;
        var lR1 = new Label(dlg);
        lR1.text = "🏆 Finals:";
        lR1.minWidth = 120;
        var rFinalS = new CheckBox(dlg);
        rFinalS.text = "Final (stretched, with stars) (TIFF)";
        rFinalS.checked = state.save.rgb.final_stretched;
        var rFinalL = new CheckBox(dlg);
        rFinalL.text = "Final (linear, with stars)";
        rFinalL.checked = state.save.rgb.final_linear;
        rowR1.add(lR1);
        rowR1.add(rFinalS);
        rowR1.add(rFinalL);
        rowR1.addStretch();
        gRGB.sizer.add(rowR1);

        var rowR2 = new HorizontalSizer;
        rowR2.spacing = 10;
        var lR2 = new Label(dlg);
        lR2.text = "🎭 Masks & Starless:";
        lR2.minWidth = 120;
        var rStarsS = new CheckBox(dlg);
        rStarsS.text = "Stars (stretched mask) (TIFF)";
        rStarsS.checked = state.save.rgb.stars_stretched;
        var rgbEnh = new CheckBox(dlg);
        rgbEnh.text = "✨ Enhance with rich star colors";
        rgbEnh.checked = state.colorEnhanceRGBStarsStretched;
        var rSLessS = new CheckBox(dlg);
        rSLessS.text = "Starless (stretched) (TIFF)";
        rSLessS.checked = state.save.rgb.starless_stretched;
        var rSLessL = new CheckBox(dlg);
        rSLessL.text = "Starless (linear)";
        rSLessL.checked = state.save.rgb.starless_linear;
        rowR2.add(lR2);
        rowR2.add(rStarsS);
        rowR2.add(rgbEnh);
        rowR2.add(rSLessS);
        rowR2.add(rSLessL);
        rowR2.addStretch();
        gRGB.sizer.add(rowR2);
        dlg.sizer.add(gRGB);

        var gM = new GroupBox(dlg);
        gM.title = "Monochrome / Narrowband Outputs";
        gM.sizer = new VerticalSizer;
        gM.sizer.margin = 8;
        gM.sizer.spacing = 6;

        var rowMonoMaster = new HorizontalSizer;
        rowMonoMaster.spacing = 10;
        var chkProcMono = new CheckBox(gM);
        chkProcMono.text = "✅ Process Monochrome / Singles";
        chkProcMono.checked = state.processMonochrome;
        chkProcMono.toolTip = "Enable or disable all Monochrome/single-channel processing and outputs.";
        rowMonoMaster.add(chkProcMono);
        rowMonoMaster.addStretch();
        gM.sizer.add(rowMonoMaster);

        var rowM1 = new HorizontalSizer;
        rowM1.spacing = 10;
        var lM1 = new Label(dlg);
        lM1.text = "🏆 Finals:";
        lM1.minWidth = 120;
        var mFinalS = new CheckBox(dlg);
        mFinalS.text = "Final (stretched, with stars) (TIFF)";
        mFinalS.checked = state.save.mono.final_stretched;
        var mFinalL = new CheckBox(dlg);
        mFinalL.text = "Final (linear, with stars)";
        mFinalL.checked = state.save.mono.final_linear;
        rowM1.add(lM1);
        rowM1.add(mFinalS);
        rowM1.add(mFinalL);
        rowM1.addStretch();
        gM.sizer.add(rowM1);

        var rowM2 = new HorizontalSizer;
        rowM2.spacing = 10;
        var lM2 = new Label(dlg);
        lM2.text = "🎭 Starless & Masks:";
        lM2.minWidth = 120;
        var mStarsS = new CheckBox(dlg);
        mStarsS.text = "Stars (stretched mask) (TIFF)";
        mStarsS.checked = state.save.mono.stars_stretched;
        var mSLessS = new CheckBox(dlg);
        mSLessS.text = "Starless (stretched) (TIFF)";
        mSLessS.checked = state.save.mono.starless_stretched;
        var mSLessL = new CheckBox(dlg);
        mSLessL.text = "Starless (linear)";
        mSLessL.checked = state.save.mono.starless_linear;
        rowM2.add(lM2);
        rowM2.add(mStarsS);
        rowM2.add(mSLessS);
        rowM2.add(mSLessL);
        rowM2.addStretch();
        gM.sizer.add(rowM2);
        dlg.sizer.add(gM);

        var rgbControls = [cbCombine, rFinalS, rFinalL, rStarsS, rgbEnh, rSLessS, rSLessL];
        var monoControls = [mFinalS, mFinalL, mStarsS, mSLessS, mSLessL];

        function toggleSection(enabled, controls) {
            for (var i = 0; i < controls.length; ++i) controls[i].enabled = enabled;
        }

        chkProcRGB.onCheck = function(checked) {
            toggleSection(checked, rgbControls);
        };

        chkProcMono.onCheck = function(checked) {
            toggleSection(checked, monoControls);
        };

        toggleSection(chkProcRGB.checked, rgbControls);
        toggleSection(chkProcMono.checked, monoControls);

        var rowBtn = new HorizontalSizer;
        rowBtn.spacing = 8;
        rowBtn.addStretch();
        var bStart = new PushButton(dlg);
        bStart.text = "Start";
        bStart.icon = dlg.scaledResource(":/icons/ok.png");
        bStart.defaultButton = true;
        var bCancel = new PushButton(dlg);
        bCancel.text = "Cancel";
        bCancel.icon = dlg.scaledResource(":/icons/close.png");
        rowBtn.add(bStart);
        rowBtn.add(bCancel);
        dlg.sizer.add(rowBtn);

        bCancel.onClick = function() {
            dlg.cancel();
        };
        bStart.onClick = function() {
            if (!state.inputDir || !File.directoryExists(state.inputDir)) {
                (new MessageBox("Input directory does not exist:\n" + (state.inputDir || "<empty>"), "Error", StdIcon_Error, StdButton_Ok)).execute();
                return;
            }
            if (!state.outputDir || !File.directoryExists(state.outputDir)) {
                (new MessageBox("Output base directory does not exist:\n" + (state.outputDir || "<empty>"), "Error", StdIcon_Error, StdButton_Ok)).execute();
                return;
            }
            state.processRGB = chkProcRGB.checked;
            state.processMonochrome = chkProcMono.checked;
            state.combineRGB = cbCombine.checked;
            state.ai.deblur1 = cbD1.checked;
            state.ai.deblur2 = cbD2.checked;
            state.ai.stretch = cbST.checked;
            state.ai.denoise = cbDN.checked;
            state.ai.starless = cbSL.checked;
            state.colorEnhanceRGBStarsStretched = rgbEnh.checked;
            state.spccGraphs = cbSPCCGraph.checked;
            state.save.rgb = {
                final_stretched: rFinalS.checked,
                final_linear: rFinalL.checked,
                stars_stretched: rStarsS.checked,
                starless_stretched: rSLessS.checked,
                starless_linear: rSLessL.checked,
                integration_linear: false,
                baseline_linear: false,
                deblur1: false,
                deblur2: false,
                denoised: false
            };
            state.save.mono = {
                final_stretched: mFinalS.checked,
                final_linear: mFinalL.checked,
                stars_stretched: mStarsS.checked,
                starless_stretched: mSLessS.checked,
                starless_linear: mSLessL.checked,
                integration_linear: false,
                baseline_linear: false,
                deblur1: false,
                deblur2: false,
                denoised: false
            };
            dlg.ok();
        };

        return dlg.execute();
    }

    // -------------------- Entrypoint --------------------
    function run() {
        Console.show();
        Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — V7 Robust SaveAs Fix ===");

        var settingsKey = "PostIntegrationPipeline";
        var state = {
            inputDir: Settings.read(settingsKey + "/InputDir", DataType_String) || defaults.inputDir,
            outputDir: Settings.read(settingsKey + "/OutputDir", DataType_String) || defaults.outputDir,
            processRGB: defaults.processRGB,
            processMonochrome: defaults.processMonochrome,
            combineRGB: defaults.combineRGB,
            ai: {
                deblur1: defaults.ai.deblur1,
                deblur2: defaults.ai.deblur2,
                stretch: defaults.ai.stretch,
                denoise: defaults.ai.denoise,
                starless: defaults.ai.starless
            },
            colorEnhanceRGBStarsStretched: defaults.colorEnhanceRGBStarsStretched,
            spccGraphs: defaults.spccGraphs,
            save: {
                rgb: defaults.save.rgb,
                mono: defaults.save.mono
            }
        };

        if (!showGUI(state)) {
            Console.writeln("Cancelled.");
            return;
        }

        Settings.write(settingsKey + "/InputDir", DataType_String, state.inputDir);
        Settings.write(settingsKey + "/OutputDir", DataType_String, state.outputDir);

        Console.writeln("Input dir : " + state.inputDir);
        Console.writeln("Output dir: " + state.outputDir);

        var root = tsFolder(state.outputDir);
        Console.writeln("Output folder: " + root);
        ensureDir(root + "/5_stacked");
        ensureDir(root + "/6_final");

        // Collect summaries for final report
        var allSummaries = [];

        try {
            var plan = buildWorkPlan(state.inputDir, state.combineRGB);
            var doRGB = state.processRGB && plan.doRGB && shouldProcessConfig(state.save.rgb);

            if (!state.processRGB && !state.processMonochrome) {
                Console.writeln("Both RGB and Monochrome processing are disabled. Exiting.");
                return;
            }

            if (doRGB) {
                Console.writeln("\n→ Building RGB from:");
                Console.writeln("    R: " + File.extractName(plan.r));
                Console.writeln("    G: " + File.extractName(plan.g));
                Console.writeln("    B: " + File.extractName(plan.b));
                var combo = combineRGB(plan.r, plan.g, plan.b, root);
                var result = processOne(combo.window, combo.base, root, state.ai, state.save.rgb, true, state.colorEnhanceRGBStarsStretched, false, state.spccGraphs);
                allSummaries.push({
                    summary: result.summary,
                    baseTag: result.baseTag
                });
                try {
                    if (result.win && result.win.isWindow) result.win.forceClose();
                } catch (_) {}
            } else {
                Console.writeln("RGB Combination: skipped (no R+G+B set found or option disabled).");
            }

            if (plan.singles.length > 0) {
                Console.writeln("\n→ Processing mono/narrowband/color singles: " + plan.singles.length);
                for (var i = 0; i < plan.singles.length; ++i) {
                    var singleInfo = plan.singles[i];
                    var p = singleInfo.path;
                    var tag = singleInfo.tag;
                    var isStackedRGB = singleInfo.isStackedRGB;
                    Console.writeln("\n— [" + (i + 1) + "/" + plan.singles.length + "] " + File.extractName(p) + "  [" + tag + "]");
                    var w = ImageWindow.open(p);
                    if (w.length === 0) {
                        Console.writeln("  ⚠️ Could not open, skipping.");
                        continue;
                    }
                    var win = w[0],
                        base = tag + "_" + sanitizeBase(File.extractName(p));
                    var isColor = win.mainView.image.isColor;
                    var saveCfg = isColor ? state.save.rgb : state.save.mono;
                    var procEnabled = isColor ? state.processRGB : state.processMonochrome;
                    if (!procEnabled || !shouldProcessConfig(saveCfg)) {
                        Console.writeln("  ⏭️ Skipped (processing or outputs disabled for this image type).");
                        try {
                            win.forceClose();
                        } catch (_) {}
                        continue;
                    }
                    var result = processOne(win, base, root, state.ai, saveCfg, false, isColor && state.colorEnhanceRGBStarsStretched, isStackedRGB, state.spccGraphs);
                    allSummaries.push({
                        summary: result.summary,
                        baseTag: result.baseTag
                    });
                    try {
                        if (result.win && result.win.isWindow) result.win.forceClose();
                    } catch (_) {}
                    closeAllWindowsExcept(null);
                }
            } else {
                Console.writeln("\nNo single channel (Mono/NB/Color) masters found to process.");
            }

            // Print all summaries at the end
            Console.writeln("\n" + "=".repeat(80));
            Console.writeln("FINAL PROCESSING SUMMARY");
            Console.writeln("=".repeat(80));

            for (var s = 0; s < allSummaries.length; s++) {
                var summaryData = allSummaries[s];
                Console.writeln("\nSummary: " + summaryData.baseTag + " —");

                // Clean order - removed integrationSave and baselineSave, added starColorsEnhanced
                var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "stretch", "denoising", "starSeparation", "starColorsEnhanced"];

                for (var i = 0; i < order.length; ++i) {
                    var k = order[i];
                    if (summaryData.summary[k]) {
                        var it = summaryData.summary[k];
                        Console.writeln("  " + it.status + " " + it.name + ": " + it.details);
                    }
                }
            }

            Console.writeln("\n=== Done. Output: " + root + " ===");
        } catch (err) {
            if (err.message === ABORT_PROCESSING) {
                Console.writeln("\n=== Batch Processing Aborted by User ===");
            } else {
                Console.criticalln("Error: " + err.message);
                if (DEBUG_MODE) {
                    (new MessageBox(err.message, "Script Aborted", StdIcon_Error, StdButton_Ok)).execute();
                } else {
                    throw err;
                }
            }
        }
    }

    run();
})(); // IIFE
