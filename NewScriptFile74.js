// =================================================================
// PixInsight Script - Combine RGB Channels from Monochrome XISF Files
// Detects channels by filename pattern
// Works with modern PixInsight and correct data types
// =================================================================

#include <pjsr/DataType.jsh>

function getChannelFromFilename(filePath) {
    var filename = File.extractName(filePath).toLowerCase();

    if (filename.indexOf("filter-red") !== -1 || filename.indexOf("filter-r") !== -1)
        return "R";
    if (filename.indexOf("filter-green") !== -1 || filename.indexOf("filter-g") !== -1)
        return "G";
    if (filename.indexOf("filter-blue") !== -1 || filename.indexOf("filter-b") !== -1)
        return "B";

    return "";
}

function loadImageByChannel(folder, targetChannel) {
    var ff = new FileFind;
    if (!ff.begin(folder + "/*.xisf")) {
        console.criticalln("❌ No .xisf files found in: " + folder);
        return null;
    }

    do {
        var filePath = folder + "/" + ff.name;
        var channel = getChannelFromFilename(filePath);
        console.writeln("🔍 Inspecting file: " + filePath + " | Detected channel: '" + channel + "'");

        if (channel === targetChannel) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0 && windowArray[0].mainView) {
                console.writeln("✅ Loaded '" + channel + "' channel image.");
                return windowArray[0];
            } else {
                console.warningln("⚠️ Failed to load image from: " + filePath);
            }
        }
    } while (ff.next());
    ff.end();

    console.criticalln("🚫 No matching image found for channel: " + targetChannel);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("🚀 Loading R, G, and B channel images...");

    var redWindow = loadImageByChannel(inputDir, "R");
    if (!redWindow) return;

    var greenWindow = loadImageByChannel(inputDir, "G");
    if (!greenWindow) return;

    var blueWindow = loadImageByChannel(inputDir, "B");
    if (!blueWindow) return;

    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    if (!redView || !greenView || !blueView) {
        console.criticalln("❌ One or more views are invalid or inaccessible.");
        return;
    }

    // Create output RGB image with proper format
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,                      // 3 channels = RGB
        DataType_Float32,       // High dynamic range
        true,                   // isColor = true
        false,                  // not temporary
        "RGB_Combined"          // window ID
    );

    var outputView = outputImage.mainView;

    // Combine channels
    var combo = new ChannelCombination;
    combo.channels = [
        [redView.id],
        [greenView.id],
        [blueView.id]
    ];

    combo.executeOn(outputView, false); // false -> do not replace image

    outputImage.show();

    console.writeln("🌈 RGB channels successfully merged into a color image!");
}

main();
