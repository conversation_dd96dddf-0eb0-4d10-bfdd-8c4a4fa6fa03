// =================================================================
// PixInsight Script - Combine RGB Channels from Monochrome XISF Files
// Works even on older PixInsight versions (fallback enum values used)
// =================================================================

function getChannelFromFilename(filePath) {
    var filename = File.extractName(filePath).toLowerCase();

    if (filename.indexOf("filter-red") !== -1 || filename.indexOf("filter-r") !== -1)
        return "R";
    if (filename.indexOf("filter-green") !== -1 || filename.indexOf("filter-g") !== -1)
        return "G";
    if (filename.indexOf("filter-blue") !== -1 || filename.indexOf("filter-b") !== -1)
        return "B";

    return "";
}

function loadImageByChannel(folder, targetChannel) {
    var ff = new FileFind;
    if (!ff.begin(folder + "/*.xisf")) {
        console.criticalln("❌ No .xisf files found in: " + folder);
        return null;
    }

    do {
        var filePath = folder + "/" + ff.name;
        var channel = getChannelFromFilename(filePath);
        console.writeln("🔍 Inspecting file: " + filePath + " | Detected channel: '" + channel + "'");

        if (channel === targetChannel) {
            var windowArray = ImageWindow.open(filePath, "");
            if (windowArray && windowArray.length > 0 && windowArray[0].mainView) {
                console.writeln("✅ Loaded '" + channel + "' channel image.");
                return windowArray[0];
            } else {
                console.warningln("⚠️ Failed to load image from: " + filePath);
            }
        }
    } while (ff.next());
    ff.end();

    console.criticalln("🚫 No matching image found for channel: " + targetChannel);
    return null;
}

function main() {
    var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";

    console.writeln("🚀 Loading R, G, and B channel images...");

    var redWindow = loadImageByChannel(inputDir, "R");
    if (!redWindow) return;

    var greenWindow = loadImageByChannel(inputDir, "G");
    if (!greenWindow) return;

    var blueWindow = loadImageByChannel(inputDir, "B");
    if (!blueWindow) return;

    var redView = redWindow.mainView;
    var greenView = greenWindow.mainView;
    var blueView = blueWindow.mainView;

    if (!redView || !greenView || !blueView) {
        console.criticalln("❌ One or more views are invalid.");
        return;
    }

    // Explicitly use Float32 data type via numeric enum (safe fallback)
    var outputImage = new ImageWindow(
        redView.image.width,
        redView.image.height,
        3,
        4,              // 4 = Float32 bit depth
        true,           // isColor
        false,          // canDelete
        "RGB_Combined"  // window ID
    );

    var outputView = outputImage.mainView;

    // Channel combine using identifiers (safe fallback)
    var combo = new ChannelCombination;
    combo.channels = [
        [redView.id],
        [greenView.id],
        [blueView.id]
    ];

    combo.executeOn(outputView, false);

    outputImage.show();

    console.writeln("🌈 RGB image successfully created!");
}

main();
