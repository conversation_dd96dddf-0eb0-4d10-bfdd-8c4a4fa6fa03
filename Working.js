/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * FINAL VERSION
 * - Uses an absolute path for the GraXpert library to ensure it is found.
 * - Uses a direct library call to GraXpert for maximum reliability.
 * - Enhanced with additional debug points after SPCC
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- UPDATED: Using the absolute path to the GraXpert library ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Debugger Control --------------------
var DEBUG_MODE = true;
var debugStepCounter = 1;

// ... (The rest of the script is the same) ...
var STF_UNLINKED        = true;
var STF_K_SHADOWS       = -3.0;
var STF_TARGET_BG       = 0.22;
var STF_HIGHLIGHTS      = 1.0;
var STF_MAX_SAMPLES     = 120000;
var outputExtension = ".xisf";

// -------------------- Defaults (empty paths for cross-OS) --------------------
var defaults = {
  inputDir:  "",
  outputDir: "",
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}

function debugPause(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Pause Warning: Invalid window provided for step '", stepName, "'");
        return;
    }
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let fileName = counterStr + "_" + baseTag + "_" + sanitizedStepName + ".fit";
    let filePath = debugDir + "/" + fileName;
    debugStepCounter++;
    Console.writeln("DEBUG: Saving intermediate file: ", filePath);
    win.saveAs(filePath, false, false, false, true);
    var msgBox = new MessageBox( "<h2>Debug Pause</h2><p><b>Paused after:</b> " + stepName + "</p><p>A snapshot has been saved as a FITS file in the 'debug' subfolder:</p><p><b>" + fileName + "</b></p><p>Click <b>Continue</b> to proceed, or <b>Stop</b> to abort the script.</p>", "Debug Pause", StdIcon_Information, StdButton_Ok, StdButton_Cancel );
    msgBox.okButtonText = "Continue";
    msgBox.cancelButtonText = "Stop";
    if (msgBox.execute() == StdButton_Cancel) {
        throw new Error("Script execution aborted by user after '" + stepName + "'.");
    }
}

function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents();
    }
}

function findAllInputImages(dir){
  var files=[];
  function scan(d){
    var list=File.directoryEntries(d,false);
    for(var i=0;i<list.length;++i){
      var full=d+"/"+list[i];
      if(File.isDirectory(full)){ scan(full); }
      else{
        var ext=File.extractExtension(full).toLowerCase();
        if(ext===".xisf"||ext===".fits"||ext===".fit") files.push(full);
      }
    }
  }
  scan(dir); return files;
}

function detectFilterFromName(name){
  var n=name.toLowerCase();
  if(n.indexOf("_r_")!==-1||n.indexOf("red")!==-1) return "R";
  if(n.indexOf("_g_")!==-1||n.indexOf("green")!==-1) return "G";
  if(n.indexOf("_b_")!==-1||n.indexOf("blue")!==-1) return "B";
  if(n.indexOf("_l_")!==-1||n.indexOf("lum")!==-1) return "L";
  if(n.indexOf("ha")!==-1||n.indexOf("h_alpha")!==-1) return "Ha";
  if(n.indexOf("oiii")!==-1||n.indexOf("o3")!==-1) return "OIII";
  if(n.indexOf("sii")!==-1||n.indexOf("s2")!==-1) return "SII";
  return null;
}

function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  var plan = { doRGB:false, r:null, g:null, b:null, others:[] };
  if(combineRGB && byFilter.R && byFilter.G && byFilter.B){
    plan.doRGB=true; plan.r=byFilter.R[0]; plan.g=byFilter.G[0]; plan.b=byFilter.B[0];
    delete byFilter.R; delete byFilter.G; delete byFilter.B;
  }
  for(var k in byFilter){ if(byFilter[k].length>0) plan.others.push({filter:k, path:byFilter[k][0]}); }
  for(var i=0;i<unknownSingles.length;++i) plan.others.push({filter:"Unknown", path:unknownSingles[i]});
  return plan;
}

function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var base=sanitizeBase("RGB_Combined");
  return {window:rgb, base:base};
}

function finalABE(win, sum){
  try{
    Console.writeln("\n=== Running Automatic Background Extractor (ABE) ===");
    var P = new AutomaticBackgroundExtractor;
    P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050; P.maxBackground = 1.0000; P.minBackground = 0.0000; P.useBrightnessLimits = false; P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5; P.modelImageSampleFormat = AutomaticBackgroundExtractor.prototype.f32; P.abeDownsample = 2.00; P.writeSampleBoxes = false; P.justTrySamples = false;
    P.targetCorrection = AutomaticBackgroundExtractor.prototype.Subtraction; P.normalize = true; P.replaceTarget = true; P.discardModel = true;
    P.correctedImageId = ""; P.correctedImageSampleFormat = AutomaticBackgroundExtractor.prototype.SameAsTarget; P.verboseCoefficients = false; P.compareModel = false; P.compareFactor = 10.00;
    P.executeOn(win.mainView);
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE with custom settings applied"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}

function autoBlackPoint(win, sum){
  try{
    var img=win.mainView.image;
    var n=img.numberOfChannels;
    var samples=[];
    for(var c=0;c<n;++c){
      var vals=[];
      img.selectedChannel=c;
      for(var y=0;y<img.height;y+=10){
        for(var x=0;x<img.width;x+=10){
          vals.push(img.sample(x,y));
          if(vals.length>=10000) break;
        }
        if(vals.length>=10000) break;
      }
      vals.sort(function(a,b){return a-b;});
      var p5=vals[Math.floor(vals.length*0.05)];
      samples.push(p5);
    }
    var HT=new HistogramTransformation;
    if(n===1){ HT.H=[[0,0.5,1,samples[0],1]]; }
    else{ HT.H=[[0,0.5,1,samples[0],1],[0,0.5,1,samples[1],1],[0,0.5,1,samples[2],1],[0,0.5,1,0,1]]; }
    HT.executeOn(win.mainView);
    sum.blackPoint={name:"Auto Black Point",status:"✅",details:"Applied"};
  }catch(e){ sum.blackPoint={name:"Auto Black Point",status:"❌",details:e.message}; }
}

function runGraXpert(win, sum) {
    try {
        Console.writeln("\n=== Running GraXpert via Library Call ===");
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0;
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = win.mainView;
        gxp.process();
        sum.graxpert = {name: "GraXpert", status: "✅", details: "Applied via library call"};
    } catch (e) {
        sum.graxpert = {name: "GraXpert", status: "❌", details: e.message};
    }
}

function solveImage(win, sum){
  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    var solver=new ImageSolver;
    solver.centerRA=0; solver.centerDec=0; solver.resolution=0; solver.focalLength=0; solver.pixelSize=0;
    solver.catalogMode=ImageSolver.prototype.GAIA_DR3; solver.magnitude=16; solver.autoMagnitude=true;
    solver.solverCatalogMode=ImageSolver.prototype.GAIA_DR3; solver.solverAutoCatalog=true; solver.solverLimitMagnitude=16.0;
    solver.distortionCorrection=true; solver.splineSmoothing=0.025; solver.enableSimplifyDistortion=false; solver.simplifyTolerance=0.25;
    solver.generateErrorImg=false; solver.generateDistortMap=false; solver.useDistortionCorrection=true; solver.onlyOptimize=false;
    solver.showStars=false; solver.showDistortion=false; solver.generateTextFiles=false; solver.textFormat=ImageSolver.prototype.CSV;
    solver.executeOn(win.mainView);
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}

function performSPCC(win, sum, profileSelector){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();
    try{ P.generateGraphs=false; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied ("+profileSelector+")"};
    return true;
  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}

function deblur1(win, sum){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
    P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV1={name:"Deblur V1",status:"✅",details:"Round stars"};
    return true;
  }catch(e){ sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message}; return false; }
}

function deblur2(win, sum){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
    P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};
    return true;
  }catch(e){ sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message}; return false; }
}

function denoise(win, sum){
  try{
    var P=new NoiseXTerminator();
    P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
    P.executeOn(win.mainView);
    sum.denoising={name:"Denoising",status:"✅",details:"Applied"};
    return true;
  }catch(e){ sum.denoising={name:"Denoising",status:"❌",details:e.message}; return false; }
}

function clamp(x,a,b){ return x<a?a:(x>b?b:x); }
function median(v){ var n=v.length; if(!n) return 0; v.sort(function(a,b){return a-b;}); return (n&1)? v[(n-1)>>1] : 0.5*(v[n>>1]+v[(n>>1)-1]); }
function mad(v, med){ var a=new Array(v.length); for (var i=0;i<v.length;++i) a[i]=Math.abs(v[i]-med); return median(a); }

function stretchImageAtStage(inputPath, outputPath, saveFlag){
  var w=ImageWindow.open(inputPath)[0];
  if(!w) return null;
  try{
    var img=w.mainView.image;
    var n=img.numberOfChannels;
    var shadows=[], highlights=[], midtones=[];
    for(var c=0;c<n;++c){
      img.selectedChannel=c;
      var vals=[];
      for(var y=0;y<img.height;y+=Math.max(1,Math.floor(img.height/100))){
        for(var x=0;x<img.width;x+=Math.max(1,Math.floor(img.width/100))){
          vals.push(img.sample(x,y));
          if(vals.length>=STF_MAX_SAMPLES) break;
        }
        if(vals.length>=STF_MAX_SAMPLES) break;
      }
      var med=median(vals), madVal=mad(vals,med);
      var c0=Math.max(0,med+STF_K_SHADOWS*madVal);
      shadows.push(c0); highlights.push(STF_HIGHLIGHTS); midtones.push(STF_TARGET_BG);
    }
    var STF=new ScreenTransferFunction;
    for(var c=0;c<n;++c){ STF.STF[c]=[shadows[c],midtones[c],highlights[c],0,1]; }
    STF.executeOn(w.mainView);
    var HT=new HistogramTransformation;
    HT.H=STF.STF; HT.executeOn(w.mainView);
    if(saveFlag) w.saveAs(outputPath+outputExtension,false,false,false,false);
    return w;
  }catch(e){ try{w.forceClose();}catch(_){} return null; }
}

function starSeparation(win, sum, saveCfg, finalDir, baseTag){
  try{
    var P=new StarXTerminator(); P.generateStarImage=true; P.unscreenStars=true; P.largeOverlap=false;
    P.executeOn(win.mainView);
    var starsWin=null, wins=ImageWindow.windows;
    for(var i=wins.length-1;i>=0;--i){
      var id=wins[i].mainView.id;
      if(id.indexOf("stars")!==-1 || id.indexOf("Stars")!==-1){ starsWin=wins[i]; break; }
    }
    if(starsWin && saveCfg.stars_stretched)
      starsWin.saveAs(finalDir+"/Stars_"+baseTag+outputExtension,false,false,false,false);
    if(saveCfg.starless_stretched)
      win.saveAs(finalDir+"/Starless_"+baseTag+outputExtension,false,false,false,false);
    if(starsWin) try{ starsWin.forceClose(); }catch(_){}
    sum.starSeparation={name:"Star Separation",status:"✅",details:"StarX applied"};
    return true;
  }catch(e){ sum.starSeparation={name:"Star Separation",status:"❌",details:e.message}; return false; }
}

function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceStars){
  var sum={};
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);
  var debugDir = rootOut + "/debug";
  if (DEBUG_MODE) ensureDir(debugDir);
  debugStepCounter = 1;

  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};
  closeAllWindowsExcept([win.mainView.id]);

  // Linear steps
  finalABE(win,sum);
  debugPause(win, "Background Extraction ABE", baseTag, debugDir);
  closeAllWindowsExcept([win.mainView.id]);

  autoBlackPoint(win,sum);
  debugPause(win, "Auto Black Point", baseTag, debugDir);
  closeAllWindowsExcept([win.mainView.id]);

  runGraXpert(win, sum);
  debugPause(win, "Gradient Removal GraXpert", baseTag, debugDir);
  closeAllWindowsExcept([win.mainView.id]);

  // ----- Solve + SPCC on COLOR only -----
  var isColor = win.mainView.image.isColor;
  if(isColor){
    var solved = solveImage(win, sum);
    debugPause(win, "Image Solver WCS", baseTag, debugDir);
    closeAllWindowsExcept([win.mainView.id]);

    if(solved){
      var spccProfile = isRGBCombined ? "RGB" : "OSC";
      performSPCC(win, sum, spccProfile);
      debugPause(win, "Color Calibration SPCC", baseTag, debugDir);
      closeAllWindowsExcept([win.mainView.id]);
    } else {
      sum.spcc = {name:"SPCC",status:"⏭️",details:"Skipped (no WCS)"};
    }
  } else {
    sum.solver = {name:"ImageSolver",status:"⏭️",details:"Skipped (mono/NB)"};
    sum.spcc   = {name:"SPCC",status:"⏭️",details:"Skipped (mono/NB)"};
  }

  // Baseline (linear with stars)
  var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};
  debugPause(win, "Baseline Linear Save", baseTag, debugDir);

  // Deblur V1 (linear)
  if(ai.deblur1){
    var w1=ImageWindow.open(baseline)[0];
    if(w1 && deblur1(w1,sum)){
      debugPause(w1, "Deblur V1 Round Stars", baseTag, debugDir);
      if(saveCfg.deblur1) w1.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
    }
    try{ w1.forceClose(); }catch(_){}
  }

  // Deblur V2 (linear)
  if(ai.deblur2){
    var w2=ImageWindow.open(baseline)[0];
    if(w2 && deblur2(w2,sum)){
      debugPause(w2, "Deblur V2 Enhance", baseTag, debugDir);
      if(saveCfg.deblur2) w2.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
    }
    try{ w2.forceClose(); }catch(_){}
  }

  // Stretch (after Deblur V2)
  var stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
  if(ai.stretch){
    var wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/true);
    if(wS){
      debugPause(wS, "Stretch AutoSTF-HT", baseTag, debugDir);
      try{ wS.forceClose(); }catch(_){}
    }
  }

  // Denoise (prefer stretched if exists)
  if(ai.denoise){
    var dnIn = ai.stretch ? stretchedPath : baseline;
    var w3 = ImageWindow.open(dnIn)[0];
    if(w3 && denoise(w3,sum)){
      debugPause(w3, "Denoise NoiseX", baseTag, debugDir);
      if(saveCfg.denoised) w3.saveAs(finalDir+"/Denoised_"+baseTag+outputExtension,false,false,false,false);
    }
    try{ w3.forceClose(); }catch(_){}
  }

  // Star separation (on stretched if available)
  if(ai.starless){
    var starIn = ai.stretch ? stretchedPath : baseline;
    var w4 = ImageWindow.open(starIn)[0];
    if(w4){
      starSeparation(w4, sum, saveCfg, finalDir, baseTag);
      debugPause(w4, "Star Separation StarX", baseTag, debugDir);
      try{ w4.forceClose(); }catch(_){}
    }
  }

  // Clean up intermediates if user doesn't want them
  removeIf(integrationPath, saveCfg.integration_linear===true);
  removeIf(baseline,        saveCfg.baseline_linear===true);
  if(ai.stretch){
    var needStretchedFinal = saveCfg.final_stretched===true;
    if(!needStretchedFinal && saveCfg.denoised!==true)
      removeIf(stretchedPath, /*keep*/false);
  }
  if(!saveCfg.final_linear)
    removeIf(baseline, saveCfg.baseline_linear===true);

  // Summary
  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction","blackPoint","graxpert","solver","spcc","deblurV1","deblurV2","denoising","starSeparation","finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}

// -------------------- GUI --------------------
function createGUI(){
  var dlg=new Dialog();
  dlg.windowTitle="Post-Integration Pipeline";
  dlg.minWidth=500;

  var state={ inputDir:defaults.inputDir, outputDir:defaults.outputDir, combineRGB:defaults.combineRGB,
              ai:{ deblur1:defaults.ai.deblur1, deblur2:defaults.ai.deblur2, stretch:defaults.ai.stretch, denoise:defaults.ai.denoise, starless:defaults.ai.starless },
              save:{ rgb:defaults.save.rgb, mono:defaults.save.mono } };

  var mainSizer=new VerticalSizer(); dlg.sizer=mainSizer;

  // Input/Output
  var ioGroup=new GroupBox(dlg); ioGroup.title="Input/Output"; ioGroup.sizer=new VerticalSizer();
  var inputSizer=new HorizontalSizer();
  inputSizer.add(new Label(dlg)); inputSizer.lastControl.text="Input Directory:"; inputSizer.lastControl.minWidth=120; inputSizer.lastControl.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var inputEdit=new Edit(dlg); inputEdit.text=state.inputDir; inputEdit.minWidth=300; inputSizer.add(inputEdit);
  var inputBtn=new PushButton(dlg); inputBtn.text="Browse"; inputBtn.onClick=function(){ var d=new GetDirectoryDialog(); if(d.execute()) inputEdit.text=d.directory; }; inputSizer.add(inputBtn);
  ioGroup.sizer.add(inputSizer);

  var outputSizer=new HorizontalSizer();
  outputSizer.add(new Label(dlg)); outputSizer.lastControl.text="Output Directory:"; outputSizer.lastControl.minWidth=120; outputSizer.lastControl.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var outputEdit=new Edit(dlg); outputEdit.text=state.outputDir; outputEdit.minWidth=300; outputSizer.add(outputEdit);
  var outputBtn=new PushButton(dlg); outputBtn.text="Browse"; outputBtn.onClick=function(){ var d=new GetDirectoryDialog(); if(d.execute()) outputEdit.text=d.directory; }; outputSizer.add(outputBtn);
  ioGroup.sizer.add(outputSizer);

  var combineCheck=new CheckBox(dlg); combineCheck.text="Combine RGB (if R/G/B masters found)"; combineCheck.checked=state.combineRGB;
  ioGroup.sizer.add(combineCheck);
  mainSizer.add(ioGroup);

  // AI Processing
  var aiGroup=new GroupBox(dlg); aiGroup.title="AI Processing"; aiGroup.sizer=new VerticalSizer();
  var deblur1Check=new CheckBox(dlg); deblur1Check.text="Deblur V1 (Round Stars)"; deblur1Check.checked=state.ai.deblur1; aiGroup.sizer.add(deblur1Check);
  var deblur2Check=new CheckBox(dlg); deblur2Check.text="Deblur V2 (Enhance)"; deblur2Check.checked=state.ai.deblur2; aiGroup.sizer.add(deblur2Check);
  var stretchCheck=new CheckBox(dlg); stretchCheck.text="Stretch (AutoSTF→HT)"; stretchCheck.checked=state.ai.stretch; aiGroup.sizer.add(stretchCheck);
  var denoiseCheck=new CheckBox(dlg); denoiseCheck.text="Denoise"; denoiseCheck.checked=state.ai.denoise; aiGroup.sizer.add(denoiseCheck);
  var starlessCheck=new CheckBox(dlg); starlessCheck.text="Star Separation"; starlessCheck.checked=state.ai.starless; aiGroup.sizer.add(starlessCheck);
  mainSizer.add(aiGroup);

  // Buttons
  var btnSizer=new HorizontalSizer();
  btnSizer.addStretch();
  var okBtn=new PushButton(dlg); okBtn.text="Run"; okBtn.defaultButton=true; okBtn.onClick=function(){
    state.inputDir=inputEdit.text; state.outputDir=outputEdit.text; state.combineRGB=combineCheck.checked;
    state.ai.deblur1=deblur1Check.checked; state.ai.deblur2=deblur2Check.checked; state.ai.stretch=stretchCheck.checked; state.ai.denoise=denoiseCheck.checked; state.ai.starless=starlessCheck.checked;
    dlg.ok();
  }; btnSizer.add(okBtn);
  var cancelBtn=new PushButton(dlg); cancelBtn.text="Cancel"; cancelBtn.cancelButton=true; cancelBtn.onClick=function(){ dlg.cancel(); }; btnSizer.add(cancelBtn);
  mainSizer.add(btnSizer);

  if(dlg.execute()!==StdDialogCode_Ok) return null;
  return state;
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (Mixed Sets) — Enhanced Debug Version ===");

  var state=createGUI();
  if(!state) return;

  if(!state.inputDir || !File.directoryExists(state.inputDir)){ Console.criticalln("Invalid input directory"); return; }
  if(!state.outputDir || !File.directoryExists(state.outputDir)){ Console.criticalln("Invalid output directory"); return; }

  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder: "+root);

  try{
    var plan = buildWorkPlan(state.inputDir, state.combineRGB);

    // RGB first if planned
    if (plan.doRGB){
      Console.writeln("\n→ Building RGB from:");
      Console.writeln("   R: "+File.extractName(plan.r));
      Console.writeln("   G: "+File.extractName(plan.g));
      Console.writeln("   B: "+File.extractName(plan.b));
      var combo = combineRGB(plan.r, plan.g, plan.b, root);
      processOne(combo.window, combo.base, root, state.ai, state.save.rgb, /*isRGBCombined*/true, /*enhanceStars*/defaults.colorEnhanceRGBStarsStretched);
      try{ combo.window.forceClose(); }catch(_){}
    } else {
      Console.writeln("\n(No full RGB set found or combining disabled)");
    }

    // Process remaining masters
    for(var i=0;i<plan.others.length;++i){
      var item=plan.others[i];
      Console.writeln("\n→ Processing "+item.filter+": "+File.extractName(item.path));
      var w=ImageWindow.open(item.path)[0];
      if(w){
        var base=sanitizeBase(item.filter+"_"+File.extractNameAndExtension(item.path));
        processOne(w, base, root, state.ai, state.save.mono, /*isRGBCombined*/false, /*enhanceStars*/false);
        try{ w.forceClose(); }catch(_){}
      }
    }

    Console.writeln("\n=== Pipeline Complete ===");
    Console.writeln("All outputs saved to: "+root);

  }catch(e){
    Console.criticalln("Pipeline failed: "+e.message);
    if(e.stack) Console.writeln(e.stack);
  }
}

run();

})();