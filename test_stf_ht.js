/*
 * STF Auto Stretch and Apply <PERSON>ript - CORRECTED VERSION
 *
 * This script replicates the functionality of the STF Auto-Stretch ("nuclear button"),
 * applies it temporarily for display, and then applies the stretch permanently using HistogramTransformation.
 *
 * Based on the canonical implementation of the STF algorithm.
 */
#feature-id Utilities > AutoStretchAndApplyPermanent

// Test image path
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-24T06-11-29/5_stacked/RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test/";

// Create output directory if it doesn't exist
try {
    if (!File.directoryExists(outputDir)) {
        File.createDirectory(outputDir, true);
        Console.writeln("Created output directory: " + outputDir);
    } else {
        Console.writeln("Output directory already exists: " + outputDir);
    }
} catch(e) {
    Console.writeln("Directory creation note: " + e.message);
    Console.writeln("Using existing directory: " + outputDir);
}

/*
 * Default STF Parameters (These match the default Auto-Stretch behavior)
 */
// Shadows clipping point in (normalized) MAD units from the median.
#define DEFAULT_AUTOSTRETCH_SCLIP -2.80
// Target mean background in the [0,1] range.
#define DEFAULT_AUTOSTRETCH_TBGND 0.25
// Apply the same STF to all nominal channels (true for nuclear linked to preserve colors).
#define DEFAULT_AUTOSTRETCH_CLINK true

/*
 * STF Auto Stretch routine
 * Calculates STF parameters based on image statistics.
 */
function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked )
{
   // Use defaults if parameters are not provided
   if ( shadowsClipping == undefined )
      shadowsClipping = DEFAULT_AUTOSTRETCH_SCLIP;
   if ( targetBackground == undefined )
      targetBackground = DEFAULT_AUTOSTRETCH_TBGND;
   if ( rgbLinked == undefined )
      rgbLinked = DEFAULT_AUTOSTRETCH_CLINK;

   console.writeln("STF Parameters: shadowsClipping=" + shadowsClipping + ", targetBackground=" + targetBackground + ", rgbLinked=" + rgbLinked);

   // Initialize an STF object to hold the parameters
   var stf = new ScreenTransferFunction;

   // Determine the number of nominal channels (3 for Color, 1 for Grayscale)
   var n = view.image.isColor ? 3 : 1;
   console.writeln("Image channels: " + n + ", isColor: " + view.image.isColor);

   // 1. Calculate Statistics (Median and MAD)
   // computeOrFetchProperty ensures we get up-to-date statistics efficiently.
   var median = view.computeOrFetchProperty( "Median" );
   var mad = view.computeOrFetchProperty( "MAD" );

   console.writeln("Original statistics computed");
   for (var c = 0; c < n; ++c) {
      console.writeln("Channel " + c + ": median=" + median.at(c).toFixed(6) + ", mad=" + mad.at(c).toFixed(6));
   }

   // 2. Normalize MAD
   // Normalize MAD by 1.4826 to be coherent with the standard deviation (sigma).
   mad.mul( 1.4826 );

   console.writeln("Normalized MAD statistics:");
   for (var c = 0; c < n; ++c) {
      console.writeln("Channel " + c + ": median=" + median.at(c).toFixed(6) + ", mad=" + mad.at(c).toFixed(6));
   }

   // 3. Calculate Stretch Parameters
   if ( rgbLinked && n > 1)
   {
      // Linked RGB channels (the typical "nuclear button" behavior)
      console.noteln("Calculating Linked RGB Stretch...");

      // Determine if the image is inverted (e.g., median > 0.5)
      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
         if ( median.at( c ) > 0.5 )
            ++invertedChannels;

      console.writeln("Inverted channels: " + invertedChannels + "/" + n);

      if ( invertedChannels < n )
      {
         // Noninverted image (typical astronomical data)
         var c0 = 0, m = 0;
         // Calculate average statistics across channels
         for ( var c = 0; c < n; ++c )
         {
            // Robust check for zero MAD
            if ( 1 + mad.at( c ) != 1 )
               // Formula: Median + (ClippingFactor * Sigma)
               c0 += median.at( c ) + shadowsClipping * mad.at( c );
            m += median.at( c );
         }
         // Average the shadows clipping point (c0) and clamp to [0, 1]
         c0 = Math.range( c0/n, 0.0, 1.0 );

         // Calculate midtones balance using the MTF (Midtones Transfer Function)
         // Math.mtf(target, input) calculates the 'm' needed to map 'input' to 'target'
         m = Math.mtf( targetBackground, m/n - c0 );

         // Apply the same STF parameters to all channels
         // STF structure: [c0, c1, m, r0, r1]
         stf.STF = [
                     [c0, 1, m, 0, 1], // R
                     [c0, 1, m, 0, 1], // G
                     [c0, 1, m, 0, 1], // B
                     [0, 1, 0.5, 0, 1] // Alpha (identity)
                   ];
         console.writeln("Linked Stretch: c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
      }
      else
      {
         // Inverted image logic (Handles highlights clipping c1 instead of shadows c0)
         console.noteln("Image appears inverted. Calculating highlight clipping.");
         var c1 = 0, m = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c1 += median.at( c ) - shadowsClipping * mad.at( c );
            m += median.at( c );
         }
         c1 = Math.range( c1/n, 0.0, 1.0 );
         m = Math.mtf( c1 - m/n, targetBackground );

         stf.STF = [
                     [0, c1, m, 0, 1],
                     [0, c1, m, 0, 1],
                     [0, c1, m, 0, 1],
                     [0, 1, 0.5, 0, 1]
                   ];
         console.writeln("Inverted Stretch: c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
      }
   }
   else
   {
      // Unlinked channels (or Grayscale image) - NUCLEAR STRETCH
      console.noteln("Calculating Unlinked/Nuclear Stretch...");
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];

      for ( var c = 0; c < n; ++c )
      {
         if ( median.at( c ) < 0.5 )
         {
            // Noninverted channel
            var c0 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
            var m = Math.mtf( targetBackground, median.at( c ) - c0 );
            A[c] = [c0, 1, m, 0, 1];
            console.writeln("Channel " + c + ": c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
         }
         else
         {
            // Inverted channel
            var c1 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
            var m = Math.mtf( c1 - median.at( c ), targetBackground );
            A[c] = [0, c1, m, 0, 1];
            console.writeln("Channel " + c + " (inverted): c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
         }
      }
      stf.STF = A;
   }

   console.writeln("Final STF parameters:");
   for (var c = 0; c < stf.STF.length; ++c) {
      console.writeln("  Channel " + c + ": [" + stf.STF[c].join(", ") + "]");
   }

   return stf;
}

/*
 * Apply HistogramTransformation
 * Transfers STF parameters to HT and applies it to the view.
 */
function ApplyHistogramTransformation( view, stf )
{
   console.writeln("\n=== Applying HistogramTransformation ===");

   var ht = new HistogramTransformation;

   // Initialize HT parameters array (H).
   // HT structure for each channel is identical to STF:
   // [shadows_clip, highlights_clip, midtones_balance, low_range, high_range]
   // We must define 5 channels for HT: R/K, G, B, RGB/K (Combined), Alpha.
   var H = [
            [0, 1, 0.5, 0, 1], // R/K (Index 0)
            [0, 1, 0.5, 0, 1], // G   (Index 1)
            [0, 1, 0.5, 0, 1], // B   (Index 2)
            [0, 1, 0.5, 0, 1], // RGB/K Combined (Index 3)
            [0, 1, 0.5, 0, 1]  // Alpha (Index 4)
           ];

   var n = view.image.isColor ? 3 : 1;
   console.writeln("Setting up HT for " + n + " channels, isColor: " + view.image.isColor);

   if ( view.image.isColor )
   {
       // Check if the STF channels are linked (parameters are identical)
       var linked = true;
       for(var c = 1; c < n; ++c) {
           // Compare c0, c1, and m parameters
           if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
               linked = false;
               break;
           }
       }

       console.writeln("STF channels are " + (linked ? "linked" : "unlinked"));

       if(linked) {
           // CRITICAL: If linked (the "nuclear button" case), apply to the combined RGB/K channel (Index 3) in HT.
           // This mirrors the behavior when manually dragging the STF instance onto the HT tool.
           H[3][0] = stf.STF[0][0]; // shadows clipping
           H[3][1] = stf.STF[0][1]; // highlights clipping
           H[3][2] = stf.STF[0][2]; // midtones balance
           console.writeln("Applied to combined RGB channel (index 3): [" + H[3].join(", ") + "]");
           // Individual R, G, B channels (0, 1, 2) remain as identity transforms.
       } else {
           // If unlinked, apply to individual channels (Indices 0, 1, 2).
           for ( var c = 0; c < n; ++c )
           {
              H[c][0] = stf.STF[c][0];
              H[c][1] = stf.STF[c][1];
              H[c][2] = stf.STF[c][2];
              console.writeln("Applied to channel " + c + ": [" + H[c].join(", ") + "]");
           }
           // RGB/K Combined channel (3) remains as identity transform.
       }
   }
   else
   {
       // Monochrome image (Apply to Index 0)
       H[0][0] = stf.STF[0][0];
       H[0][1] = stf.STF[0][1];
       H[0][2] = stf.STF[0][2];
       console.writeln("Applied to mono channel (index 0): [" + H[0].join(", ") + "]");
   }

   ht.H = H;

   console.writeln("Final HT parameters:");
   for (var c = 0; c < H.length; ++c) {
      console.writeln("  HT Channel " + c + ": [" + H[c].join(", ") + "]");
   }

   // Apply the HistogramTransformation to the view (making the stretch permanent)
   console.noteln("Applying Histogram Transformation...");
   ht.executeOn( view );
   console.writeln("HistogramTransformation applied successfully!");
}

// Main execution block
function main()
{
   console.show();
   console.writeln("=== STF Auto Stretch Test ===");

   try {
       // Open the test file
       console.writeln("Opening: " + inputFile);
       var windows = ImageWindow.open(inputFile);
       if (windows.length === 0) {
           throw new Error("Failed to open file: " + inputFile);
       }

       var window = windows[0];
       var view = window.currentView;
       console.writeln("File opened successfully: " + view.fullId);
       console.writeln("Image info: " + view.image.width + "x" + view.image.height +
                      ", channels=" + view.image.numberOfChannels +
                      ", color=" + view.image.isColor);

       // 1. Calculate STF Auto Stretch parameters.
       // The last parameter 'true' ensures linked RGB channels for color preservation.
       var stf = STFAutoStretch( view, DEFAULT_AUTOSTRETCH_SCLIP, DEFAULT_AUTOSTRETCH_TBGND, true );

       // 1.5 Apply STF temporarily for display
       console.writeln("Applying temporary STF for display...");
       stf.executeOn(view);
       console.writeln("Temporary STF applied.");

       // 2. Apply the calculated STF parameters using HistogramTransformation for permanent stretch
       ApplyHistogramTransformation( view, stf );

       // 3. Reset the STF visualization.
       // Since the image is now permanently stretched, we must disable the STF visualization
       // to avoid displaying a "double stretch".
       try {
           window.disableScreenTransferFunctions();
       } catch(e) {
           // Alternative method to reset STF
           var stf = new ScreenTransferFunction;
           stf.executeOn(view, false); // Reset to identity
           console.writeln("STF reset using alternative method");
       }

       // 4. Save results
       var outputPath = outputDir + "STF_Nuclear_Test_Result.xisf";
       window.saveAs(outputPath, false, false, false, false);
       console.writeln("Result saved: " + outputPath);

       // Save as FITS for inspection
       var fitsPath = outputDir + "STF_Nuclear_Test_Result.fit";
       window.saveAs(fitsPath, false, false, false, true);
       console.writeln("FITS saved: " + fitsPath);

       console.writeln( "✅ STF Nuclear Stretch applied permanently via HistogramTransformation." );
       console.writeln("\n=== Test Complete ===");

   } catch (error) {
       console.criticalln("Error: " + error.message);
   }
}

main();
