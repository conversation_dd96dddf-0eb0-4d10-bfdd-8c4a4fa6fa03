/*
 * Test STF and HT Function Debug Script
 * Tests the AutoSTF algorithm on a specific image
 */

(function(){

// Test image path
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-24T06-11-29/5_stacked/RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test/";

// Create output directory if it doesn't exist
try {
    if (!File.directoryExists(outputDir)) {
        File.createDirectory(outputDir, true);
        Console.writeln("Created output directory: " + outputDir);
    } else {
        Console.writeln("Output directory already exists: " + outputDir);
    }
} catch(e) {
    Console.writeln("Directory creation note: " + e.message);
    Console.writeln("Using existing directory: " + outputDir);
}

Console.show();
Console.writeln("=== STF and HT Function Debug Test ===");

#define DEFAULT_SHADOWS_CLIPPING   -2.80
#define DEFAULT_TARGET_BACKGROUND   0.25
#define DEFAULT_RGB_LINKED         false

function computeAutoSTFParams(view, shadowsClipping, targetBackground, rgbLinked) {
   Console.writeln("Computing AutoSTF parameters...");
   
   if (shadowsClipping === undefined) shadowsClipping = DEFAULT_SHADOWS_CLIPPING;
   if (targetBackground === undefined) targetBackground = DEFAULT_TARGET_BACKGROUND;
   if (rgbLinked === undefined) rgbLinked = DEFAULT_RGB_LINKED;

   Console.writeln("Settings: shadowsClipping=" + shadowsClipping + ", targetBackground=" + targetBackground + ", rgbLinked=" + rgbLinked);

   var n = view.image.numberOfChannels;
   Console.writeln("Number of channels: " + n);
   
   // Use simple approach - compute statistics for entire image
   var median = [], mad = [];
   var img = view.image;
   var rect = new Rect(0, 0, img.width, img.height);
   Console.writeln("Image size: " + img.width + "x" + img.height);
   
   try {
      for (var c = 0; c < n; ++c) {
         Console.writeln("Computing statistics for channel " + c + "...");
         median[c] = img.median(rect, c, c);
         mad[c] = img.MAD(median[c], rect, c, c) * 1.4826; // Scale MAD to stddev
         Console.writeln("Channel " + c + ": median=" + median[c].toFixed(6) + ", mad=" + mad[c].toFixed(6));
      }
   } catch(e) {
      Console.criticalln("Error computing statistics: " + e.message);
      throw e;
   }

   var params = [[0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]]; // R, G, B, combined

   if (rgbLinked) {
      Console.writeln("Using RGB linked mode...");
      var invertedChannels = 0;
      for (var c = 0; c < n; ++c) {
         if (median[c] > 0.5) ++invertedChannels;
      }
      Console.writeln("Inverted channels: " + invertedChannels + "/" + n);

      if (invertedChannels < n) {
         // Non-inverted image
         Console.writeln("Processing as non-inverted image...");
         var c0 = 0, medAvg = 0;
         for (var c = 0; c < n; ++c) {
            var dev = (1 + mad[c] !== 1) ? shadowsClipping * mad[c] : 0;
            c0 += median[c] + dev;
            medAvg += median[c];
         }
         c0 = Math.range(c0 / n, 0, 1);
         var m = Math.mtf(targetBackground, (medAvg / n) - c0);
         Console.writeln("Linked params: c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
         for (var c = 0; c < 3; ++c) params[c] = [c0, 1, m, 0, 1];
      } else {
         // Inverted image
         Console.writeln("Processing as inverted image...");
         var c1 = 0, medAvg = 0;
         for (var c = 0; c < n; ++c) {
            var dev = (1 + mad[c] !== 1) ? -shadowsClipping * mad[c] : 0;
            c1 += median[c] + dev;
            medAvg += median[c];
         }
         c1 = Math.range(c1 / n, 0, 1);
         var m = Math.mtf(c1 - (medAvg / n), targetBackground);
         Console.writeln("Inverted params: c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
         for (var c = 0; c < 3; ++c) params[c] = [0, c1, m, 0, 1];
      }
   } else {
      Console.writeln("Using RGB unlinked mode (nuclear stretch)...");
      // Unlinked channels (nuclear stretch)
      for (var c = 0; c < n; ++c) {
         if (median[c] < 0.5) {
            // Non-inverted channel
            var c0 = Math.range(median[c] + shadowsClipping * mad[c], 0, 1);
            var m = Math.mtf(targetBackground, median[c] - c0);
            params[c] = [c0, 1, m, 0, 1];
            Console.writeln("Channel " + c + " (non-inverted): c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
         } else {
            // Inverted channel
            var c1 = Math.range(median[c] - shadowsClipping * mad[c], 0, 1);
            var m = Math.mtf(c1 - median[c], targetBackground);
            params[c] = [0, c1, m, 0, 1];
            Console.writeln("Channel " + c + " (inverted): c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
         }
      }
   }
   
   Console.writeln("Final parameters:");
   for (var c = 0; c < params.length; ++c) {
      Console.writeln("  Channel " + c + ": [" + params[c].join(", ") + "]");
   }
   
   return params;
}

function testAutoSTF(view) {
   Console.writeln("\n=== Testing AutoSTF ===");
   try {
      var params = computeAutoSTFParams(view, -2.80, 0.25, false);
      Console.writeln("AutoSTF parameters computed successfully!");
      return params;
   } catch(e) {
      Console.criticalln("AutoSTF failed: " + e.message);
      return null;
   }
}

function testAutoHT(view, params) {
   Console.writeln("\n=== Testing AutoHT ===");
   if (!params) {
      Console.criticalln("No parameters to test HT with!");
      return false;
   }
   
   try {
      var ht = new HistogramTransformation();
      ht.H = params;
      Console.writeln("HistogramTransformation created successfully!");
      Console.writeln("Applying to view...");
      ht.executeOn(view);
      Console.writeln("HistogramTransformation applied successfully!");
      return true;
   } catch(e) {
      Console.criticalln("AutoHT failed: " + e.message);
      return false;
   }
}

try {
    // Open the file
    Console.writeln("Opening: " + inputFile);
    var windows = ImageWindow.open(inputFile);
    if (windows.length === 0) {
        throw new Error("Failed to open file: " + inputFile);
    }
    
    var win = windows[0];
    Console.writeln("File opened successfully: " + win.mainView.id);
    Console.writeln("Image info: " + win.mainView.image.width + "x" + win.mainView.image.height + 
                   ", channels=" + win.mainView.image.numberOfChannels + 
                   ", color=" + win.mainView.image.isColor);
    
    // Test AutoSTF computation
    var params = testAutoSTF(win.mainView);
    
    if (params) {
        // Test AutoHT application
        var success = testAutoHT(win.mainView, params);
        
        if (success) {
            // Save result
            var outputPath = outputDir + "STF_HT_Test_Result.xisf";
            win.saveAs(outputPath, false, false, false, false);
            Console.writeln("Result saved: " + outputPath);
            
            // Save as FITS for inspection
            var fitsPath = outputDir + "STF_HT_Test_Result.fit";
            win.saveAs(fitsPath, false, false, false, true);
            Console.writeln("FITS saved: " + fitsPath);
        }
    }
    
    Console.writeln("\n=== Test Complete ===");
    
} catch (error) {
    Console.criticalln("Error: " + error.message);
}

})();
