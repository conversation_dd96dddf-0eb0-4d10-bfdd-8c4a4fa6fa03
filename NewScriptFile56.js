/* AutoIntegrateSimple – ES5-safe, ONE picker, RGB-separated (with Windows path normalization)
   - Select ALL frames once (LIGHT + optional BIAS/DARK/FLAT)
   - Build masters if present -> AI_Simple/masters/*.xisf
   - Calibrate lights (skip/auto-disable missing masters)
   - StarAlignment per R/G/B group
   - ImageIntegration per R/G/B -> AI_Simple/int/Integration_R/G/B.xisf
*/

// ---------- Utils (ES5-safe) ----------
function log(s){ console.writeln(s); } function warn(s){ console.warningln(s); } function err(s){ console.criticalln(s); }

function endsWithSlash(d){ if(!d||d.length===0) return false; var c=d.charAt(d.length-1); return c==='/'||c==='\\'; }
function pathJoin(d,n){ return endsWithSlash(d)? (d+n) : (d + '/' + n); }
function ensureDir(p){ try{ p = npath(p); if(!File.directoryExists(p)) File.createDirectory(p); }catch(e){ throw new Error("mkdir "+p+" :: "+e); } }
function dirName(p){ return (typeof File.extractDirectory==="function")? File.extractDirectory(p) : p.replace(/[/\\][^/\\]*$/,""); }
function baseName(p){ return (typeof File.extractName==="function")? File.extractName(p) : p.replace(/^.*[/\\]/,"").replace(/\.[^.]*$/,""); }

// ---- Windows drive normalization ----
var g_drive = null;
function driveOf(p){ var m = (p||"").match(/^([A-Za-z]:)[/\\]/); return m? m[1] : null; }
function normalizePathWithDrive(p, drv){
  if(!p) return p;
  if(/^[A-Za-z]:[\\/]/.test(p)) return p;             // already has drive
  var first = p.charAt(0);
  if(first==='/' || first==='\\') return (drv||"C:")+p; // add drive before absolute root path
  return p; // relative -> leave as-is
}
function npath(p){ return normalizePathWithDrive(p, g_drive); }

function selectAllOnce(caption){
  var ofd=new OpenFileDialog;
  ofd.caption=caption||"Select ALL frames (LIGHT + optional BIAS/DARK/FLAT)";
  ofd.multipleSelections=true; ofd.loadImageFilters();
  if(!ofd.execute()) return []; return ofd.fileNames||[];
}

function saveWindowAs(win,outPath){
  outPath = npath(outPath);
  ensureDir(dirName(outPath));
  var f=new File(outPath); if(f.exists) f.remove();
  var ok=win.saveAs(outPath,false,false);
  if((!ok)||(!File.exists(outPath))) ok=win.saveAs(outPath,true,false);
  if((!ok)||(!File.exists(outPath))) throw new Error("Save failed: "+outPath);
}

function readFitsKeyword(filePath, keyUpper){
  try{
    var ws=ImageWindow.open(filePath); if(!ws||ws.length===0) return null;
    var w=ws[0], kw=w.keywords, val=null, i=0;
    for(i=0;i<kw.length;i++){
      var k=(kw[i].name||"").toUpperCase();
      if(k===keyUpper){ val=(kw[i].strippedValue||kw[i].value||""); break; }
    }
    w.close(); return val;
  }catch(e){ return null; }
}

// Safe CFA detection -> {enabled:boolean, pattern:number|null}
function detectCFAPatternFromFITS(filePath){
  try{
    var ws=ImageWindow.open(filePath); if(!ws||ws.length===0) return {enabled:false,pattern:null};
    var w=ws[0], kw=w.keywords, val=null, i=0;
    for(i=0;i<kw.length;i++){
      var k=(kw[i].name||"").toUpperCase();
      if(k==="BAYERPAT"||k==="BAYER_PATTERN"||k==="CFAPATTERN"||k==="FILTER"){
        val=(kw[i].strippedValue||kw[i].value||"").toUpperCase(); break;
      }
    }
    w.close(); if(!val) return {enabled:false,pattern:null};
    var pat=null;
    if(val.indexOf("RGGB")>=0) pat=Debayer.prototype.RGGB;
    else if(val.indexOf("BGGR")>=0) pat=Debayer.prototype.BGGR;
    else if(val.indexOf("GRBG")>=0) pat=Debayer.prototype.GRBG;
    else if(val.indexOf("GBRG")>=0) pat=Debayer.prototype.GBRG;
    return pat!=null? {enabled:true,pattern:pat}:{enabled:false,pattern:null};
  }catch(e){ return {enabled:false,pattern:null}; }
}

function filesForImageIntegration(paths){
  var v=[], i=0; for(i=0;i<paths.length;i++) v.push([true, npath(paths[i]), "", ""]); return v;
}

// ---------- Classification ----------
function classifyFrames(allPaths){
  var out={lights:[], bias:[], dark:[], flat:[]}, i=0;
  for(i=0;i<allPaths.length;i++){
    var p=allPaths[i];
    var typ=(readFitsKeyword(p,"IMAGETYP")||"").toString().trim().toUpperCase();
    var n=baseName(p).toUpperCase();
    function has(s){ return typ.indexOf(s)>=0 || n.indexOf(s)>=0; }
    if(has("LIGHT")) out.lights.push(p);
    else if(has("BIAS")||has("OFFSET")) out.bias.push(p);
    else if(has("DARK")) out.dark.push(p);
    else if(has("FLAT")) out.flat.push(p);
    else out.lights.push(p);
  }
  return out;
}

// ---------- Filter grouping ----------
function inferFilter(filePath){
  var f=(readFitsKeyword(filePath,"FILTER")||"").toString().trim().toUpperCase();
  var n=baseName(filePath).toUpperCase();
  if(f.indexOf("RED")>=0 || f==="R" || n.indexOf("-RED-")>=0 || /[_\-\.]R([_\-\.]|$)/.test(n)) return "R";
  if(f.indexOf("GREEN")>=0 || f==="G" || n.indexOf("-GREEN-")>=0 || /[_\-\.]G([_\-\.]|$)/.test(n)) return "G";
  if(f.indexOf("BLUE")>=0 || f==="B" || n.indexOf("-BLUE-")>=0 || /[_\-\.]B([_\-\.]|$)/.test(n)) return "B";
  return "UNK";
}
function groupByFilter(paths){
  var g={R:[],G:[],B:[],UNK:[]}, i=0, tag="";
  for(i=0;i<paths.length;i++){ tag=inferFilter(paths[i]); g[tag].push(npath(paths[i])); }
  return g;
}

// ---------- Masters ----------
function integrateMaster(name, files, outDir){
  if(!files||files.length<1) throw new Error("No files for "+name);
  var P=new ImageIntegration;
  P.images=filesForImageIntegration(files);
  P.combination=ImageIntegration.prototype.Average;
  P.weightMode=ImageIntegration.prototype.DontCare;
  P.normalization=ImageIntegration.prototype.NoNormalization;
  P.rejection=ImageIntegration.prototype.PercentileClip;
  P.rejectionNormalization=ImageIntegration.prototype.NoRejectionNormalization;
  P.rangeClipLow=false;
  if(typeof P.evaluateSNR!=="undefined") P.evaluateSNR=false; else P.evaluateNoise=false;
  P.subtractPedestals=false;
  P.executeGlobal();

  var outPath=npath(pathJoin(outDir,name+".xisf"));
  var win=ImageWindow.windowById(P.integrationImageId)||ImageWindow.activeWindow;
  if(!win) throw new Error("Integration result window not found for "+name);
  saveWindowAs(win,outPath);
  if(!File.exists(outPath)) throw new Error("Master not written: "+outPath);
  log(name+" -> "+outPath);

  function closeIf(id){ if(id){ var w=ImageWindow.windowById(id); if(w) w.close(); } }
  closeIf(P.highRejectionMapImageId); closeIf(P.lowRejectionMapImageId); closeIf(P.slopeMapImageId);
  return outPath;
}

// ---------- Calibration (existence checks + auto-retry) ----------
function calibrateLights(lightFiles, masters, outDir, cfaDetect){
  if(!lightFiles||lightFiles.length===0) throw new Error("No lights");
  ensureDir(outDir);

  function exist(p){ return !!(p && File.exists(npath(p))); }
  var use={ bias:exist(masters.bias)?npath(masters.bias):null,
            dark:exist(masters.dark)?npath(masters.dark):null,
            flat:exist(masters.flat)?npath(masters.flat):null };

  if(!use.bias && !use.dark && !use.flat){ log("No valid masters on disk → skipping calibration."); return lightFiles.slice(0); }

  function doCal(u){
    var P=new ImageCalibration, i=0;
    var t=[]; for(i=0;i<lightFiles.length;i++) t.push([true,npath(lightFiles[i])]); P.targetFrames=t;

    P.enableCFA=false;
    if(cfaDetect&&cfaDetect.enabled&&typeof cfaDetect.pattern==="number"){ P.enableCFA=true; P.cfaPattern=cfaDetect.pattern; }

    if(u.bias){ P.masterBiasEnabled=true; P.masterBiasPath=u.bias; } else P.masterBiasEnabled=false;
    if(u.dark){ P.masterDarkEnabled=true; P.masterDarkPath=u.dark; } else P.masterDarkEnabled=false;
    if(u.flat){ P.masterFlatEnabled=true; P.masterFlatPath=u.flat; } else P.masterFlatEnabled=false;

    P.outputPedestalMode=ImageCalibration.prototype.OutputPedestal_Auto;
    P.calibrateBias=false; P.calibrateDark=false; P.calibrateFlat=false; P.optimizeDarks=false;
    P.outputDirectory=npath(outDir); P.overwriteExistingFiles=true;

    log("Using masters → bias: "+(u.bias||"none")+", dark: "+(u.dark||"none")+", flat: "+(u.flat||"none"));
    P.executeGlobal();

    var out=[], j=0; for(j=0;j<P.outputData.length;j++) out.push(npath(P.outputData[j][0])); return out;
  }

  try { return doCal(use); }
  catch(e){
    var msg=e.toString(); warn("Calibration failed: "+msg+" → retry disabling missing master.");
    var u2={bias:use.bias,dark:use.dark,flat:use.flat};
    if(msg.indexOf("bias_master")>=0) u2.bias=null;
    if(msg.indexOf("dark_master")>=0) u2.dark=null;
    if(msg.indexOf("flat_master")>=0) u2.flat=null;
    if(!u2.bias && !u2.dark && !u2.flat){ warn("All masters disabled → skipping calibration."); return lightFiles.slice(0); }
    return doCal(u2);
  }
}

// ---------- Registration ----------
function alignLights(calibratedFiles, outDir){
  if(!calibratedFiles||calibratedFiles.length<1) throw new Error("No calibrated lights");
  ensureDir(outDir);

  var SA=new StarAlignment, i=0;
  SA.referenceIsFile=true; SA.referenceImage=npath(calibratedFiles[0]);
  var targets=[]; for(i=0;i<calibratedFiles.length;i++) targets.push([true,true,npath(calibratedFiles[i])]);
  SA.targets=targets;

  SA.structureLayers=5; SA.noiseLayers=1; SA.hotPixelFilterRadius=1;
  SA.sensitivity=0.100; SA.maxStarDistortion=0.500; SA.matcherTolerance=0.0030;

  SA.outputDirectory=npath(outDir); SA.outputExtension=".xisf"; SA.outputPostfix="_r"; SA.maskPostfix="_m";
  SA.generateMasks=false; SA.frameAdaptation=false; SA.useSurfaceSplines=false; SA.overwriteExistingFiles=true;
  SA.onError=StarAlignment.prototype.Continue;

  SA.executeGlobal();

  var reg=[], k=0; for(k=0;k<calibratedFiles.length;k++){
    var bn=baseName(calibratedFiles[k]); reg.push(npath(pathJoin(outDir,bn+"_r.xisf")));
  }
  return reg;
}

// ---------- Integration ----------
function integrateChannel(registeredFiles, outDir, name){
  if(!registeredFiles||registeredFiles.length<1) throw new Error("No registered files for "+name);
  ensureDir(outDir);

  var P=new ImageIntegration;
  P.images=filesForImageIntegration(registeredFiles);
  P.combination=ImageIntegration.prototype.Average;
  P.weightMode=ImageIntegration.prototype.DontCare;
  P.normalization=ImageIntegration.prototype.MultiplicativeWithScaling;
  P.rejection=ImageIntegration.prototype.PercentileClip;
  P.rejectionNormalization=ImageIntegration.prototype.EqualizeFluxes;
  if(typeof P.evaluateSNR!=="undefined") P.evaluateSNR=true; else P.evaluateNoise=true;
  P.subtractPedestals=false;

  P.executeGlobal();

  var outPath=npath(pathJoin(outDir,name+".xisf"));
  var win=ImageWindow.windowById(P.integrationImageId)||ImageWindow.activeWindow;
  if(!win) throw new Error("Integrated window not found ("+name+")");
  saveWindowAs(win,outPath); log(name+" saved: "+outPath);

  function closeIf(id){ if(id){ var w=ImageWindow.windowById(id); if(w) w.close(); } }
  closeIf(P.highRejectionMapImageId); closeIf(P.lowRejectionMapImageId); closeIf(P.slopeMapImageId);
  return outPath;
}

// ---------- Main (ONE picker) ----------
function runAutoIntegrateSimple(){
  var all=selectAllOnce("Select ALL frames at once (LIGHT + optional BIAS/DARK/FLAT)");
  if(all.length===0){ warn("No files selected. Aborting."); return; }

  // capture drive from first file; normalize all downstream paths
  g_drive = driveOf(all[0]);

  var base=npath(dirName(all[0]));
  var work=npath(pathJoin(base,"AI_Simple"));
  var mastersDir=npath(pathJoin(work,"masters"));
  var cal=npath(pathJoin(work,"cal"));
  var reg=npath(pathJoin(work,"reg"));
  var out=npath(pathJoin(work,"int"));
  ensureDir(work); ensureDir(mastersDir); ensureDir(cal); ensureDir(reg); ensureDir(out);

  var sets=classifyFrames(all);
  log("Detected → LIGHT:"+sets.lights.length+"  BIAS:"+sets.bias.length+"  DARK:"+sets.dark.length+"  FLAT:"+sets.flat.length);
  if(sets.lights.length===0){ warn("No LIGHT frames detected (check IMAGETYP or filenames). Aborting."); return; }

  var masters={bias:null,dark:null,flat:null};
  if(sets.bias.length>=1){ log("Master bias..."); masters.bias=integrateMaster("bias_master",sets.bias,mastersDir); if(!File.exists(npath(masters.bias))) masters.bias=null; }
  if(sets.dark.length>=1){ log("Master dark..."); masters.dark=integrateMaster("dark_master",sets.dark,mastersDir); if(!File.exists(npath(masters.dark))) masters.dark=null; }
  if(sets.flat.length>=1){ log("Master flat..."); masters.flat=integrateMaster("flat_master",sets.flat,mastersDir); if(!File.exists(npath(masters.flat))) masters.flat=null; }

  var cfa=detectCFAPatternFromFITS(sets.lights[0]); // safe

  log("Calibrating "+sets.lights.length+" lights");
  var calLights=calibrateLights(sets.lights,masters,cal,cfa);

  var groups=groupByFilter(calLights);
  function process(label, arr){
    if(arr.length===0) return;
    log("Registering "+arr.length+" frames for "+label);
    var regCh=alignLights(arr, reg);
    log("Integrating "+label);
    integrateChannel(regCh, out, "Integration_"+label);
  }
  process("R",groups.R); process("G",groups.G); process("B",groups.B);
  if(groups.UNK.length>0) warn("Unknown FILTER files: "+groups.UNK.length+" (skipped RGB integration)");

  console.noteln("Done. Outputs in: "+out);
}

// ---------- Entry ----------
runAutoIntegrateSimple();
