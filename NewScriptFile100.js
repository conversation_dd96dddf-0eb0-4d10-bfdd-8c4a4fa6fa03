/*
 * RGB/Monochrome → Post-Integration Pipeline (Single Output Folder + GUI)
 * - GUI to choose Input/Output directories
 * - Option to SKIP RGB combination (monochrome batch: L/Ha/SII/OIII/OSC/etc.)
 * - Toggles for Deblur V1 (round stars), Deblur V2, <PERSON>oise, Starless
 * - Creates exactly ONE timestamped output folder per run
 * - Runs: Final ABE → Auto Black Point → SPCC (color only, auto-skip for mono) → AI variants
 */

// === REQUIRED INCLUDES ===
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"

// === GLOBAL CONFIG / DEFAULTS ===
var outputExtension = ".xisf";
var defaultInputDir  = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";
var defaultOutputDir = "C:/Users/<USER>/OneDrive/Desktop/Input/Output";

// Auto black-point params
var enableAutoBlackPoint = true;
var sampleSize = 20;
var numSamples = 20;

// AI toggles (overridable by GUI)
var enableDeblurV1 = true;   // round stars
var enableDeblurV2 = true;   // enhancement
var enableDenoising = true;  // noise reduction
var enableStarless = true;   // star separation

// === RUNTIME STATE (set by GUI) ===
var g = {
   inputDir: defaultInputDir,
   outputBaseDir: defaultOutputDir,
   skipRGB: false,             // "No RGB" → process all monochrome/color images individually
   rootOut: ""                 // the SINGLE timestamped output folder created for this run
};

// === UTILS ===
function ensureDir(path){ if(!File.directoryExists(path)) File.createDirectory(path); }

function createTimestampedOutputDir(baseDir){
   function pad2(n){ return n<10 ? "0"+n : ""+n; }
   var now=new Date();
   var ts = now.getFullYear()+"-"+pad2(now.getMonth()+1)+"-"+pad2(now.getDate())+"T"+pad2(now.getHours())+"-"+pad2(now.getMinutes())+"-"+pad2(now.getSeconds());
   var out = baseDir.replace(/\\/g,"/").replace(/\/$/,"") + "/Processed_" + ts;
   ensureDir(out);
   return out;
}

function closeAllWindowsExcept(keepIds){
   var wins = ImageWindow.windows;
   for (var i = wins.length-1; i>=0; --i){
      var keep=false;
      if (keepIds){
         for (var j=0;j<keepIds.length;++j){ if (wins[i].mainView.id===keepIds[j]){ keep=true; break; } }
      }
      if(!keep){ try{ wins[i].forceClose(); }catch(e){} }
   }
}

function sanitizeBase(name){
   var b = name.replace(/[^\w\-]+/g,"_");
   if (b.length===0) b="image";
   return b;
}

// === FILE DISCOVERY ===
function findMasterFramesRGB(dirPath){
   var ff = new FileFind;
   var files = { red:"", green:"", blue:"" };
   if(!ff.begin(dirPath + "/*.xisf")) throw new Error("Cannot access input directory: " + dirPath);
   do{
      var fn = ff.name;
      var full = dirPath + "/" + fn;
      if (fn.indexOf("masterLight")>=0 && fn.indexOf(".xisf")>=0){
         if (fn.indexOf("FILTER-Red")  >=0) { files.red   = full; Console.writeln("Found Red:   " + fn); }
         else if (fn.indexOf("FILTER-Green")>=0) { files.green = full; Console.writeln("Found Green: " + fn); }
         else if (fn.indexOf("FILTER-Blue") >=0) { files.blue  = full; Console.writeln("Found Blue:  " + fn); }
      }
   } while(ff.next());
   return files;
}

function findAllImages(dirPath){
   var list=[];
   var ff = new FileFind;
   if (ff.begin(dirPath + "/*.xisf")) {
      do { list.push(dirPath + "/" + ff.name); } while(ff.next());
   }
   return list;
}

// === RGB COMBINATION ===
function combineRGBChannels(redPath, greenPath, bluePath, rootOut){
   Console.writeln("\n=== RGB Channel Combination ===");
   var r = ImageWindow.open(redPath), g = ImageWindow.open(greenPath), b = ImageWindow.open(bluePath);
   if (r.length===0 || g.length===0 || b.length===0) throw new Error("Failed to open one or more source images");

   var rw=r[0], gw=g[0], bw=b[0];
   var CC = new ChannelCombination;
   CC.colorSpace = ChannelCombination.prototype.RGB;
   CC.channels = [[true,rw.mainView.id],[true,gw.mainView.id],[true,bw.mainView.id]];
   Console.writeln("Executing ChannelCombination...");
   CC.executeGlobal();

   // Get last color window
   var rgbWindow=null, wins=ImageWindow.windows;
   for (var i=wins.length-1; i>=0; --i){ if (wins[i].mainView.image.isColor){ rgbWindow=wins[i]; break; } }
   if (!rgbWindow) throw new Error("Could not find the RGB combined image");

   // Save inside the SINGLE output folder
   var stackDir = rootOut + "/5_stacked"; ensureDir(stackDir);
   var outPath = stackDir + "/RGB_Combined" + outputExtension;
   Console.writeln("Saving RGB image → " + outPath);
   rgbWindow.saveAs(outPath, false, false, false, false);

   // Close source windows
   try{ rw.close(); }catch(_){}
   try{ gw.close(); }catch(_){}
   try{ bw.close(); }catch(_){}

   return { window: rgbWindow, path: outPath, base: "RGB_Combined" };
}

// === FINAL ABE ===
function finalBackgroundExtraction(targetWindow, processSummary){
   Console.writeln("\n=== Final Background Extraction (ABE) ===");
   try{
      var P = new AutomaticBackgroundExtractor();
      P.targetCorrection = 1; P.normalize = true; P.discardBackground = true;

      var before = ImageWindow.windows;
      P.executeOn(targetWindow.mainView);
      var after  = ImageWindow.windows;
      // Close background result window if created
      for (var k=0;k<after.length;++k){
         var isNew=true;
         for (var j=0;j<before.length;++j) if (after[k].mainView.id===before[j].mainView.id){ isNew=false; break; }
         if (isNew && after[k].mainView.id.toLowerCase().indexOf("background")!==-1){ after[k].forceClose(); break; }
      }
      Console.writeln("✅ ABE complete");
      processSummary.backgroundExtraction.status="✅";
      processSummary.backgroundExtraction.details="ABE complete";
   }catch(e){
      Console.writeln("⚠️  ABE failed: " + e.message);
      processSummary.backgroundExtraction.status="⚠️";
      processSummary.backgroundExtraction.details="Failed: " + e.message;
   }
}

// === AUTO BLACK POINT ===
function performAutomaticBlackPointAdjustment(win, processSummary){
   if (!enableAutoBlackPoint){ processSummary.blackPoint.status="⏭️"; processSummary.blackPoint.details="Skipped"; return false; }
   Console.writeln("\n=== Automatic Black Point Adjustment ===");
   try{
      var view = win.mainView, img = view.image;
      var w = img.width, h = img.height, isColor = img.isColor;
      Console.writeln("  Image: " + w + "x" + h + "  Color=" + isColor);

      // If not color → use a robust fallback (AutoHistogram) for mono/NB
      if (!isColor || img.numberOfChannels < 3){
         var A = new AutoHistogram();
         A.auto = true; A.clipLow=0.1; A.clipHigh=0.1;
         A.executeOn(view);
         Console.writeln("  ✅ AutoHistogram applied (mono/NB)");
         processSummary.blackPoint.status="✅"; processSummary.blackPoint.details="AutoHistogram (mono/NB)";
         return true;
      }

      // Color path (sample darkest patches across RGB)
      var rs=[], gs=[], bs=[];
      for (var i=0;i<numSamples;++i){
         var x=Math.floor(Math.random()*(w - sampleSize));
         var y=Math.floor(Math.random()*(h - sampleSize));
         var rect=new Rect(x,y,x+sampleSize,y+sampleSize);
         var s=img.readSamples(rect);
         var cnt=sampleSize*sampleSize, rSum=0,gSum=0,bSum=0;
         for (var p=0;p<cnt;++p){ rSum+=s[p*3]; gSum+=s[p*3+1]; bSum+=s[p*3+2]; }
         rs.push(rSum/cnt); gs.push(gSum/cnt); bs.push(bSum/cnt);
      }
      rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});
      var n=Math.max(1, Math.floor(numSamples*0.25)), r=0,g=0,b=0;
      for (var m=0;m<n;++m){ r+=rs[m]; g+=gs[m]; b+=bs[m]; } r/=n; g/=n; b/=n;

      // Try "Levels" if available; else fallback to AutoHistogram
      try{
         var L = new Levels();
         L.redBlack   = Math.min(r*0.8, 0.02);
         L.greenBlack = Math.min(g*0.9, 0.03);
         L.blueBlack  = Math.min(b*0.8, 0.02);
         L.redWhite   = 0.98; L.greenWhite = 0.97; L.blueWhite = 0.98;
         L.executeOn(view);
         Console.writeln("  ✅ Levels applied");
         processSummary.blackPoint.status="✅"; processSummary.blackPoint.details="Levels";
      }catch(e2){
         var AH = new AutoHistogram();
         AH.auto = true; AH.clipLow=0.1; AH.clipHigh=0.1;
         AH.executeOn(view);
         Console.writeln("  ✅ Fallback AutoHistogram applied");
         processSummary.blackPoint.status="✅"; processSummary.blackPoint.details="Fallback AutoHistogram";
      }
      return true;
   }catch(e){
      Console.writeln("  ❌ Auto black point failed: " + e.message);
      processSummary.blackPoint.status="❌"; processSummary.blackPoint.details="Failed: " + e.message;
      return false;
   }
}

// === SPCC (auto-skip on mono) ===
function performSPCC(win, processSummary){
   try{
      var isColor = win.mainView.image.isColor;
      if (!isColor){ processSummary.spcc.status="⏭️"; processSummary.spcc.details="Mono image"; return false; }

      Console.writeln("\n=== SPCC (with ImageSolver) ===");
      Console.writeln("  Solving image...");
      var solver = new ImageSolver();
      solver.Init(win, false);
      solver.useMetadata = true;
      solver.catalog = "GAIA DR3";
      solver.useDistortionCorrection=false;
      solver.generateErrorMaps=false;
      solver.showStars=false; solver.showDistortion=false;
      solver.generateDistortionMaps=false;
      solver.sensitivity=0.1;

      if(!solver.SolveImage(win)){
         Console.writeln("  ⚠️ Metadata solve failed, trying blind hints...");
         solver.useMetadata=false;
         // Optional hints can be set here if desired:
         // solver.centerRA=..., solver.centerDec=..., solver.resolution=...
         if(!solver.SolveImage(win)) throw new Error("Plate-solving failed.");
      }
      Console.writeln("  ✅ Astrometric solution found");

      Console.writeln("  Applying SPCC...");
      var P = new SpectrophotometricColorCalibration();
      P.whiteReferenceId="AVG_G2V"; P.structureLayers=5; P.saturationThreshold=0.99;
      P.backgroundReferenceViewId=""; P.limitMagnitude=16.0;
      P.executeOn(win.mainView);
      Console.writeln("  ✅ SPCC applied");
      processSummary.spcc.status="✅"; processSummary.spcc.details="SPCC applied";
      return true;
   }catch(e){
      Console.writeln("  ❌ SPCC failed: " + e.message);
      processSummary.spcc.status="❌"; processSummary.spcc.details="Failed: " + e.message;
      return false;
   }
}

// === AI STEPS ===
function applyDeblurV1(win, processSummary){
   if (!enableDeblurV1){ processSummary.deblurV1.status="⏭️"; processSummary.deblurV1.details="Skipped"; return false; }
   Console.writeln("\n=== Deblur V1 (round stars) ===");
   try{
      var P = new BlurXTerminator();
      P.sharpenStars=0.15; P.adjustStarHalos=0.00; P.autoPSF=true; P.psfDiameter=0.00;
      P.sharpenNonstellar=0.00; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
      P.executeOn(win.mainView);
      Console.writeln("  ✅ Deblur V1 applied");
      processSummary.deblurV1.status="✅"; processSummary.deblurV1.details="Applied";
      return true;
   }catch(e){ Console.writeln("  ❌ Deblur V1 failed: " + e.message); processSummary.deblurV1.status="❌"; processSummary.deblurV1.details=e.message; return false; }
}

function applyDeblurV2(win, processSummary){
   if (!enableDeblurV2){ processSummary.deblurV2.status="⏭️"; processSummary.deblurV2.details="Skipped"; return false; }
   Console.writeln("\n=== Deblur V2 (enhancement) ===");
   try{
      var P = new BlurXTerminator();
      P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.autoPSF=true; P.psfDiameter=0.00;
      P.sharpenNonstellar=0.90; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
      P.executeOn(win.mainView);
      Console.writeln("  ✅ Deblur V2 applied");
      processSummary.deblurV2.status="✅"; processSummary.deblurV2.details="Applied";
      return true;
   }catch(e){ Console.writeln("  ❌ Deblur V2 failed: " + e.message); processSummary.deblurV2.status="❌"; processSummary.deblurV2.details=e.message; return false; }
}

function applyDenoising(win, processSummary){
   if (!enableDenoising){ processSummary.denoising.status="⏭️"; processSummary.denoising.details="Skipped"; return false; }
   Console.writeln("\n=== Denoise ===");
   try{
      var P = new NoiseXTerminator();
      P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
      P.executeOn(win.mainView);
      Console.writeln("  ✅ Denoising applied");
      processSummary.denoising.status="✅"; processSummary.denoising.details="Applied";
      return true;
   }catch(e){ Console.writeln("  ❌ Denoising failed: " + e.message); processSummary.denoising.status="❌"; processSummary.denoising.details=e.message; return false; }
}

function applyStarSeparation(win, processSummary, finalDir, baseTag){
   if (!enableStarless){ processSummary.starSeparation.status="⏭️"; processSummary.starSeparation.details="Skipped"; return false; }
   Console.writeln("\n=== Star Separation ===");
   try{
      var P = new StarXTerminator();
      P.generateStarImage=true; P.unscreenStars=true; P.largeOverlap=false;
      P.executeOn(win.mainView);
      Console.writeln("  ✅ Star separation done");

      // If a stars window exists, save it
      var savedStars=false, wins=ImageWindow.windows;
      for (var i=0;i<wins.length;++i){
         var id=wins[i].mainView.id;
         if (id.indexOf("stars")!==-1 || id.indexOf("Stars")!==-1){
            var starsPath = finalDir + "/Stars_Extracted_" + baseTag + outputExtension;
            wins[i].saveAs(starsPath, false, false, false, false);
            try{ wins[i].forceClose(); }catch(_){}
            savedStars=true; break;
         }
      }
      processSummary.starSeparation.status="✅"; processSummary.starSeparation.details = savedStars ? "Saved stars" : "No separate stars window";
      return true;
   }catch(e){ Console.writeln("  ❌ Star separation failed: " + e.message); processSummary.starSeparation.status="❌"; processSummary.starSeparation.details=e.message; return false; }
}

// === POST-INTEGRATION PIPELINING FOR ONE IMAGE ===
function runPostIntegrationPipeline(win, baseTag, rootOut){
   var processSummary = {
      backgroundExtraction: { name:"Background Extraction", status:"❌", details:"Not started" },
      blackPoint:           { name:"Black Point",           status:"❌", details:"Not started" },
      spcc:                 { name:"SPCC",                  status:"❌", details:"Not started" },
      deblurV1:             { name:"Deblur V1",             status:"❌", details:"Not started" },
      deblurV2:             { name:"Deblur V2",             status:"❌", details:"Not started" },
      denoising:            { name:"Denoising",             status:"❌", details:"Not started" },
      starSeparation:       { name:"Star Separation",       status:"❌", details:"Not started" },
      finalSave:            { name:"Final Save",            status:"❌", details:"Not started" }
   };

   // Ensure hierarchy
   var stackDir = rootOut + "/5_stacked"; ensureDir(stackDir);
   var finalDir = rootOut + "/6_final";   ensureDir(finalDir);

   // Save a copy as "Integration_<base>"
   var integrationPath = stackDir + "/Integration_" + baseTag + outputExtension;
   win.saveAs(integrationPath, false, false, false, false);
   processSummary.finalSave.status="✅"; processSummary.finalSave.details="Integration saved";

   closeAllWindowsExcept([win.mainView.id]);

   // Final ABE
   finalBackgroundExtraction(win, processSummary);     closeAllWindowsExcept([win.mainView.id]);

   // Auto black point
   performAutomaticBlackPointAdjustment(win, processSummary);  closeAllWindowsExcept([win.mainView.id]);

   // SPCC (color only)
   performSPCC(win, processSummary);                   closeAllWindowsExcept([win.mainView.id]);

   // Save baseline
   var baselinePath = finalDir + "/Final_Stacked_" + baseTag + outputExtension;
   win.saveAs(baselinePath, false, false, false, false);
   Console.writeln("  ✅ Baseline saved → " + File.extractName(baselinePath));
   processSummary.finalSave.status="✅"; processSummary.finalSave.details="Baseline saved";

   // AI variants
   if (enableDeblurV1){
      var w1 = ImageWindow.open(baselinePath)[0];
      if (w1 && !w1.isNull && applyDeblurV1(w1, processSummary)){
         w1.saveAs(finalDir + "/Deblurred_V1_StarRounding_" + baseTag + outputExtension, false,false,false,false);
      }
      try{ w1.forceClose(); }catch(_){}
   }
   if (enableDeblurV2){
      var w2 = ImageWindow.open(baselinePath)[0];
      if (w2 && !w2.isNull && applyDeblurV2(w2, processSummary)){
         w2.saveAs(finalDir + "/Deblurred_V2_Enhancement_" + baseTag + outputExtension, false,false,false,false);
      }
      try{ w2.forceClose(); }catch(_){}
   }
   if (enableDenoising){
      var w3 = ImageWindow.open(baselinePath)[0];
      if (w3 && !w3.isNull && applyDenoising(w3, processSummary)){
         w3.saveAs(finalDir + "/Denoised_" + baseTag + outputExtension, false,false,false,false);
      }
      try{ w3.forceClose(); }catch(_){}
   }
   if (enableStarless){
      var w4 = ImageWindow.open(baselinePath)[0];
      if (w4 && !w4.isNull){
         if (applyStarSeparation(w4, processSummary, finalDir, baseTag)){
            w4.saveAs(finalDir + "/Starless_" + baseTag + outputExtension, false,false,false,false);
         }
         try{ w4.forceClose(); }catch(_){}
      }
   }

   Console.writeln("\n— Summary for " + baseTag + " —");
   for (var k in processSummary){
      var item=processSummary[k];
      Console.writeln("  " + item.status + " " + item.name + ": " + item.details);
   }
}

// === GUI ===
function showConfigDialog(){
   var dlg = new Dialog;
   dlg.windowTitle = "Post-Integration Pipeline (RGB/Monochrome)";
   dlg.sizer = new VerticalSizer; dlg.sizer.margin=10; dlg.sizer.spacing=8;

   // Title
   var title = new Label(dlg); title.text="Utah Masterclass — Post-Integration Pipeline";
   title.textAlignment = TextAlign_Center;
   dlg.sizer.add(title);

   // Input group
   var gbIn = new GroupBox(dlg); gbIn.title="Input Directory"; gbIn.sizer=new VerticalSizer; gbIn.sizer.margin=8; gbIn.sizer.spacing=6;
   var inRow = new HorizontalSizer; inRow.spacing=6;
   var inEdit = new Edit(dlg); inEdit.readOnly=true; inEdit.text=g.inputDir; inEdit.minWidth=450;
   var inBtn = new PushButton(dlg); inBtn.text="Browse...";
   inBtn.onClick=function(){
      var d=new GetDirectoryDialog; d.caption="Select Input Directory"; d.initialDirectory=g.inputDir;
      if(d.execute()){ g.inputDir=d.directory.replace(/\\/g,"/").replace(/\/$/,""); inEdit.text=g.inputDir; }
   };
   inRow.add(inEdit,100); inRow.add(inBtn);
   gbIn.sizer.add(inRow);
   dlg.sizer.add(gbIn);

   // Output group
   var gbOut = new GroupBox(dlg); gbOut.title="Output Base Directory (timestamped subfolder will be created)"; gbOut.sizer=new VerticalSizer; gbOut.sizer.margin=8; gbOut.sizer.spacing=6;
   var outRow = new HorizontalSizer; outRow.spacing=6;
   var outEdit = new Edit(dlg); outEdit.readOnly=true; outEdit.text=g.outputBaseDir; outEdit.minWidth=450;
   var outBtn = new PushButton(dlg); outBtn.text="Browse...";
   outBtn.onClick=function(){
      var d=new GetDirectoryDialog; d.caption="Select Output Base Directory"; d.initialDirectory=g.outputBaseDir;
      if(d.execute()){ g.outputBaseDir=d.directory.replace(/\\/g,"/").replace(/\/$/,""); outEdit.text=g.outputBaseDir; }
   };
   outRow.add(outEdit,100); outRow.add(outBtn);
   gbOut.sizer.add(outRow);
   dlg.sizer.add(gbOut);

   // Options group
   var gbOpt = new GroupBox(dlg); gbOpt.title="Options"; gbOpt.sizer=new VerticalSizer; gbOpt.sizer.margin=8; gbOpt.sizer.spacing=6;

   var skipRGB = new CheckBox(dlg); skipRGB.text="Skip RGB combination (process all images in input folder individually — mono or color)"; skipRGB.checked=g.skipRGB;
   skipRGB.toolTip="Check for sets like L/Ha/SII/OIII or any monochrome/OSC masters; each file is processed through ABE→BlackPoint→SPCC(if color)→AI.";
   gbOpt.sizer.add(skipRGB);

   var aiRow1 = new HorizontalSizer; aiRow1.spacing=12;
   var cbDeblur1 = new CheckBox(dlg); cbDeblur1.text="Deblur V1 (round stars)"; cbDeblur1.checked=enableDeblurV1;
   var cbDeblur2 = new CheckBox(dlg); cbDeblur2.text="Deblur V2 (enhancement)"; cbDeblur2.checked=enableDeblurV2;
   aiRow1.add(cbDeblur1); aiRow1.add(cbDeblur2); aiRow1.addStretch();
   gbOpt.sizer.add(aiRow1);

   var aiRow2 = new HorizontalSizer; aiRow2.spacing=12;
   var cbDenoise = new CheckBox(dlg); cbDenoise.text="Denoise"; cbDenoise.checked=enableDenoising;
   var cbStarless = new CheckBox(dlg); cbStarless.text="Starless"; cbStarless.checked=enableStarless;
   aiRow2.add(cbDenoise); aiRow2.add(cbStarless); aiRow2.addStretch();
   gbOpt.sizer.add(aiRow2);

   dlg.sizer.add(gbOpt);

   // Buttons
   var btnRow = new HorizontalSizer; btnRow.spacing=8; btnRow.addStretch();
   var btnStart = new PushButton(dlg); btnStart.text="Start"; btnStart.defaultButton=true;
   var btnCancel = new PushButton(dlg); btnCancel.text="Cancel";
   btnRow.add(btnStart); btnRow.add(btnCancel);
   dlg.sizer.add(btnRow);

   btnCancel.onClick=function(){ dlg.cancel(); };
   btnStart.onClick=function(){
      // validations
      if (!File.directoryExists(inEdit.text)){ (new MessageBox("Input directory does not exist.\n"+inEdit.text, "Error", StdIcon_Error, StdButton_Ok)).execute(); return; }
      if (!File.directoryExists(outEdit.text)){ (new MessageBox("Output base directory does not exist.\n"+outEdit.text, "Error", StdIcon_Error, StdButton_Ok)).execute(); return; }

      // commit
      g.inputDir = inEdit.text;
      g.outputBaseDir = outEdit.text;
      g.skipRGB = skipRGB.checked;

      enableDeblurV1 = cbDeblur1.checked;
      enableDeblurV2 = cbDeblur2.checked;
      enableDenoising = cbDenoise.checked;
      enableStarless  = cbStarless.checked;

      dlg.ok();
   };

   return dlg.execute();
}

// === MAIN ===
function main(){
   Console.show();
   Console.writeln("=== Post-Integration Pipeline (RGB/Monochrome) ===");

   if (!showConfigDialog()){
      Console.writeln("Cancelled.");
      return;
   }

   // Create exactly ONE timestamped output folder and use it everywhere
   g.rootOut = createTimestampedOutputDir(g.outputBaseDir);
   Console.writeln("Output folder: " + g.rootOut);

   ensureDir(g.rootOut + "/5_stacked");
   ensureDir(g.rootOut + "/6_final");

   try{
      if (g.skipRGB){
         // MONOCHROME / BATCH PATH: process all images individually
         Console.writeln("\nMode: SKIP RGB — Processing each image found in input...");
         var images = findAllImages(g.inputDir);
         if (images.length===0) throw new Error("No .xisf files found in: " + g.inputDir);

         for (var i=0;i<images.length;++i){
            var path = images[i];
            Console.writeln("\n--- ["+(i+1)+"/"+images.length+"] " + File.extractName(path) + " ---");
            var w = ImageWindow.open(path);
            if (w.length===0){ Console.writeln("  ⚠️ Could not open, skipping."); continue; }
            var win = w[0];
            var base = sanitizeBase(File.extractName(path));
            runPostIntegrationPipeline(win, base, g.rootOut);
            try{ win.forceClose(); }catch(_){}
            closeAllWindowsExcept(null);
         }
      } else {
         // RGB PATH: combine integrated masters then run pipeline ONCE
         Console.writeln("\nMode: RGB combination first, then full pipeline");
         var files = findMasterFramesRGB(g.inputDir);
         var missing=[];
         if(!files.red)   missing.push("Red");
         if(!files.green) missing.push("Green");
         if(!files.blue)  missing.push("Blue");
         if(missing.length) throw new Error("Missing master frames: " + missing.join(", "));

         var rgb = combineRGBChannels(files.red, files.green, files.blue, g.rootOut);
         // Use RGB combined as the integrated image and run pipeline ONCE
         runPostIntegrationPipeline(rgb.window, rgb.base, g.rootOut);
         try{ rgb.window.forceClose(); }catch(_){}
      }

      Console.writeln("\n=== Done. Output: " + g.rootOut + " ===");
      Console.writeln("5_stacked and 6_final contain results. No duplicate output folders were created.");

   }catch(err){
      Console.criticalln("Error: " + err.message);
      throw err;
   }
}

// Execute
main();
