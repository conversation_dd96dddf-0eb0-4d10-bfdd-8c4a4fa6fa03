/*
 * Simple STF and Histogram Transform Test Script
 * Only processes the specified Final_Stacked_RGB_Combined.xisf file
 */

(function(){

// File path
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-23T16-06-36/6_final/Final_Stacked_RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-23T16-06-36/6_final/";

Console.show();
Console.writeln("=== STF and Histogram Transform Test ===");

try {
    // Open the file
    Console.writeln("Opening: " + inputFile);
    var windows = ImageWindow.open(inputFile);
    if (windows.length === 0) {
        throw new Error("Failed to open file: " + inputFile);
    }
    
    var win = windows[0];
    Console.writeln("File opened successfully: " + win.mainView.id);
    
    // Method 1: ScreenTransferFunction with your parameters
    Console.writeln("\n=== Method 1: ScreenTransferFunction ===");
    var win1 = win.createPreview();
    win1.mainView.id = "STF_Test";
    
    var P = new ScreenTransferFunction;
    P.STF = [ // c0, c1, m, r0, r1
       [0.11743, 1.00000, 0.00698, 0.00000, 1.00000],
       [0.11743, 1.00000, 0.00698, 0.00000, 1.00000],
       [0.11743, 1.00000, 0.00698, 0.00000, 1.00000],
       [0.00000, 1.00000, 0.50000, 0.00000, 1.00000]
    ];
    P.interaction = ScreenTransferFunction.prototype.SeparateChannels;
    P.executeOn(win1.mainView);
    
    // Save STF result
    var stfOutput = outputDir + "STF_Result.xisf";
    win1.saveAs(stfOutput, false, false, false, false);
    Console.writeln("STF result saved: " + stfOutput);
    
    // Save as FITS for inspection
    var stfFits = outputDir + "STF_Result.fit";
    win1.saveAs(stfFits, false, false, false, true);
    Console.writeln("STF result saved as FITS: " + stfFits);
    
    // Method 2: Convert STF to Histogram Transformation
    Console.writeln("\n=== Method 2: Histogram Transformation ===");
    var win2 = win.createPreview();
    win2.mainView.id = "HT_Test";
    
    // Convert STF parameters to Histogram Transformation
    var HT = new HistogramTransformation;
    HT.H = [
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Red
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Green  
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Blue
        [0.00000, 0.50000, 1.00000, 0.00000, 1.00000]  // Luminance
    ];
    HT.executeOn(win2.mainView);
    
    // Save HT result
    var htOutput = outputDir + "HT_Result.xisf";
    win2.saveAs(htOutput, false, false, false, false);
    Console.writeln("HT result saved: " + htOutput);
    
    // Save as FITS for inspection
    var htFits = outputDir + "HT_Result.fit";
    win2.saveAs(htFits, false, false, false, true);
    Console.writeln("HT result saved as FITS: " + htFits);
    
    Console.writeln("\n=== Test Complete ===");
    Console.writeln("Compare the results:");
    Console.writeln("- Original: " + inputFile);
    Console.writeln("- STF Method: " + stfOutput);
    Console.writeln("- HT Method: " + htOutput);
    
} catch (error) {
    Console.criticalln("Error: " + error.message);
}

})();
