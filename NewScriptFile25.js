function APIKeyDialog() {
    this.__base__ = Dialog;
    this.__base__();

    this.windowTitle = "Astrometry.net API Key Required";
    this.apiKey = "";

    this.sizer = new VerticalSizer;
    this.sizer.margin = 15;
    this.sizer.spacing = 10;

    // Info text - Fixed string concatenation
    var infoLabel = new Label(this);
    infoLabel.useRichText = true;
    infoLabel.text = "<b>Astrometry.net API Key Required</b><br/><br/>" +
                     "No astrometric solution found in the image. To perform blind plate solving,<br/>" +
                     "please enter your astrometry.net API key.<br/><br/>" +
                     "If you don't have one, visit: nova.astrometry.net<br/>" +
                     "and create a free account to get your API key.";
    infoLabel.wordWrapping = true;
    infoLabel.minWidth = 400;
    this.sizer.add(infoLabel);

    // API Key input
    var keyLabel = new Label(this);
    keyLabel.text = "API Key:";
    this.sizer.add(keyLabel);

    this.keyEdit = new Edit(this);
    this.keyEdit.text = "kkjdojeiwunqowgv"; // Pre-fill with user's key
    this.keyEdit.minWidth = 400;
    this.keyEdit.onTextUpdated = function(text) {
        this.dialog.apiKey = text;
    };
    this.sizer.add(this.keyEdit);

    // Buttons
    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var okButton = new PushButton(this);
    okButton.text = "OK";
    okButton.defaultButton = true;
    okButton.onClick = function() {
        this.dialog.apiKey = this.dialog.keyEdit.text;
        if (this.dialog.apiKey.trim().length === 0) {
            (new MessageBox("Please enter your API key.", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        this.dialog.ok();
    };

    var cancelButton = new PushButton(this);
    cancelButton.text = "Skip Solving";
    cancelButton.onClick = function() {
        this.dialog.cancel();
    };

    buttonRow.add(okButton);
    buttonRow.add(cancelButton);
    this.sizer.add(buttonRow);

    this.adjustToContents();
}
