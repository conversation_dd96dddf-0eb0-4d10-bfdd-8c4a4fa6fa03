/*
 * PixInsight Preprocessing Parameter Manager & Launcher
 * Unified interface for WBPP and AutoIntegrate with parameter persistence
 * Integrates with Utah Masterclass Post-Integration Pipeline
 */

#feature-id    PreprocessingLauncher > PreprocessingLauncherScript

#feature-icon  @script_icons_dir/PreprocessingLauncherScript.svg

#feature-info  PixInsight Preprocessing Parameter Manager & Launcher<br/>\
   <br/>\
   Unified interface for WBPP and AutoIntegrate preprocessing workflows.<br/>\
   Features:<br/>\
   - Parameter templates and persistence<br/>\
   - Auto-detection of calibration and light frames<br/>\
   - Support for both OSC and LRGB workflows<br/>\
   - Integration with post-processing pipeline<br/>\
   - Batch processing multiple targets<br/>\
   <br/>\
   Supports: WBPP, AutoIntegrate, and custom workflows<br/>\
   Copyright &copy; 2024 Utah Masterclass

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

(function(){

// ============================================================================
// CONFIGURATION AND GLOBALS
// ============================================================================

var VERSION = "1.0.1";
var TITLE = "Preprocessing Parameter Manager";

// Platform Detection
var isWindows = (CoreApplication.platform == "MSWindows");
var isMacOS = (CoreApplication.platform == "MacOSX");
var isLinux = (CoreApplication.platform == "Linux");

// Force Windows detection if needed
if (!isWindows && !isMacOS && !isLinux && File.directoryExists("C:/Program Files")) {
    isWindows = true;
    isMacOS = false;
    isLinux = false;
}

// Default paths
var DEFAULT_PATHS = {
    lights: isWindows ? "C:/AstroImages/Lights" : File.homeDirectory + "/AstroImages/Lights",
    darks: isWindows ? "C:/AstroImages/Darks" : File.homeDirectory + "/AstroImages/Darks",
    flats: isWindows ? "C:/AstroImages/Flats" : File.homeDirectory + "/AstroImages/Flats",
    bias: isWindows ? "C:/AstroImages/Bias" : File.homeDirectory + "/AstroImages/Bias",
    output: isWindows ? "C:/AstroImages/Output" : File.homeDirectory + "/AstroImages/Output"
};

// Supported file extensions
var SUPPORTED_EXTENSIONS = [".fit", ".fits", ".fts", ".xisf", ".cr2", ".cr3", ".nef", ".arw", ".dng"];

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// Custom Object.assign replacement for PixInsight compatibility
function cloneObject(target, source) {
    var result = {};

    // Copy target properties first
    for (var key in target) {
        if (target.hasOwnProperty(key)) {
            result[key] = target[key];
        }
    }

    // Copy source properties (overwriting target if they exist)
    if (source) {
        for (var key in source) {
            if (source.hasOwnProperty(key)) {
                result[key] = source[key];
            }
        }
    }

    return result;
}

// Deep copy function for nested objects
function deepCopyObject(obj) {
    if (obj === null || typeof obj !== "object") {
        return obj;
    }

    var copy = {};
    for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
            if (typeof obj[key] === "object" && obj[key] !== null) {
                copy[key] = deepCopyObject(obj[key]);
            } else {
                copy[key] = obj[key];
            }
        }
    }

    return copy;
}

// Parameter Templates
var PARAMETER_TEMPLATES = {
    // High Quality DSO Template
    dso_hq: {
        name: "DSO High Quality",
        description: "High quality deep space object processing",
        cosmicRayRejection: true,
        stackingMode: "Average",
        rejectionAlgorithm: "Winsorized Sigma Clipping",
        rejectionLow: 4.0,
        rejectionHigh: 2.0,
        pixelRejection: true,
        useSTF: true,
        generateDrizzleData: false,
        localNormalization: true,
        adaptiveNormalization: false
    },

    // Fast Processing Template
    dso_fast: {
        name: "DSO Fast",
        description: "Faster processing for testing",
        cosmicRayRejection: false,
        stackingMode: "Average",
        rejectionAlgorithm: "Linear Fit Clipping",
        rejectionLow: 3.0,
        rejectionHigh: 3.0,
        pixelRejection: false,
        useSTF: true,
        generateDrizzleData: false,
        localNormalization: false,
        adaptiveNormalization: false
    },

    // Planetary Template
    planetary: {
        name: "Planetary",
        description: "High frame rate planetary imaging",
        cosmicRayRejection: false,
        stackingMode: "Average",
        rejectionAlgorithm: "Percentile Clipping",
        rejectionLow: 0.2,
        rejectionHigh: 0.1,
        pixelRejection: false,
        useSTF: false,
        generateDrizzleData: false,
        localNormalization: false,
        adaptiveNormalization: false
    }
};

// ============================================================================
// FILE SYSTEM UTILITIES
// ============================================================================

function normalizePath(path) {
    var result = "";
    for (var i = 0; i < path.length; i++) {
        if (path.charAt(i) == "\\") {
            result += "/";
        } else {
            result += path.charAt(i);
        }
    }
    return result;
}

function findFilesInDirectory(directory, extensions) {
    var files = [];
    if (!File.directoryExists(directory)) return files;

    var ff = new FileFind();
    if (ff.begin(directory + "/*")) {
        do {
            if (!ff.isDirectory) {
                var fileName = ff.name.toLowerCase();
                for (var i = 0; i < extensions.length; i++) {
                    if (fileName.indexOf(extensions[i]) === fileName.length - extensions[i].length) {
                        files.push(directory + "/" + ff.name);
                        break;
                    }
                }
            }
        } while (ff.next());
    }
    ff.end();
    return files;
}

function detectFrameType(filePath) {
    var fileName = File.extractName(filePath).toLowerCase();

    // Detect frame types based on filename patterns
    if (fileName.indexOf("bias") >= 0 || fileName.indexOf("offset") >= 0) return "bias";
    if (fileName.indexOf("dark") >= 0) return "dark";
    if (fileName.indexOf("flat") >= 0) return "flat";
    if (fileName.indexOf("light") >= 0) return "light";

    // Try to detect filter names for lights
    if (fileName.indexOf("red") >= 0 || fileName.indexOf("filter_r") >= 0) return "light_r";
    if (fileName.indexOf("green") >= 0 || fileName.indexOf("filter_g") >= 0) return "light_g";
    if (fileName.indexOf("blue") >= 0 || fileName.indexOf("filter_b") >= 0) return "light_b";
    if (fileName.indexOf("luminance") >= 0 || fileName.indexOf("filter_l") >= 0) return "light_l";
    if (fileName.indexOf("ha") >= 0 || fileName.indexOf("h_alpha") >= 0) return "light_ha";
    if (fileName.indexOf("oiii") >= 0 || fileName.indexOf("o3") >= 0) return "light_oiii";
    if (fileName.indexOf("sii") >= 0 || fileName.indexOf("s2") >= 0) return "light_sii";

    return "light"; // Default to light frame
}

function autoDetectFrames(baseDirectory) {
    var result = {
        lights: [],
        darks: [],
        flats: [],
        bias: [],
        byFilter: {}
    };

    // Scan base directory and subdirectories
    function scanDirectory(dir, depth) {
        if (depth > 3) return; // Limit recursion depth

        var files = findFilesInDirectory(dir, SUPPORTED_EXTENSIONS);
        for (var i = 0; i < files.length; i++) {
            var frameType = detectFrameType(files[i]);

            if (frameType === "bias") result.bias.push(files[i]);
            else if (frameType === "dark") result.darks.push(files[i]);
            else if (frameType === "flat") result.flats.push(files[i]);
            else if (frameType.indexOf("light") === 0) {
                result.lights.push(files[i]);

                // Organize by filter
                var filter = frameType.substring(6); // Remove "light_" prefix
                if (filter) {
                    if (!result.byFilter[filter]) result.byFilter[filter] = [];
                    result.byFilter[filter].push(files[i]);
                }
            }
        }

        // Scan subdirectories
        var ff = new FileFind();
        if (ff.begin(dir + "/*")) {
            do {
                if (ff.isDirectory && ff.name !== "." && ff.name !== "..") {
                    scanDirectory(dir + "/" + ff.name, depth + 1);
                }
            } while (ff.next());
        }
        ff.end();
    }

    scanDirectory(baseDirectory, 0);
    return result;
}

// ============================================================================
// PREPROCESSING ENGINE INTERFACES
// ============================================================================

function isWBPPAvailable() {
    // Check if WBPP is available by trying to create the process
    try {
        var processId = "WeightedBatchPreProcessing";
        var P = ProcessInstance.fromIcon(processId);
        if (P && !P.isNull) {
            return true;
        }

        // Try alternative class names
        var alternativeNames = [
            "WeightedBatchPreprocessing",
            "WBPP",
            "BatchPreprocessing"
        ];

        for (var i = 0; i < alternativeNames.length; i++) {
            try {
                P = ProcessInstance.fromIcon(alternativeNames[i]);
                if (P && !P.isNull) {
                    return true;
                }
            } catch (e) {
                // Continue trying
            }
        }

        return false;
    } catch (e) {
        Console.warningln("WBPP availability check failed: " + e.message);
        return false;
    }
}

function isAutoIntegrateAvailable() {
    // Check for AutoIntegrate script in common locations
    try {
        var possiblePaths = [
            File.homeDirectory + "/PixInsight/src/scripts/AutoIntegrate",
            "/Applications/PixInsight/src/scripts/AutoIntegrate",
            "/usr/local/PixInsight/src/scripts/AutoIntegrate",
            "C:/Program Files/PixInsight/src/scripts/AutoIntegrate"
        ];

        for (var i = 0; i < possiblePaths.length; i++) {
            if (File.directoryExists(possiblePaths[i])) {
                return true;
            }
        }
        return false;
    } catch (e) {
        return false;
    }
}

function launchWBPPManually(parameters, frameData) {
    Console.writeln("=== Manual WBPP Launch Instructions ===");
    Console.writeln("");
    Console.writeln("Since WBPP automation failed, please follow these manual steps:");
    Console.writeln("");
    Console.writeln("1. Open WBPP from: Script > Batch Processing > WeightedBatchPreProcessing");
    Console.writeln("");
    Console.writeln("2. Configure the following settings:");
    Console.writeln("   • Cosmic Ray Rejection: " + (parameters.cosmicRayRejection ? "Enabled" : "Disabled"));
    Console.writeln("   • Rejection Algorithm: " + parameters.rejectionAlgorithm);
    Console.writeln("   • Rejection Low: " + parameters.rejectionLow);
    Console.writeln("   • Rejection High: " + parameters.rejectionHigh);
    Console.writeln("   • Local Normalization: " + (parameters.localNormalization ? "Enabled" : "Disabled"));
    Console.writeln("   • Generate Drizzle Data: " + (parameters.generateDrizzleData ? "Enabled" : "Disabled"));
    Console.writeln("");
    Console.writeln("3. Add the following frames:");
    Console.writeln("   • Light frames (" + frameData.lights.length + "):");
    for (var i = 0; i < Math.min(frameData.lights.length, 5); i++) {
        Console.writeln("     " + frameData.lights[i]);
    }
    if (frameData.lights.length > 5) {
        Console.writeln("     ... and " + (frameData.lights.length - 5) + " more");
    }

    if (frameData.darks.length > 0) {
        Console.writeln("   • Dark frames (" + frameData.darks.length + "):");
        for (var i = 0; i < Math.min(frameData.darks.length, 3); i++) {
            Console.writeln("     " + frameData.darks[i]);
        }
        if (frameData.darks.length > 3) {
            Console.writeln("     ... and " + (frameData.darks.length - 3) + " more");
        }
    }

    if (frameData.flats.length > 0) {
        Console.writeln("   • Flat frames (" + frameData.flats.length + "):");
        for (var i = 0; i < Math.min(frameData.flats.length, 3); i++) {
            Console.writeln("     " + frameData.flats[i]);
        }
        if (frameData.flats.length > 3) {
            Console.writeln("     ... and " + (frameData.flats.length - 3) + " more");
        }
    }

    if (frameData.bias.length > 0) {
        Console.writeln("   • Bias frames (" + frameData.bias.length + "):");
        for (var i = 0; i < Math.min(frameData.bias.length, 3); i++) {
            Console.writeln("     " + frameData.bias[i]);
        }
        if (frameData.bias.length > 3) {
            Console.writeln("     ... and " + (frameData.bias.length - 3) + " more");
        }
    }

    Console.writeln("");
    Console.writeln("4. Set output directory to: " + parameters.outputDirectory);
    Console.writeln("");
    Console.writeln("5. Click 'Global Apply' to start processing");
    Console.writeln("");
    Console.writeln("========================================");

    return true;
}

function runWBPP(parameters, frameData) {
    Console.writeln("=== Attempting WBPP Launch ===");

    // Since direct WBPP automation is complex in PixInsight, we'll provide manual instructions
    // and try to open WBPP for the user

    try {
        // Try to launch WBPP script if possible
        Console.writeln("Attempting to open WBPP script interface...");

        // This won't work in most cases, but we try anyway
        var script = null;
        try {
            // Try to find and execute WBPP script
            executeScript("WeightedBatchPreProcessing");
        } catch (e) {
            // Script execution failed, fall back to manual instructions
            Console.writeln("Direct WBPP launch failed: " + e.message);
        }

        // Provide manual instructions regardless
        return launchWBPPManually(parameters, frameData);

    } catch (e) {
        Console.criticalln("❌ WBPP launch failed: " + e.message);

        // Always provide manual instructions as fallback
        return launchWBPPManually(parameters, frameData);
    }
}

function executeScript(scriptName) {
    // Try to execute a script by name
    // This is a placeholder - actual implementation would depend on PixInsight's script execution API
    throw new Error("Script execution not implemented for: " + scriptName);
}

function runAutoIntegrate(parameters, frameData) {
    Console.writeln("=== AutoIntegrate Configuration ===");

    try {
        // Since AutoIntegrate automation is complex, provide configuration instructions
        Console.writeln("AutoIntegrate parameters configured:");
        Console.writeln("  • Input lights path: " + (frameData.lights.length > 0 ? File.extractDirectory(frameData.lights[0]) : "N/A"));
        Console.writeln("  • Input darks path: " + (frameData.darks.length > 0 ? File.extractDirectory(frameData.darks[0]) : "N/A"));
        Console.writeln("  • Input flats path: " + (frameData.flats.length > 0 ? File.extractDirectory(frameData.flats[0]) : "N/A"));
        Console.writeln("  • Input bias path: " + (frameData.bias.length > 0 ? File.extractDirectory(frameData.bias[0]) : "N/A"));
        Console.writeln("  • Output directory: " + parameters.outputDirectory);
        Console.writeln("  • Integration rejection: " + parameters.rejectionAlgorithm);
        Console.writeln("  • Integration rejection low: " + parameters.rejectionLow);
        Console.writeln("  • Integration rejection high: " + parameters.rejectionHigh);
        Console.writeln("  • Integration combination: " + parameters.stackingMode);
        Console.writeln("  • Cosmic ray rejection: " + parameters.cosmicRayRejection);
        Console.writeln("  • Local normalization: " + parameters.localNormalization);
        Console.writeln("  • Generate drizzle data: " + parameters.generateDrizzleData);

        Console.writeln("");
        Console.writeln("Please manually configure AutoIntegrate with these parameters and run it.");
        Console.writeln("AutoIntegrate can be found in: Script > Batch Processing > AutoIntegrate");

        return true;

    } catch (e) {
        Console.criticalln("❌ AutoIntegrate configuration failed: " + e.message);
        return false;
    }
}

// ============================================================================
// PARAMETER MANAGEMENT
// ============================================================================

function saveParameterSet(name, parameters) {
    var settingsKey = "PreprocessingLauncher/ParameterSets/" + name;

    for (var key in parameters) {
        if (typeof parameters[key] === "boolean") {
            Settings.write(settingsKey + "/" + key, DataType_Boolean, parameters[key]);
        } else if (typeof parameters[key] === "number") {
            Settings.write(settingsKey + "/" + key, DataType_Double, parameters[key]);
        } else {
            Settings.write(settingsKey + "/" + key, DataType_String, parameters[key].toString());
        }
    }

    Console.writeln("✅ Parameter set '" + name + "' saved");
}

function loadParameterSet(name) {
    var settingsKey = "PreprocessingLauncher/ParameterSets/" + name;
    var parameters = {};

    // Try to load each parameter
    var templateKeys = Object.keys ? Object.keys(PARAMETER_TEMPLATES.dso_hq) : getObjectKeys(PARAMETER_TEMPLATES.dso_hq);
    for (var i = 0; i < templateKeys.length; i++) {
        var key = templateKeys[i];
        try {
            var settingKey = settingsKey + "/" + key;
            if (typeof PARAMETER_TEMPLATES.dso_hq[key] === "boolean") {
                parameters[key] = Settings.read(settingKey, DataType_Boolean);
            } else if (typeof PARAMETER_TEMPLATES.dso_hq[key] === "number") {
                parameters[key] = Settings.read(settingKey, DataType_Double);
            } else {
                parameters[key] = Settings.read(settingKey, DataType_String);
            }
        } catch (e) {
            // Use default if setting doesn't exist
            parameters[key] = PARAMETER_TEMPLATES.dso_hq[key];
        }
    }

    return parameters;
}

function getObjectKeys(obj) {
    var keys = [];
    for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
            keys.push(key);
        }
    }
    return keys;
}

function getAvailableParameterSets() {
    // This would scan the settings for available parameter sets
    // For now, return the built-in templates
    return getObjectKeys(PARAMETER_TEMPLATES);
}

// ============================================================================
// MAIN GUI
// ============================================================================

function PreprocessingLauncherDialog() {
    this.__base__ = Dialog;
    this.__base__();

    var self = this;

    // State variables - using deepCopyObject instead of Object.assign
    this.parameters = deepCopyObject(PARAMETER_TEMPLATES.dso_hq);
    this.frameData = {
        lights: [],
        darks: [],
        flats: [],
        bias: [],
        byFilter: {}
    };
    this.selectedEngine = "WBPP";

    // Window properties
    this.windowTitle = TITLE + " v" + VERSION;
    this.adjustToContents();

    // Create GUI
    this.createGUI();
}

PreprocessingLauncherDialog.prototype = new Dialog;

PreprocessingLauncherDialog.prototype.createGUI = function() {
    var self = this;

    this.sizer = new VerticalSizer;
    this.sizer.margin = 10;
    this.sizer.spacing = 8;

    // Title
    var titleLabel = new Label(this);
    titleLabel.useRichText = true;
    titleLabel.text = "<b>" + TITLE + " v" + VERSION + "</b>";
    titleLabel.textAlignment = TextAlign_Center;
    this.sizer.add(titleLabel);

    // Engine Selection
    this.createEngineSelection();

    // Frame Detection Section
    this.createFrameDetection();

    // Parameter Templates Section
    this.createParameterTemplates();

    // Processing Parameters Section
    this.createProcessingParameters();

    // Output Section
    this.createOutputSection();

    // Frame Summary
    this.createFrameSummary();

    // Action Buttons
    this.createActionButtons();
};

PreprocessingLauncherDialog.prototype.createEngineSelection = function() {
    var engineGroup = new GroupBox(this);
    engineGroup.title = "Preprocessing Engine";
    engineGroup.sizer = new VerticalSizer;
    engineGroup.sizer.margin = 8;
    engineGroup.sizer.spacing = 6;

    var engineRow = new HorizontalSizer;
    engineRow.spacing = 12;

    var self = this;

    this.wbppRadio = new RadioButton(this);
    this.wbppRadio.text = "WBPP (Manual Launch)";
    this.wbppRadio.checked = true;
    this.wbppRadio.enabled = true; // Always available for manual launch
    this.wbppRadio.onCheck = function(checked) {
        if (checked) self.selectedEngine = "WBPP";
    };

    this.autoIntegrateRadio = new RadioButton(this);
    this.autoIntegrateRadio.text = "AutoIntegrate (Config Only)";
    this.autoIntegrateRadio.checked = false;
    this.autoIntegrateRadio.enabled = true; // Always available for configuration
    this.autoIntegrateRadio.onCheck = function(checked) {
        if (checked) self.selectedEngine = "AutoIntegrate";
    };

    var statusLabel = new Label(this);
    statusLabel.text = "Note: This script provides configuration and manual launch instructions";
    statusLabel.textAlignment = TextAlign_Left;

    engineRow.add(this.wbppRadio);
    engineRow.add(this.autoIntegrateRadio);
    engineRow.addStretch();

    engineGroup.sizer.add(engineRow);
    engineGroup.sizer.add(statusLabel);
    this.sizer.add(engineGroup);
};

PreprocessingLauncherDialog.prototype.createFrameDetection = function() {
    var frameGroup = new GroupBox(this);
    frameGroup.title = "Frame Detection";
    frameGroup.sizer = new VerticalSizer;
    frameGroup.sizer.margin = 8;
    frameGroup.sizer.spacing = 6;

    var self = this;

    // Auto-detection row
    var autoRow = new HorizontalSizer;
    autoRow.spacing = 6;

    var autoLabel = new Label(this);
    autoLabel.text = "Base Directory:";
    autoLabel.minWidth = 120;
    autoLabel.textAlignment = TextAlign_Right | TextAlign_VertCenter;

    this.baseDirectoryEdit = new Edit(this);
    this.baseDirectoryEdit.text = DEFAULT_PATHS.lights;
    this.baseDirectoryEdit.minWidth = 400;

    var browseDirButton = new PushButton(this);
    browseDirButton.text = "Browse...";
    browseDirButton.icon = this.scaledResource(":/icons/select-file.png");
    browseDirButton.onClick = function() {
        var dialog = new GetDirectoryDialog;
        dialog.caption = "Select Base Directory for Frame Detection";
        dialog.initialDirectory = self.baseDirectoryEdit.text;

        if (dialog.execute()) {
            self.baseDirectoryEdit.text = normalizePath(dialog.directory);
        }
    };

    var autoDetectButton = new PushButton(this);
    autoDetectButton.text = "Auto-Detect Frames";
    autoDetectButton.icon = this.scaledResource(":/icons/find.png");
    autoDetectButton.onClick = function() {
        self.performAutoDetection();
    };

    autoRow.add(autoLabel);
    autoRow.add(this.baseDirectoryEdit, 100);
    autoRow.add(browseDirButton);
    autoRow.add(autoDetectButton);

    frameGroup.sizer.add(autoRow);
    this.sizer.add(frameGroup);
};

PreprocessingLauncherDialog.prototype.createParameterTemplates = function() {
    var templateGroup = new GroupBox(this);
    templateGroup.title = "Parameter Templates";
    templateGroup.sizer = new VerticalSizer;
    templateGroup.sizer.margin = 8;
    templateGroup.sizer.spacing = 6;

    var self = this;

    var templateRow = new HorizontalSizer;
    templateRow.spacing = 6;

    var templateLabel = new Label(this);
    templateLabel.text = "Template:";
    templateLabel.minWidth = 120;
    templateLabel.textAlignment = TextAlign_Right | TextAlign_VertCenter;

    this.templateCombo = new ComboBox(this);
    for (var templateName in PARAMETER_TEMPLATES) {
        var template = PARAMETER_TEMPLATES[templateName];
        this.templateCombo.addItem(template.name + " - " + template.description);
    }
    this.templateCombo.currentItem = 0;
    this.templateCombo.onItemSelected = function(index) {
        var templateNames = getObjectKeys(PARAMETER_TEMPLATES);
        var selectedTemplate = templateNames[index];
        self.parameters = deepCopyObject(PARAMETER_TEMPLATES[selectedTemplate]);
        self.updateParameterControls();
    };

    var saveTemplateButton = new PushButton(this);
    saveTemplateButton.text = "Save Current as Template";
    saveTemplateButton.onClick = function() {
        self.saveCurrentTemplate();
    };

    templateRow.add(templateLabel);
    templateRow.add(this.templateCombo, 100);
    templateRow.add(saveTemplateButton);

    templateGroup.sizer.add(templateRow);
    this.sizer.add(templateGroup);
};

PreprocessingLauncherDialog.prototype.createProcessingParameters = function() {
    var paramGroup = new GroupBox(this);
    paramGroup.title = "Processing Parameters";
    paramGroup.sizer = new VerticalSizer;
    paramGroup.sizer.margin = 8;
    paramGroup.sizer.spacing = 6;

    var self = this;

    // Integration parameters
    var integrationRow1 = new HorizontalSizer;
    integrationRow1.spacing = 12;

    this.cosmicRayCheck = new CheckBox(this);
    this.cosmicRayCheck.text = "Cosmic Ray Rejection";
    this.cosmicRayCheck.checked = this.parameters.cosmicRayRejection;
    this.cosmicRayCheck.onCheck = function(checked) {
        self.parameters.cosmicRayRejection = checked;
    };

    this.localNormCheck = new CheckBox(this);
    this.localNormCheck.text = "Local Normalization";
    this.localNormCheck.checked = this.parameters.localNormalization;
    this.localNormCheck.onCheck = function(checked) {
        self.parameters.localNormalization = checked;
    };

    this.drizzleCheck = new CheckBox(this);
    this.drizzleCheck.text = "Generate Drizzle Data";
    this.drizzleCheck.checked = this.parameters.generateDrizzleData;
    this.drizzleCheck.onCheck = function(checked) {
        self.parameters.generateDrizzleData = checked;
    };

    integrationRow1.add(this.cosmicRayCheck);
    integrationRow1.add(this.localNormCheck);
    integrationRow1.add(this.drizzleCheck);
    integrationRow1.addStretch();

    // Rejection parameters
    var rejectionRow = new HorizontalSizer;
    rejectionRow.spacing = 6;

    var rejectionLabel = new Label(this);
    rejectionLabel.text = "Rejection:";
    rejectionLabel.minWidth = 80;

    this.rejectionCombo = new ComboBox(this);
    this.rejectionCombo.addItem("Linear Fit Clipping");
    this.rejectionCombo.addItem("Sigma Clipping");
    this.rejectionCombo.addItem("Winsorized Sigma Clipping");
    this.rejectionCombo.addItem("Percentile Clipping");
    this.rejectionCombo.currentItem = 2; // Default to Winsorized Sigma Clipping
    this.rejectionCombo.onItemSelected = function(index) {
        var algorithms = ["Linear Fit Clipping", "Sigma Clipping", "Winsorized Sigma Clipping", "Percentile Clipping"];
        self.parameters.rejectionAlgorithm = algorithms[index];
    };

    var lowLabel = new Label(this);
    lowLabel.text = "Low:";
    this.rejectionLowSpin = new SpinBox(this);
    this.rejectionLowSpin.minValue = 0.1;
    this.rejectionLowSpin.maxValue = 10.0;
    this.rejectionLowSpin.stepSize = 0.1;
    this.rejectionLowSpin.value = this.parameters.rejectionLow;
    this.rejectionLowSpin.onValueUpdated = function(value) {
        self.parameters.rejectionLow = value;
    };

    var highLabel = new Label(this);
    highLabel.text = "High:";
    this.rejectionHighSpin = new SpinBox(this);
    this.rejectionHighSpin.minValue = 0.1;
    this.rejectionHighSpin.maxValue = 10.0;
    this.rejectionHighSpin.stepSize = 0.1;
    this.rejectionHighSpin.value = this.parameters.rejectionHigh;
    this.rejectionHighSpin.onValueUpdated = function(value) {
        self.parameters.rejectionHigh = value;
    };

    rejectionRow.add(rejectionLabel);
    rejectionRow.add(this.rejectionCombo);
    rejectionRow.add(lowLabel);
    rejectionRow.add(this.rejectionLowSpin);
    rejectionRow.add(highLabel);
    rejectionRow.add(this.rejectionHighSpin);
    rejectionRow.addStretch();

    paramGroup.sizer.add(integrationRow1);
    paramGroup.sizer.add(rejectionRow);
    this.sizer.add(paramGroup);
};

PreprocessingLauncherDialog.prototype.createOutputSection = function() {
    var outputGroup = new GroupBox(this);
    outputGroup.title = "Output";
    outputGroup.sizer = new VerticalSizer;
    outputGroup.sizer.margin = 8;
    outputGroup.sizer.spacing = 6;

    var self = this;

    var outputRow = new HorizontalSizer;
    outputRow.spacing = 6;

    var outputLabel = new Label(this);
    outputLabel.text = "Output Directory:";
    outputLabel.minWidth = 120;
    outputLabel.textAlignment = TextAlign_Right | TextAlign_VertCenter;

    this.outputDirectoryEdit = new Edit(this);
    this.outputDirectoryEdit.text = DEFAULT_PATHS.output;
    this.outputDirectoryEdit.minWidth = 400;

    var browseOutputButton = new PushButton(this);
    browseOutputButton.text = "Browse...";
    browseOutputButton.icon = this.scaledResource(":/icons/select-file.png");
    browseOutputButton.onClick = function() {
        var dialog = new GetDirectoryDialog;
        dialog.caption = "Select Output Directory";
        dialog.initialDirectory = self.outputDirectoryEdit.text;

        if (dialog.execute()) {
            self.outputDirectoryEdit.text = normalizePath(dialog.directory);
        }
    };

    outputRow.add(outputLabel);
    outputRow.add(this.outputDirectoryEdit, 100);
    outputRow.add(browseOutputButton);

    // Post-processing chain option
    var chainRow = new HorizontalSizer;
    chainRow.spacing = 6;

    this.chainProcessingCheck = new CheckBox(this);
    this.chainProcessingCheck.text = "Chain to Post-Integration Pipeline after completion";
    this.chainProcessingCheck.checked = false;

    chainRow.add(this.chainProcessingCheck);
    chainRow.addStretch();

    outputGroup.sizer.add(outputRow);
    outputGroup.sizer.add(chainRow);
    this.sizer.add(outputGroup);
};

PreprocessingLauncherDialog.prototype.createFrameSummary = function() {
    var summaryGroup = new GroupBox(this);
    summaryGroup.title = "Frame Summary";
    summaryGroup.sizer = new VerticalSizer;
    summaryGroup.sizer.margin = 8;
    summaryGroup.sizer.spacing = 6;

    this.frameSummaryLabel = new Label(this);
    this.frameSummaryLabel.text = "No frames detected. Use Auto-Detect or manual selection.";
    this.frameSummaryLabel.textAlignment = TextAlign_Left;

    summaryGroup.sizer.add(this.frameSummaryLabel);
    this.sizer.add(summaryGroup);
};

PreprocessingLauncherDialog.prototype.createActionButtons = function() {
    var self = this;

    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var previewButton = new PushButton(this);
    previewButton.text = "Preview Parameters";
    previewButton.onClick = function() {
        self.previewParameters();
    };

    var startButton = new PushButton(this);
    startButton.text = "Generate Instructions";
    startButton.icon = this.scaledResource(":/icons/ok.png");
    startButton.defaultButton = true;
    startButton.onClick = function() {
        self.startProcessing();
    };

    var cancelButton = new PushButton(this);
    cancelButton.text = "Cancel";
    cancelButton.icon = this.scaledResource(":/icons/cancel.png");
    cancelButton.onClick = function() {
        self.cancel();
    };

    buttonRow.add(previewButton);
    buttonRow.add(startButton);
    buttonRow.add(cancelButton);

    this.sizer.add(buttonRow);
};

// ============================================================================
// DIALOG METHODS
// ============================================================================

PreprocessingLauncherDialog.prototype.performAutoDetection = function() {
    var baseDir = this.baseDirectoryEdit.text;
    if (!File.directoryExists(baseDir)) {
        (new MessageBox("Base directory does not exist: " + baseDir,
                       "Error", StdIcon_Error, StdButton_Ok)).execute();
        return;
    }

    Console.writeln("Detecting frames in: " + baseDir);
    this.frameData = autoDetectFrames(baseDir);
    this.updateFrameSummary();

    Console.writeln("Frame detection completed:");
    Console.writeln("  Lights: " + this.frameData.lights.length);
    Console.writeln("  Darks: " + this.frameData.darks.length);
    Console.writeln("  Flats: " + this.frameData.flats.length);
    Console.writeln("  Bias: " + this.frameData.bias.length);

    for (var filter in this.frameData.byFilter) {
        Console.writeln("  " + filter.toUpperCase() + ": " + this.frameData.byFilter[filter].length);
    }
};

PreprocessingLauncherDialog.prototype.updateFrameSummary = function() {
    var summary = "Detected frames:\n";
    summary += "• Lights: " + this.frameData.lights.length + "\n";
    summary += "• Darks: " + this.frameData.darks.length + "\n";
    summary += "• Flats: " + this.frameData.flats.length + "\n";
    summary += "• Bias: " + this.frameData.bias.length + "\n";

    if (getObjectKeys(this.frameData.byFilter).length > 0) {
        summary += "\nBy Filter:\n";
        for (var filter in this.frameData.byFilter) {
            summary += "• " + filter.toUpperCase() + ": " + this.frameData.byFilter[filter].length + "\n";
        }
    }

    this.frameSummaryLabel.text = summary;
};

PreprocessingLauncherDialog.prototype.updateParameterControls = function() {
    this.cosmicRayCheck.checked = this.parameters.cosmicRayRejection;
    this.localNormCheck.checked = this.parameters.localNormalization;
    this.drizzleCheck.checked = this.parameters.generateDrizzleData;
    this.rejectionLowSpin.value = this.parameters.rejectionLow;
    this.rejectionHighSpin.value = this.parameters.rejectionHigh;
};

PreprocessingLauncherDialog.prototype.previewParameters = function() {
    var preview = "Processing Parameters:\n\n";
    preview += "Engine: " + this.selectedEngine + "\n";
    preview += "Output Directory: " + this.outputDirectoryEdit.text + "\n\n";

    preview += "Integration Settings:\n";
    preview += "• Stacking Mode: " + this.parameters.stackingMode + "\n";
    preview += "• Rejection Algorithm: " + this.parameters.rejectionAlgorithm + "\n";
    preview += "• Rejection Low: " + this.parameters.rejectionLow + "\n";
    preview += "• Rejection High: " + this.parameters.rejectionHigh + "\n";
    preview += "• Cosmic Ray Rejection: " + (this.parameters.cosmicRayRejection ? "Yes" : "No") + "\n";
    preview += "• Local Normalization: " + (this.parameters.localNormalization ? "Yes" : "No") + "\n";
    preview += "• Generate Drizzle Data: " + (this.parameters.generateDrizzleData ? "Yes" : "No") + "\n\n";

    preview += "Frame Count:\n";
    preview += "• Lights: " + this.frameData.lights.length + "\n";
    preview += "• Darks: " + this.frameData.darks.length + "\n";
    preview += "• Flats: " + this.frameData.flats.length + "\n";
    preview += "• Bias: " + this.frameData.bias.length + "\n";

    (new MessageBox(preview, "Parameter Preview", StdIcon_Information, StdButton_Ok)).execute();
};

PreprocessingLauncherDialog.prototype.startProcessing = function() {
    // Validate inputs
    if (this.frameData.lights.length === 0) {
        (new MessageBox("No light frames detected. Please run frame detection first.",
                       "Error", StdIcon_Error, StdButton_Ok)).execute();
        return;
    }

    if (!File.directoryExists(this.outputDirectoryEdit.text)) {
        (new MessageBox("Output directory does not exist: " + this.outputDirectoryEdit.text,
                       "Error", StdIcon_Error, StdButton_Ok)).execute();
        return;
    }

    // Update parameters with current values
    this.parameters.outputDirectory = this.outputDirectoryEdit.text;

    // Close dialog and start processing
    this.ok();

    // Launch selected engine
    var success = false;
    if (this.selectedEngine === "WBPP") {
        success = runWBPP(this.parameters, this.frameData);
    } else if (this.selectedEngine === "AutoIntegrate") {
        success = runAutoIntegrate(this.parameters, this.frameData);
    }

    if (success && this.chainProcessingCheck.checked) {
        Console.writeln("Attempting to chain to post-integration pipeline...");
        // Here you would launch the post-integration pipeline
        // with the output directory from preprocessing
        this.launchPostIntegrationPipeline();
    }
};

PreprocessingLauncherDialog.prototype.saveCurrentTemplate = function() {
    var dialog = new SaveTemplateDialog();
    if (dialog.execute()) {
        var templateName = dialog.templateName;
        saveParameterSet(templateName, this.parameters);
    }
};

PreprocessingLauncherDialog.prototype.launchPostIntegrationPipeline = function() {
    try {
        // Attempt to find and launch the post-integration pipeline
        Console.writeln("Looking for Post-Integration Pipeline script...");

        // You would implement the actual launch logic here
        // For example, if the post-integration script is available as a global function:
        // launchPostIntegrationPipeline(this.parameters.outputDirectory);

        Console.noteln("Post-integration pipeline chaining not yet implemented.");
        Console.noteln("Please manually run the post-integration pipeline with output directory:");
        Console.noteln(this.parameters.outputDirectory);

    } catch (e) {
        Console.warningln("Could not chain to post-integration pipeline: " + e.message);
    }
};

// ============================================================================
// SAVE TEMPLATE DIALOG
// ============================================================================

function SaveTemplateDialog() {
    this.__base__ = Dialog;
    this.__base__();

    this.windowTitle = "Save Template";
    this.templateName = "";

    this.sizer = new VerticalSizer;
    this.sizer.margin = 10;
    this.sizer.spacing = 8;

    var nameLabel = new Label(this);
    nameLabel.text = "Template Name:";
    this.sizer.add(nameLabel);

    this.nameEdit = new Edit(this);
    this.nameEdit.minWidth = 300;
    this.nameEdit.onTextUpdated = function(text) {
        this.dialog.templateName = text;
    };
    this.sizer.add(this.nameEdit);

    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var okButton = new PushButton(this);
    okButton.text = "OK";
    okButton.defaultButton = true;
    okButton.onClick = function() {
        if (this.dialog.templateName.trim() === "") {
            (new MessageBox("Please enter a template name.", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        this.dialog.ok();
    };

    var cancelButton = new PushButton(this);
    cancelButton.text = "Cancel";
    cancelButton.onClick = function() {
        this.dialog.cancel();
    };

    buttonRow.add(okButton);
    buttonRow.add(cancelButton);
    this.sizer.add(buttonRow);

    this.adjustToContents();
}

SaveTemplateDialog.prototype = new Dialog;

// ============================================================================
// MAIN ENTRY POINT
// ============================================================================

function main() {
    Console.show();
    Console.writeln("=== " + TITLE + " v" + VERSION + " ===");

    var dialog = new PreprocessingLauncherDialog();
    dialog.execute();
}

// Launch the script
main();

})(); // IIFE
