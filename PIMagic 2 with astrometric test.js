/*
 * Post-Integration Pipeline — RGB & Monochrome (Cross-Platform)
 * WORK8 VERSION - CROSS-PLATFORM COMPATIBLE - FIXED PLATFORM DETECTION
 * - Works on both Windows and macOS
 * - Configurable paths section at the beginning
 * - Uses PixInsight BatchFormatConversion algorithm
 * - Auto-detects platform and sets appropriate defaults
 */

// Always include these first to avoid strict mode issues
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/ColorSpace.jsh>
#include <pjsr/SampleType.jsh>
#include <pjsr/DataType.jsh>
// Add this after the existing includes at the top
#include <pjsr/NumericControl.jsh>

// Add these new functions for astrometry.net integration

// -------------------- Astrometry.net Integration --------------------

function getAstrometryAPIKey() {
    var settingsKey = "PostIntegrationPipeline/AstrometryAPIKey";
    try {
        var savedKey = Settings.read(settingsKey, DataType_String);
        if (savedKey && savedKey.length > 0) {
            return savedKey;
        }
    } catch (e) {
        // No saved key
    }

    // Ask user for API key
    var dialog = new APIKeyDialog();
    if (dialog.execute()) {
        var apiKey = dialog.apiKey;
        if (apiKey && apiKey.length > 0) {
            // Save the API key
            Settings.write(settingsKey, DataType_String, apiKey);
            return apiKey;
        }
    }

    return null;
}

function APIKeyDialog() {
    this.__base__ = Dialog;
    this.__base__();

    this.windowTitle = "Astrometry.net API Key Required";
    this.apiKey = "";

    this.sizer = new VerticalSizer;
    this.sizer.margin = 15;
    this.sizer.spacing = 10;

    // Info text
    var infoLabel = new Label(this);
    infoLabel.useRichText = true;
    infoLabel.text = "<b>Astrometry.net API Key Required</b><br/><br/>" +
                     "No astrometric solution found in the image. To perform blind plate solving,<br/>" +
                     "please enter your astrometry.net API key.<br/><br/>" +
                     "If you don't have one, visit: <a href='https://nova.astrometry.net/'>nova.astrometry.net</a><br/>" +
                     "and create a free account to get your API key.";
    infoLabel.wordWrapping = true;
    infoLabel.minWidth = 400;
    this.sizer.add(infoLabel);

    // API Key input
    var keyLabel = new Label(this);
    keyLabel.text = "API Key:";
    this.sizer.add(keyLabel);

    this.keyEdit = new Edit(this);
    this.keyEdit.text = "kkjdojeiwunqowgv"; // Pre-fill with user's key
    this.keyEdit.minWidth = 400;
    this.keyEdit.onTextUpdated = function(text) {
        this.dialog.apiKey = text;
    };
    this.sizer.add(this.keyEdit);

    // Buttons
    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var okButton = new PushButton(this);
    okButton.text = "OK";
    okButton.defaultButton = true;
    okButton.onClick = function() {
        this.dialog.apiKey = this.dialog.keyEdit.text;
        if (this.dialog.apiKey.trim().length === 0) {
            (new MessageBox("Please enter your API key.", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        this.dialog.ok();
    };

    var cancelButton = new PushButton(this);
    cancelButton.text = "Skip Solving";
    cancelButton.onClick = function() {
        this.dialog.cancel();
    };

    buttonRow.add(okButton);
    buttonRow.add(cancelButton);
    this.sizer.add(buttonRow);

    this.adjustToContents();
}

APIKeyDialog.prototype = new Dialog;

function hasAstrometricSolution(win) {
    try {
        var view = win.mainView;

        // Check for WCS keywords that indicate an astrometric solution
        var keywords = view.window.keywords;
        var hasRA = false, hasDEC = false, hasCD = false, hasCTYPE = false;

        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            var name = keyword.name.toUpperCase();

            if (name === "RA" || name === "OBJCTRA" || name === "CRVAL1") hasRA = true;
            if (name === "DEC" || name === "OBJCTDEC" || name === "CRVAL2") hasDEC = true;
            if (name.indexOf("CD1_") === 0 || name.indexOf("CD2_") === 0) hasCD = true;
            if (name === "CTYPE1" || name === "CTYPE2") hasCTYPE = true;
        }

        // Check if we have sufficient WCS information
        var hasBasicCoords = hasRA && hasDEC;
        var hasWCSMatrix = hasCD && hasCTYPE;

        if (hasBasicCoords && hasWCSMatrix) {
            Console.writeln("✅ Valid astrometric solution found");
            return true;
        } else {
            Console.writeln("❌ No valid astrometric solution found");
            Console.writeln("   Has coordinates: " + hasBasicCoords);
            Console.writeln("   Has WCS matrix: " + hasWCSMatrix);
            return false;
        }

    } catch (e) {
        Console.writeln("❌ Error checking astrometric solution: " + e.message);
        return false;
    }
}

function performBlindSolve(win, apiKey, baseTag, debugDir) {
    Console.writeln("\n=== Starting Astrometry.net Blind Solve ===");

    try {
        // Save current image as temporary file for upload
        var tempDir = normalizePath(debugDir) + "/temp";
        if (!File.directoryExists(tempDir)) {
            File.createDirectory(tempDir, true);
        }

        var tempImagePath = tempDir + "/temp_solve_" + baseTag + "_" + Date.now() + ".fit";

        Console.writeln("Saving temporary image for solving: " + tempImagePath);
        win.saveAs(tempImagePath, false, false, false, false);

        // Get image parameters for solving
        var imageWidth = win.mainView.image.width;
        var imageHeight = win.mainView.image.height;
        var pixelSize = getPixelSizeFromKeywords(win);
        var focalLength = getFocalLengthFromKeywords(win);

        Console.writeln("Image dimensions: " + imageWidth + "x" + imageHeight);
        if (pixelSize > 0) Console.writeln("Pixel size: " + pixelSize + " μm");
        if (focalLength > 0) Console.writeln("Focal length: " + focalLength + " mm");

        // Calculate approximate scale if we have pixel size and focal length
        var approxScale = null;
        if (pixelSize > 0 && focalLength > 0) {
            approxScale = (pixelSize / focalLength) * 206.265; // arcsec/pixel
            Console.writeln("Calculated scale: " + approxScale.toFixed(3) + " arcsec/pixel");
        }

        // Create solving parameters
        var solveParams = {
            apiKey: apiKey,
            imagePath: tempImagePath,
            imageWidth: imageWidth,
            imageHeight: imageHeight,
            approxScale: approxScale,
            downsample: 2, // Downsample for faster solving
            timeout: 300 // 5 minute timeout
        };

        // Attempt to solve using ImageSolver if available, otherwise use manual method
        var solveSuccess = false;

        if (IMAGE_SOLVER_AVAILABLE) {
            Console.writeln("Attempting solve using PixInsight ImageSolver...");
            solveSuccess = solveWithImageSolver(win, solveParams);
        }

        if (!solveSuccess) {
            Console.writeln("Attempting solve using direct astrometry.net API...");
            solveSuccess = solveWithAstrometryNet(win, solveParams);
        }

        // Clean up temporary file
        try {
            File.remove(tempImagePath);
        } catch (e) {
            Console.writeln("Warning: Could not remove temporary file: " + e.message);
        }

        if (solveSuccess) {
            Console.writeln("✅ Blind solve completed successfully");
            debugSave(win, "After_BlindSolve", baseTag, debugDir);
            return true;
        } else {
            Console.writeln("❌ Blind solve failed");
            return false;
        }

    } catch (e) {
        Console.writeln("❌ Blind solve error: " + e.message);
        return false;
    }
}

function getPixelSizeFromKeywords(win) {
    try {
        var keywords = win.mainView.window.keywords;
        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            var name = keyword.name.toUpperCase();

            if (name === "PIXSIZE" || name === "XPIXSZ" || name === "PIXELSZ") {
                return parseFloat(keyword.value);
            }
        }
    } catch (e) {
        // Ignore errors
    }
    return 0;
}

function getFocalLengthFromKeywords(win) {
    try {
        var keywords = win.mainView.window.keywords;
        for (var i = 0; i < keywords.length; i++) {
            var keyword = keywords[i];
            var name = keyword.name.toUpperCase();

            if (name === "FOCALLEN" || name === "FOCAL" || name === "FL") {
                return parseFloat(keyword.value);
            }
        }
    } catch (e) {
        // Ignore errors
    }
    return 0;
}

function solveWithImageSolver(win, params) {
    try {
        Console.writeln("Using PixInsight ImageSolver for blind solving...");

        // Note: This would require the actual ImageSolver implementation
        // For now, we'll fall back to the manual method

        Console.writeln("ImageSolver integration not yet implemented, falling back to manual method");
        return false;

    } catch (e) {
        Console.writeln("ImageSolver failed: " + e.message);
        return false;
    }
}

function solveWithAstrometryNet(win, params) {
    Console.writeln("Using astrometry.net API for blind solving...");
    Console.writeln("");
    Console.writeln("⚠️ Manual Astrometry.net Solving Required ⚠️");
    Console.writeln("");
    Console.writeln("Due to PixInsight scripting limitations, please follow these steps:");
    Console.writeln("");
    Console.writeln("1. Go to: https://nova.astrometry.net/upload");
    Console.writeln("2. Upload this image file: " + params.imagePath);
    Console.writeln("3. Use these settings:");
    Console.writeln("   • API Key: " + params.apiKey);
    if (params.approxScale) {
        var scaleMin = params.approxScale * 0.8;
        var scaleMax = params.approxScale * 1.2;
        Console.writeln("   • Scale range: " + scaleMin.toFixed(2) + " to " + scaleMax.toFixed(2) + " arcsec/pixel");
    }
    Console.writeln("   • Downsample: " + params.downsample);
    Console.writeln("");
    Console.writeln("4. After successful solving, download the 'new-image.fits' file");
    Console.writeln("5. Replace the current image by opening the solved file");
    Console.writeln("");
    Console.writeln("Would you like to continue with manual solving?");

    var result = (new MessageBox(
        "Astrometry.net Manual Solving Required\n\n" +
        "Please follow the instructions in the Console to solve the image manually.\n\n" +
        "After solving, click 'Continue' to proceed with SPCC, or 'Skip' to continue without solving.",
        "Manual Solving Required",
        StdIcon_Question,
        StdButton_Yes, StdButton_No
    )).execute();

    if (result == StdButton_Yes) {
        // User will solve manually
        Console.writeln("✅ Proceeding with assumption that user will solve manually");
        return true; // Assume success for workflow continuation
    } else {
        Console.writeln("❌ User chose to skip solving");
        return false;
    }
}

// -------------------- Updated ImageSolver + SPCC Functions --------------------

function solveImage(win, sum, baseTag, debugDir){
    Console.writeln("\n=== Checking Astrometric Solution ===");

    // First check if image already has a valid astrometric solution
    if (hasAstrometricSolution(win)) {
        sum.solver = {name:"ImageSolver", status:"✅", details:"Existing astrometric solution found"};
        debugSave(win, "After_ExistingSolution", baseTag, debugDir);
        return true;
    }

    // No existing solution - offer blind solving
    Console.writeln("No astrometric solution found. Blind solving required for SPCC.");

    var solveChoice = (new MessageBox(
        "No Astrometric Solution Found\n\n" +
        "The image needs an astrometric solution for SPCC to work properly.\n\n" +
        "Would you like to attempt blind plate solving using astrometry.net?\n\n" +
        "YES = Attempt blind solving\n" +
        "NO = Skip SPCC (continue without color calibration)",
        "Blind Solving Required",
        StdIcon_Question,
        StdButton_Yes, StdButton_No
    )).execute();

    if (solveChoice == StdButton_No) {
        sum.solver = {name:"ImageSolver", status:"⏭️", details:"User chose to skip solving"};
        return false;
    }

    // User wants to attempt solving
    var apiKey = getAstrometryAPIKey();
    if (!apiKey) {
        sum.solver = {name:"ImageSolver", status:"❌", details:"No API key provided"};
        return false;
    }

    var solveSuccess = performBlindSolve(win, apiKey, baseTag, debugDir);

    if (solveSuccess) {
        sum.solver = {name:"ImageSolver", status:"✅", details:"Blind solve completed"};
        return true;
    } else {
        sum.solver = {name:"ImageSolver", status:"❌", details:"Blind solve failed"};
        return false;
    }
}

function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir){
    try{
        Console.writeln("\n=== SPCC (Spectrophotometric Color Calibration) ===");
        Console.writeln("Profile: " + profileSelector);

        // Double-check that we have an astrometric solution before proceeding
        if (!hasAstrometricSolution(win)) {
            Console.writeln("❌ SPCC cannot proceed - no astrometric solution available");
            sum.spcc = {name:"SPCC", status:"❌", details:"No astrometric solution available"};
            return false;
        }

        var P = new SpectrophotometricColorCalibration();

        // Configure SPCC parameters
        try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
        try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}

        try{
            if(profileSelector === "RGB"){
                P.redFilter   = "Astronomik Typ 2c R";
                P.greenFilter = "Astronomik Typ 2c G";
                P.blueFilter  = "Astronomik Typ 2c B";
            }else{
                P.redFilter   = "Sony Color Sensor R";
                P.greenFilter = "Sony Color Sensor G";
                P.blueFilter  = "Sony Color Sensor B";
            }
        }catch(_){}

        try{ P.narrowbandMode = false; }catch(_){}
        try{ P.generateGraphs = showGraphs || false; }catch(_){}
        try{ P.generateStarMaps = false; }catch(_){}
        try{ P.generateTextFiles = false; }catch(_){}
        try{ P.applyCalibration = true; }catch(_){}
        try{ P.catalog = "Gaia DR3/SP"; }catch(_){}
        try{ P.automaticLimitMagnitude = true; }catch(_){}
        try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = *****; }catch(_){}
        try{ P.structureLayers = 5; P.saturationThreshold = 0.99; P.backgroundReferenceViewId = ""; P.limitMagnitude = 16.0; }catch(_){}

        Console.writeln("Executing SPCC...");
        P.executeOn(win.mainView);

        // Check if SPCC actually succeeded by looking for the success message
        // We'll also verify the image still has its astrometric solution
        if (hasAstrometricSolution(win)) {
            sum.spcc = {name:"SPCC", status:"✅", details:"Applied successfully with " + profileSelector + " profile"};
            Console.writeln("✅ SPCC completed successfully");
        } else {
            sum.spcc = {name:"SPCC", status:"❌", details:"SPCC execution failed - astrometric solution lost"};
            Console.writeln("❌ SPCC failed - astrometric solution was lost during processing");
        }

        debugSave(win, "After_SPCC", baseTag, debugDir);
        return sum.spcc.status === "✅";

    } catch(e) {
        Console.writeln("❌ SPCC failed with error: " + e.message);

        // Check if this is the specific "no astrometric solution" error
        if (e.message.indexOf("no valid astrometric solution") >= 0) {
            sum.spcc = {name:"SPCC", status:"❌", details:"No valid astrometric solution - blind solving may be required"};
        } else {
            sum.spcc = {name:"SPCC", status:"❌", details:"SPCC error: " + e.message};
        }
        return false;
    }
}

// ============================================================================
// CROSS-PLATFORM CONFIGURATION SECTION
// ============================================================================
// Modify these paths as needed for your specific PixInsight installation

// Platform Detection - Fixed
var isWindows = (CoreApplication.platform == "MSWindows");
var isMacOS = (CoreApplication.platform == "MacOSX");
var isLinux = (CoreApplication.platform == "Linux");

// Debug platform detection
Console.writeln("DEBUG: CoreApplication.platform = '" + CoreApplication.platform + "'");

// Override platform detection if necessary (Windows users can uncomment this)
if (!isWindows && !isMacOS && !isLinux) {
    // Fallback detection based on file system
    if (File.directoryExists("C:/Program Files")) {
        isWindows = true;
        isMacOS = false;
        isLinux = false;
        Console.writeln("DEBUG: Forced Windows detection based on C:/Program Files");
    } else if (File.directoryExists("/Applications")) {
        isWindows = false;
        isMacOS = true;
        isLinux = false;
        Console.writeln("DEBUG: Forced macOS detection based on /Applications");
    } else {
        isWindows = false;
        isMacOS = false;
        isLinux = true;
        Console.writeln("DEBUG: Defaulted to Linux");
    }
}

// Platform-specific Configuration
var PLATFORM_CONFIG = {
    // PixInsight Installation Paths
    pixinsightBase: isWindows ? "C:/Program Files/PixInsight" :
                    isMacOS   ? "/Applications/PixInsight" :
                                "/usr/local/PixInsight", // Linux

    // Script Paths (relative to PixInsight base)
    scriptPaths: {
        imageSolver: "src/scripts/AdP/ImageSolver.js",
        graXpert: "src/scripts/Toolbox/GraXpertLib.jsh"
    },

    // Default Directories
    defaultDirs: {
        input: isWindows ? "C:/AstroImages/Processed" :
               isMacOS   ? File.homeDirectory + "/AstroImages/Processed" :
                          File.homeDirectory + "/AstroImages/Processed",

        output: isWindows ? "C:/AstroImages/Output" :
                isMacOS   ? File.homeDirectory + "/AstroImages/Output" :
                           File.homeDirectory + "/AstroImages/Output"
    },

    // Path separator
    pathSep: isWindows ? "\\" : "/",

    // File extensions (consistent across platforms)
    supportedExtensions: [".xisf", ".fit", ".fits", ".tif", ".tiff"]
};

// Helper function to build full paths
function buildScriptPath(scriptKey) {
    return PLATFORM_CONFIG.pixinsightBase + "/" + PLATFORM_CONFIG.scriptPaths[scriptKey];
}

// Try to detect actual PixInsight installation if default doesn't exist
function detectPixInsightInstallation() {
    var possiblePaths = [];

    if (isWindows) {
        possiblePaths = [
            "C:/Program Files/PixInsight",
            "C:/Program Files (x86)/PixInsight",
            "D:/Program Files/PixInsight",
            "C:/PixInsight"
        ];
    } else if (isMacOS) {
        possiblePaths = [
            "/Applications/PixInsight",
            File.homeDirectory + "/Applications/PixInsight",
            "/usr/local/PixInsight"
        ];
    } else { // Linux
        possiblePaths = [
            "/usr/local/PixInsight",
            "/opt/PixInsight",
            File.homeDirectory + "/PixInsight"
        ];
    }

    for (var i = 0; i < possiblePaths.length; i++) {
        if (File.directoryExists(possiblePaths[i])) {
            Console.writeln("Detected PixInsight installation at: " + possiblePaths[i]);
            PLATFORM_CONFIG.pixinsightBase = possiblePaths[i];
            return true;
        }
    }

    Console.warningln("Could not auto-detect PixInsight installation. Using default: " + PLATFORM_CONFIG.pixinsightBase);
    return false;
}

// Validate and setup paths
function validatePlatformSetup() {
    Console.writeln("Platform detected: " + (isWindows ? "Windows" : isMacOS ? "macOS" : "Linux"));

    // Try to detect PixInsight installation
    if (!File.directoryExists(PLATFORM_CONFIG.pixinsightBase)) {
        detectPixInsightInstallation();
    }

    // Check if critical script files exist
    var imageSolverPath = buildScriptPath("imageSolver");
    var graXpertPath = buildScriptPath("graXpert");

    if (!File.exists(imageSolverPath)) {
        Console.warningln("ImageSolver script not found at: " + imageSolverPath);
        Console.warningln("Image solving will be disabled. Please check your PixInsight installation.");
    }

    if (!File.exists(graXpertPath)) {
        Console.warningln("GraXpert script not found at: " + graXpertPath);
        Console.warningln("GraXpert processing will be disabled. Please install GraXpert or check the path.");
    }

    // Ensure default directories exist or create them
    try {
        if (!File.directoryExists(PLATFORM_CONFIG.defaultDirs.input)) {
            File.createDirectory(PLATFORM_CONFIG.defaultDirs.input, true);
        }
        if (!File.directoryExists(PLATFORM_CONFIG.defaultDirs.output)) {
            File.createDirectory(PLATFORM_CONFIG.defaultDirs.output, true);
        }
    } catch (e) {
        Console.warningln("Could not create default directories: " + e.message);
    }
}

// ============================================================================
// FEATURE AVAILABILITY FLAGS
// ============================================================================

// Try to include ImageSolver if available
var IMAGE_SOLVER_AVAILABLE = false;
try {
    var imageSolverPath = buildScriptPath("imageSolver");
    if (File.exists(imageSolverPath)) {
        IMAGE_SOLVER_AVAILABLE = true;
    }
} catch (e) {
    Console.warningln("ImageSolver include failed: " + e.message);
    IMAGE_SOLVER_AVAILABLE = false;
}

// Try to include GraXpert if available
var GRAXPERT_AVAILABLE = false;
try {
    var graXpertPath = buildScriptPath("graXpert");
    if (File.exists(graXpertPath)) {
        GRAXPERT_AVAILABLE = true;
    }
} catch (e) {
    Console.warningln("GraXpert include failed: " + e.message);
    GRAXPERT_AVAILABLE = false;
}

(function(){

// -------------------- Configuration & Globals --------------------
var DEBUG_MODE = true;
var INTERACTIVE_MODE = true;  // Enable interactive step-by-step review
var debugStepCounter = 1;
var USER_ABORTED = false;

// Custom Error Types for Flow Control
var ABORT_PROCESSING = "ABORT_PROCESSING"; // Stop the entire batch
var ABORT_PROCESSING_IMAGE = "ABORT_PROCESSING_IMAGE"; // Stop current image, continue batch

var outputExtension = ".xisf";
var defaults = {
  inputDir:  PLATFORM_CONFIG.defaultDirs.input,
  outputDir: PLATFORM_CONFIG.defaultDirs.output,
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};

// -------------------- Cross-Platform Utility Functions --------------------
function ensureDir(p){
    var normalizedPath = normalizePath(p);
    if(!File.directoryExists(normalizedPath)) File.createDirectory(normalizedPath, true);
}

function normalizePath(path) {
    // Convert backslashes to forward slashes for consistency across platforms
    // Using string replace method instead of regex to avoid syntax issues
    var result = "";
    for (var i = 0; i < path.length; i++) {
        if (path.charAt(i) == "\\") {
            result += "/";
        } else {
            result += path.charAt(i);
        }
    }
    return result;
}

function platformPath(path) {
    // Convert to platform-specific separators if needed
    if (isWindows) {
        var result = "";
        for (var i = 0; i < path.length; i++) {
            if (path.charAt(i) == "/") {
                result += "\\";
            } else {
                result += path.charAt(i);
            }
        }
        return result;
    }
    return path;
}

function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=normalizePath(base);
  if (out.charAt(out.length-1) == "/") {
      out = out.substring(0, out.length-1);
  }
  out = out + "/Processed_"+ts;
  ensureDir(out);
  return out;
}

function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}

function sanitizeBase(name){
    var result = "";
    for (var i = 0; i < name.length; i++) {
        var char = name.charAt(i);
        if ((char >= 'a' && char <= 'z') ||
            (char >= 'A' && char <= 'Z') ||
            (char >= '0' && char <= '9') ||
            char == '_' || char == '-') {
            result += char;
        } else {
            result += "_";
        }
    }
    return result.length ? result : "image";
}

function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }

function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}

function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}

/*
 * PIXINSIGHT BATCH CONVERTER ALGORITHM - EXTRACTED FROM BatchFormatConversion.js
 * This is the exact algorithm PixInsight uses for format conversion
 * No custom gamma correction or stretching - pure PixInsight approach
 */

// FileData constructor - handles all metadata, ICC profiles, etc.
function FileData(image, description, instance, outputFormat) {
    this.image = image;
    this.description = description;
    this.filePath = instance.filePath;

    if (outputFormat.canStoreICCProfiles && instance.format.canStoreICCProfiles)
        this.iccProfile = instance.iccProfile;
    else
        this.iccProfile = undefined;

    if (outputFormat.canStoreKeywords && instance.format.canStoreKeywords)
        this.keywords = instance.keywords;
    else
        this.keywords = undefined;

    if (outputFormat.canStoreMetadata && instance.format.canStoreMetadata)
        this.metadata = instance.metadata;
    else
        this.metadata = undefined;

    if (outputFormat.canStoreImageProperties && instance.format.canStoreImageProperties &&
        outputFormat.supportsViewProperties && instance.format.supportsViewProperties) {
        this.properties = [];
        var properties = instance.imageProperties;
        for (var i = 0; i < properties.length; ++i) {
            var value = instance.readImageProperty(properties[i][0]/*id*/);
            if (value != null)
                this.properties.push({id: properties[i][0], type: properties[i][1], value: value});
        }
    } else
        this.properties = undefined;

    if (outputFormat.canStoreThumbnails && instance.format.canStoreThumbnails)
        this.thumbnail = instance.thumbnail;
    else
        this.thumbnail = undefined;
}

// PixInsight's writeImage function adapted for our use
function writeImageUsingPIAlgorithm(image, outputFilePath, bitsPerSample, floatSample) {
    var outputFormat = new FileFormat(".tif", false/*toRead*/, true/*toWrite*/);
    if (outputFormat.isNull)
        throw new Error("No installed file format can write TIFF files.");

    var f = new FileFormatInstance(outputFormat);
    if (f.isNull)
        throw new Error("Unable to instantiate TIFF file format.");

    if (!f.create(outputFilePath, "" /* no output hints for TIFF */))
        throw new Error("Error creating output file: " + outputFilePath);

    // Create proper image description
    var d = new ImageDescription;
    d.width = image.width;
    d.height = image.height;
    d.numberOfChannels = image.numberOfChannels;
    d.colorSpace = image.colorSpace;
    d.bitsPerSample = bitsPerSample || 16;
    d.ieeefpSampleFormat = floatSample || false;

    if (!f.setOptions(d))
        throw new Error("Unable to set output file options: " + outputFilePath);

    if (!f.writeImage(image))
        throw new Error("Error writing output file: " + outputFilePath);

    f.close();
}

/*
 * CORRECTED TIFF SAVE FUNCTION - Using PixInsight's BatchFormatConversion Algorithm
 * This is exactly how PixInsight's own batch converter works
 */
function saveAs16BitTiff_Photoshop(win, path) {
    if (!win || !win.isWindow) {
        Console.writeln("Error: Invalid ImageWindow provided.");
        return;
    }

    var tifPath = normalizePath(path) + ".tif";
    var originalView = win.mainView;

    Console.writeln("Starting TIFF export using PixInsight BatchFormatConversion algorithm...");
    if (DEBUG_MODE) {
        Console.writeln("DEBUG: Target Path: " + tifPath);
    }

    try {
        // Create a copy of the image with 16-bit integer format
        var bitsPerSample = 16;
        var floatSample = false;

        var image = new Image(originalView.image.width,
                             originalView.image.height,
                             originalView.image.numberOfChannels,
                             originalView.image.colorSpace,
                             bitsPerSample,
                             floatSample ? SampleType_Real : SampleType_Integer);

        // Copy the image data
        image.assign(originalView.image);

        // Convert to 16-bit if needed
        if (originalView.image.bitsPerSample != 16 || originalView.image.isReal) {
            Console.writeln("Converting to 16-bit integer format...");
        }

        // Use PixInsight's algorithm to write the image
        writeImageUsingPIAlgorithm(image, tifPath, bitsPerSample, floatSample);

        Console.writeln("✅ Successfully saved TIFF using PixInsight algorithm: " + tifPath);

        // Clean up
        image.free();

    } catch (e) {
        Console.writeln("⚠️ Error during TIFF conversion: " + e.message);
        throw e;
    }
}

// -------------------- Debug and Revert Functions --------------------

function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '" + stepName + "'");
        return;
    }

    // Create subfolder for this step
    var counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    var sanitizedStepName = sanitizeBase(stepName);
    var stepFolder = normalizePath(debugDir) + "/" + counterStr + "_" + sanitizedStepName;

    // Create the step subfolder
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        stepFolder = debugDir;
    }

    var baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;

    // Save as XISF (critical for revert)
    var xisfPath = stepFolder + "/" + baseFileName + ".xisf";
    try {
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
        if (DEBUG_MODE) {
            Console.criticalln("CRITICAL: Debug save failed. Revert functionality may be compromised. Check disk space/permissions.");
        }
    }

    debugStepCounter++;
}

function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
    Console.writeln("🔄 REVERT: Looking for previous step before step " + currentStepNumber);

    if (currentStepNumber <= 1) {
        Console.writeln("⚠️ Cannot revert - already at the first step (Initial Integration).");
        return null;
    }

    var previousStepNumber = currentStepNumber - 1;
    var previousStepStr = (previousStepNumber < 10 ? "0" : "") + previousStepNumber;
    var revertFile = null;

    Console.writeln("🔍 Searching for previous step's folder starting with '" + previousStepStr + "_' in: " + debugDir);

    try {
        var ff = new FileFind();
        if (ff.begin(normalizePath(debugDir) + "/" + previousStepStr + "_*")) {
            do {
                if (ff.isDirectory) {
                    var folderPath = normalizePath(debugDir) + "/" + ff.name;
                    var stepNamePart = ff.name.substring(previousStepStr.length + 1);
                    var xisfFile = folderPath + "/" + previousStepStr + "_" + baseTag + "_" + stepNamePart + ".xisf";

                    if (File.exists(xisfFile)) {
                        revertFile = xisfFile;
                        Console.writeln("✅ Found revert file: " + revertFile);
                        break;
                    }
                }
            } while (ff.next());
        }
        ff.end();
    } catch (e) {
        Console.writeln("❌ Error while searching for revert file: " + e.message);
    }

    if (!revertFile) {
        Console.writeln("❌ REVERT FAILED: Could not find the output file from step " + previousStepNumber);
        return null;
    }

    try {
        Console.writeln("🔄 REVERTING: Loading " + revertFile);
        var currentId = "restored_view";

        if (win && win instanceof ImageWindow && win.isWindow) {
            currentId = win.mainView.id;
            if (typeof win.forceClose === 'function') {
                win.forceClose();
            } else {
                Console.criticalln("❌ REVERT FAILED: win.forceClose() method is missing! Cannot close current window.");
                return null;
            }
        } else {
            Console.writeln("Note: Current window was already closed or invalid.");
        }

        var windows = ImageWindow.open(revertFile);
        if (windows.length > 0) {
            var restoredWin = windows[0];
            restoredWin.mainView.id = currentId;
            restoredWin.show();
            restoredWin.bringToFront();
            Console.writeln("✅ REVERT SUCCESSFUL");
            return restoredWin;
        } else {
            Console.writeln("❌ REVERT FAILED: Could not open file " + revertFile);
            return null;
        }
    } catch (e) {
        Console.writeln("❌ REVERT ERROR during file load: " + e.message);
        return null;
    }
}

// -------------------- Interactive Review Functions --------------------

function showStretchedPreview(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    Console.writeln("📺 Applying auto-stretch for visual review...");

    try {
        win.show();
        win.bringToFront();
        win.zoomToFit();

        var view = win.mainView;
        var median = view.computeOrFetchProperty("Median");
        var mad = view.computeOrFetchProperty("MAD");

        var stf = new ScreenTransferFunction;
        var n = view.image.isColor ? 3 : 1;

        var stfParams = [
            [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
        ];

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826;
            var c0 = Math.max(0, med - 2.8 * madVal);
            var m = Math.mtf(0.25, med - c0);
            stfParams[c] = [c0, 1.0, m, 0, 1];
        }

        stf.STF = stfParams;
        stf.executeOn(view);

        win.show();
        win.bringToFront();

        Console.writeln("✅ Auto-stretch applied for review");
        return true;
    } catch(e) {
        Console.writeln("❌ Auto-stretch failed: " + e.message);
        return false;
    }
}

function resetStretch(win) {
    if (!INTERACTIVE_MODE) return true;
    if (!win || !win.isWindow) return false;

    try {
        if (typeof win.disableScreenTransferFunctions === 'function') {
            win.disableScreenTransferFunctions();
        } else {
            var stf = new ScreenTransferFunction;
            var identitySTF = [
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
            ];
            stf.STF = identitySTF;
            stf.executeOn(win.mainView);
        }
        Console.writeln("✅ Reset to linear view");
        return true;
    } catch(e) {
        Console.writeln("⚠️ Reset failed: " + e.message + " (continuing anyway)");
        return true;
    }
}

function askAcceptStep(stepName, description, stepNumber) {
    if (!INTERACTIVE_MODE) return "accept";

    if (stepName !== "GraXpert") {
        Console.writeln("✅ AUTO-ACCEPTED Step " + stepNumber + ": " + stepName);
        return "accept";
    }

    Console.writeln("\n🔍 REVIEW STEP " + stepNumber + ": " + stepName);

    var result = (new MessageBox(
        "Step " + stepNumber + ": " + stepName + "\n\n" +
        (description || "Please review the image on screen.") + "\n\n" +
        "Choose your action:\n\n" +
        "YES = Accept and keep this step\n" +
        "NO = Skip this step (revert to previous step)\n" +
        "CANCEL = Stop processing entirely",
        "Step " + stepNumber + " - Accept, Skip, or Stop?",
        StdIcon_Question,
        StdButton_Yes, StdButton_No, StdButton_Cancel
    )).execute();

    if (result == StdButton_Yes) {
        Console.writeln("✅ ACCEPTED Step " + stepNumber + ": " + stepName);
        return "accept";
    } else if (result == StdButton_No) {
        Console.writeln("⏭️ SKIPPED Step " + stepNumber + ": " + stepName + " - reverting to previous step");
        return "skip";
    } else {
        Console.writeln("🛑 STOPPED: Processing aborted by user at Step " + stepNumber + ": " + stepName);
        USER_ABORTED = true;
        throw new Error(ABORT_PROCESSING);
    }
}

// -------------------- File System Functions --------------------
function findAllInputImages(dir){
  var normalizedDir = normalizePath(dir);
  if(!File.directoryExists(normalizedDir)) throw new Error("Input directory does not exist: "+normalizedDir);
  var v=[];
  var ff=new FileFind;

  if(ff.begin(normalizedDir+"/*.*")){
    do {
      var nameLower = ff.name.toLowerCase();
      for (var i = 0; i < PLATFORM_CONFIG.supportedExtensions.length; ++i) {
        if (nameLower.indexOf(PLATFORM_CONFIG.supportedExtensions[i]) === nameLower.length - PLATFORM_CONFIG.supportedExtensions[i].length) {
          v.push(normalizedDir+"/"+ff.name);
          break;
        }
      }
    } while(ff.next());
  }
  return v;
}

function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || s.indexOf("filter-r ")>=0 || s.indexOf(" red ")>=0) return "R";
  if (s.indexOf("filter-green")>=0 || s.indexOf("filter-g ")>=0 || s.indexOf(" green ")>=0) return "G";
  if (s.indexOf("filter-blue")>=0 || s.indexOf("filter-b ")>=0 || s.indexOf(" blue ")>=0) return "B";
  if (s.indexOf("luminance")>=0 || s.indexOf("filter-l ")>=0 || s.indexOf(" l ")>=0) return "L";
  if (s.indexOf("ha")>=0 || s.indexOf("h-alpha")>=0 || s.indexOf("h_alpha")>=0) return "Ha";
  if (s.indexOf("oiii")>=0 || s.indexOf("o3")>=0 || s.indexOf("o-iii")>=0) return "OIII";
  if (s.indexOf("sii")>=0 || s.indexOf("s2")>=0 || s.indexOf("s-ii")>=0) return "SII";
  return null;
}

function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }
  var haveR=!!(byFilter.R&&byFilter.R.length), haveG=!!(byFilter.G&&byFilter.G.length), haveB=!!(byFilter.B&&byFilter.B.length);
  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };
  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }
  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k, isStackedRGB: false });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) {
    var filePath = unknownSingles[k];
    var isColorStack = false;
    try {
        var tempWinArr = ImageWindow.open(filePath);
        if (tempWinArr.length > 0) {
            var tempWin = tempWinArr[0];
            if (tempWin.mainView.image.isColor) isColorStack = true;
            tempWin.forceClose();
        }
    } catch (e) {
        Console.writeln("Warning: Could not determine color space for " + File.extractName(filePath) + ". Assuming Mono.");
    }
    plan.singles.push({ path: filePath, tag: isColorStack ? "Color" : "Single", isStackedRGB: isColorStack });
  }
  return plan;
}

// -------------------- Processing Functions (Robust Pattern) --------------------

function handleRobustExecution(win, stepName, sum, sumKey, executionFunc, baseTag, debugDir) {
    if (!win || !win.isWindow) {
        Console.criticalln("❌ CRITICAL: Invalid window passed to " + stepName + ". Aborting image.");
        throw new Error(ABORT_PROCESSING_IMAGE);
    }

    try {
        Console.writeln("\n=== Running " + stepName + " ===");

        var details = executionFunc(win);
        sum[sumKey] = {name: stepName, status: "✅", details: details || "Applied successfully"};

        debugSave(win, "After_" + stepName, baseTag, debugDir);
        var executedStepNumber = debugStepCounter - 1;

        if (INTERACTIVE_MODE) {
            var isAlreadyStretched = (sum.stretch && sum.stretch.status === "✅");

            if (isAlreadyStretched) {
                win.show();
                win.bringToFront();
                win.zoomToFit();
                Console.writeln("📺 Displaying already stretched image for review");
            } else {
                showStretchedPreview(win);
            }

            processEvents();
            msleep(1000);

            var decision = askAcceptStep(stepName, "Review the result of " + stepName + ".", executedStepNumber);

            if (!isAlreadyStretched) {
                resetStretch(win);
            }

            if (decision === "skip") {
                var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    sum[sumKey].status = "⏭️";
                    sum[sumKey].details = "Skipped by user";
                } else {
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }

        return win;

    } catch (e) {
        if (e.message === ABORT_PROCESSING || e.message === ABORT_PROCESSING_IMAGE) throw e;

        Console.writeln("❌ " + stepName + " FAILED: " + e.message);

        if (win && win.isWindow) {
            debugSave(win, "After_" + stepName + "_FAILED", baseTag, debugDir);
        }

        var failedStepNumber = debugStepCounter - 1;
        Console.writeln("🔄 Attempting automatic revert because " + stepName + " failed.");

        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            win = restoredWin;
            sum[sumKey] = {name: stepName, status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
        } else {
            if (!restoredWin) {
                Console.criticalln("❌ CRITICAL: " + stepName + " failed, and revert failed or image window is gone.");
                throw new Error(ABORT_PROCESSING_IMAGE);
            }
            sum[sumKey] = {name: stepName, status: "❌", details: "Failed, and revert also failed. Error: " + e.message};
        }
        return win;
    }
}

// --- Specific Implementations using the Robust Pattern ---

function finalABE(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "ABE (Background Extraction)", sum, "backgroundExtraction", function(w) {
        var P = new AutomaticBackgroundExtractor;
        P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050;
        P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5;
        P.targetCorrection = 0;
        P.normalize = true; P.replaceTarget = true; P.discardModel = true;
        P.executeOn(w.mainView);
        return "ABE Subtraction applied";
    }, baseTag, debugDir);
}

function runGradientCorrection(win, sum, baseTag, debugDir) {
    return handleRobustExecution(win, "GradientCorrection", sum, "gradientCorrection", function(w) {
        var P = new GradientCorrection;
        P.reference = 0.50; P.lowThreshold = 0.20; P.lowTolerance = 0.50; P.highThreshold = 0.05; P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.automaticConvergence = true;
        P.executeOn(w.mainView);
        return "Applied with custom settings";
    }, baseTag, debugDir);
}

function runGraXpert(win, sum, baseTag, debugDir) {
    if (!GRAXPERT_AVAILABLE) {
        Console.writeln("⏭️ GraXpert not available - skipping");
        sum.graxpert = {name: "GraXpert", status: "⏭️", details: "GraXpert not available on this system"};
        return win;
    }

    return handleRobustExecution(win, "GraXpert", sum, "graxpert", function(w) {
        try {
            var graXpertPath = buildScriptPath("graXpert");
            Console.writeln("Loading GraXpert from: " + graXpertPath);

            Console.writeln("GraXpert processing would happen here");
            return "GraXpert applied (placeholder)";
        } catch (e) {
            throw new Error("GraXpert execution failed: " + e.message);
        }
    }, baseTag, debugDir);
}

function autoBlackPoint(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Black Point Adjustment", sum, "blackPoint", function(w) {
        var img = w.mainView.image;
        var details = "";

        if (!img.readSamples) {
            var AH_fallback = new AutoHistogram();
            AH_fallback.auto=true; AH_fallback.clipLow=0.1; AH_fallback.clipHigh=0.1;
            AH_fallback.executeOn(w.mainView);
            details = "Fallback AutoHistogram (No sample access)";
        } else if(!img.isColor || img.numberOfChannels<3){
            var AH=new AutoHistogram();
            AH.auto=true; AH.clipLow=0.1; AH.clipHigh=0.1;
            AH.executeOn(w.mainView);
            details = "AutoHistogram (Mono/NB)";
        } else {
            var width=img.width, height=img.height, sampleSize=20, numSamples=20;
            var rs=[],gs=[],bs=[];
            for(var i=0;i<numSamples;++i){
              var x=Math.floor(Math.random()*(width-sampleSize)), y=Math.floor(Math.random()*(height-sampleSize));
              var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;
              for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }
              rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);
            }
            rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});
            var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0;
            for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; }
            R/=n; G/=n; B/=n;

            try{
              var L=new Levels();
              L.redBlack=Math.min(R*0.8,0.02);
              L.greenBlack=Math.min(G*0.9,0.03);
              L.blueBlack=Math.min(B*0.8,0.02);
              L.redWhite=0.98;
              L.greenWhite=0.97;
              L.blueWhite=0.98;
              L.executeOn(w.mainView);
              details = "Levels (Sampled)";
            }catch(e2){
              var AH2=new AutoHistogram();
              AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1;
              AH2.executeOn(w.mainView);
              details = "Fallback AutoHistogram (Color)";
            }
        }
        return details;
    }, baseTag, debugDir);
}

// -------------------- AI steps --------------------

function deblur1(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Deblur V1 (Round Stars)", sum, "deblurV1", function(w) {
        var P=new BlurXTerminator();
        P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
        P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
        P.executeOn(w.mainView);
        return "Round stars correction applied";
    }, baseTag, debugDir);
}

function deblur2(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Deblur V2 (Enhance)", sum, "deblurV2", function(w) {
        var P=new BlurXTerminator();
        P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
        P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
        P.executeOn(w.mainView);
        return "Enhancement applied";
    }, baseTag, debugDir);
}

function denoise(win, sum, baseTag, debugDir){
    return handleRobustExecution(win, "Denoising (NoiseX)", sum, "denoising", function(w) {
        var P=new NoiseXTerminator();
        P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
        P.executeOn(w.mainView);
        return "Denoising applied (0.90)";
    }, baseTag, debugDir);
}

// -------------------- ImageSolver + SPCC (Platform-aware) --------------------

function solveImage(win, sum, baseTag, debugDir){
  if (!IMAGE_SOLVER_AVAILABLE) {
    Console.writeln("⏭️ ImageSolver not available - skipping");
    sum.solver = {name:"ImageSolver", status:"⏭️", details:"ImageSolver not available on this system"};
    return false;
  }

  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    Console.writeln("ImageSolver would be executed here");
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (placeholder)"};
    debugSave(win, "After_ImageSolver", baseTag, debugDir);
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}

function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();

    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.generateGraphs=showGraphs||false; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = *****; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}

    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied"};

    debugSave(win, "After_SPCC", baseTag, debugDir);
    return true;

  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}

// -------------------- Stretch Functions --------------------

function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked )
{
    if (shadowsClipping === undefined) shadowsClipping = -2.80;
    if (targetBackground === undefined) targetBackground = 0.25;
    if (rgbLinked === undefined) rgbLinked = true;

    var stf = new ScreenTransferFunction;
    var n = view.image.isColor ? 3 : 1;

    var median = new Vector( view.computeOrFetchProperty( "Median" ) );
    var mad = new Vector( view.computeOrFetchProperty( "MAD" ) );

    mad.mul( 1.4826 );

    if ( rgbLinked && n > 1)
    {
      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
        if ( median.at( c ) > 0.5 )
          ++invertedChannels;

      if ( invertedChannels < n )
      {
        var c0_sum = 0, median_sum = 0;
        for ( var c = 0; c < n; ++c )
        {
          if ( 1 + mad.at( c ) != 1 )
            c0_sum += median.at( c ) + shadowsClipping * mad.at( c );
          median_sum += median.at( c );
        }
        var c0 = Math.range( c0_sum/n, 0.0, 1.0 );
        var m = Math.mtf( targetBackground, median_sum/n - c0 );

        stf.STF = [
                        [c0, 1, m, 0, 1], [c0, 1, m, 0, 1], [c0, 1, m, 0, 1], [0, 1, 0.5, 0, 1]
                      ];
      }
      else
      {
        var c1_sum = 0, median_sum = 0;
        for ( var c = 0; c < n; ++c )
        {
          if ( 1 + mad.at( c ) != 1 )
            c1_sum += median.at( c ) - shadowsClipping * mad.at( c );
          median_sum += median.at( c );
        }
        var c1 = Math.range( c1_sum/n, 0.0, 1.0 );
        var m = Math.mtf( c1 - median_sum/n, targetBackground );

        stf.STF = [
                        [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, 1, 0.5, 0, 1]
                      ];
      }
    }
    else
    {
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];

      for ( var c = 0; c < n; ++c )
      {
        if ( median.at( c ) < 0.5 )
        {
          var c0 = (1 + mad.at( c ) != 1) ?
                    Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
          var m = Math.mtf( targetBackground, median.at( c ) - c0 );
          A[c] = [c0, 1, m, 0, 1];
        }
        else
        {
          var c1 = (1 + mad.at( c ) != 1) ?
                    Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
          var m = Math.mtf( c1 - median.at( c ), targetBackground );
          A[c] = [0, c1, m, 0, 1];
        }
      }
      stf.STF = A;
    }

    return stf;
}

function ApplyHistogramTransformation( view, stf )
{
    var ht = new HistogramTransformation;

    var HT_IDENTITY = [0, 0.5, 1, 0, 1];
    var H = [
            HT_IDENTITY.slice(), HT_IDENTITY.slice(), HT_IDENTITY.slice(),
            HT_IDENTITY.slice(), HT_IDENTITY.slice()
            ];

    var n = view.image.isColor ? 3 : 1;

    function mapSTFtoHT(stf_channel) {
        return [
            stf_channel[0], // c0
            stf_channel[2], // m
            stf_channel[1], // c1
            stf_channel[3], // r0
            stf_channel[4]  // r1
        ];
    }

    if ( view.image.isColor )
    {
        var linked = true;
        for(var c = 1; c < n; ++c) {
            if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
                linked = false;
                break;
            }
        }

        if(linked) {
            H[3] = mapSTFtoHT(stf.STF[0]);
        } else {
            for ( var c = 0; c < n; ++c )
            {
              H[c] = mapSTFtoHT(stf.STF[c]);
            }
        }
    }
    else
    {
        H[3] = mapSTFtoHT(stf.STF[0]);
    }

    ht.H = H;
    return ht.executeOn( view );
}

function applyPerfectNuclearStretch(view) {
    if (!view || !view.image || view.image.isNull)
      throw new Error("No active view for nuclear stretch.");

    var isColor = view.image.isColor;
    var rgbLinked = isColor;

    Console.writeln("Nuclear stretch: " + (isColor ? "RGB linked" : "Mono unlinked") + " mode");

    var stf = STFAutoStretch( view, -2.80, 0.25, rgbLinked );
    return ApplyHistogramTransformation( view, stf );
}

// -------------------- Color Enhance helpers --------------------
function applyColorEnhanceToView(view){
  var C = new CurvesTransformation;
  C.R = C.G = C.B = C.K = C.A = C.L = C.a = C.b = C.c = C.H = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Rt = C.Gt = C.Bt = C.Kt = C.At = C.Lt = C.at = C.bt = C.ct = C.Ht = CurvesTransformation.prototype.AkimaSubsplines;

  C.S = [[0.00000, 0.00000], [0.14470, 0.33247], [1.00000, 1.00000]];
  C.St = CurvesTransformation.prototype.AkimaSubsplines;
  C.executeOn(view);

  var N = new SCNR;
  N.amount = 0.73;
  N.protectionMethod = SCNR.prototype.AverageNeutral;
  N.colorToRemove = SCNR.prototype.Green;
  N.preserveLightness = true;
  N.executeOn(view);
}

// -------------------- Main Processing Steps --------------------

function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}

function starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir) {
    try {
        Console.writeln("\n=== Running StarXTerminator ===");

        var tempDir = normalizePath(debugDir) + "/temp";
        if (!File.directoryExists(tempDir)) {
            File.createDirectory(tempDir, true);
        }
        var tempOriginalPath = tempDir + "/temp_original_" + baseTag + "_" + Date.now() + ".xisf";

        win.saveAs(tempOriginalPath, false, false, false, false);
        Console.writeln("✅ Saved temporary original for star generation");

        var SX = new StarXTerminator();
        SX.generateStarImage = true;
        SX.unscreenStars = true;
        SX.largeOverlap = false;

        SX.executeOn(win.mainView);

        sum.starSeparation={name:"Star Separation",status:"✅",details:"StarX applied on stretched image"};
        debugSave(win, "After_StarSeparation_Starless", baseTag, debugDir);

        var executedStepNumber = debugStepCounter - 1;

        var separationAccepted = true;
        if (INTERACTIVE_MODE) {
            win.show();
            win.bringToFront();
            win.zoomToFit();
            processEvents();
            msleep(1000);

            var decision = askAcceptStep("StarX Separation",
                "StarXTerminator has separated stars from nebulosity.",
                executedStepNumber);

            if (decision === "skip") {
                separationAccepted = false;
                var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    sum.starSeparation.status = "⏭️";
                    sum.starSeparation.details = "Skipped by user";
                    try{ File.remove(tempOriginalPath); }catch(_){}
                    return win;
                } else {
                    Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                    throw new Error(ABORT_PROCESSING_IMAGE);
                }
            }
        }

        var starsWin = null;
        if (separationAccepted && saveCfg.stars_stretched) {
            try {
                var originalWinArray = ImageWindow.open(tempOriginalPath);
                if (originalWinArray.length > 0) {
                    var originalWin = originalWinArray[0];
                    originalWin.mainView.id = "TempOriginal_" + Date.now();

                    var starsId = "Stars_" + baseTag + "_" + Date.now();
                    var exprStars = "max(0, " + originalWin.mainView.id + " - " + win.mainView.id + ")";

                    var PM = new PixelMath();
                    PM.useSingleExpression = true;
                    PM.expression = exprStars;
                    PM.createNewImage = true;
                    PM.newImageId = starsId;
                    PM.newImageColorSpace = PixelMath.prototype.SameAsTarget;
                    PM.rescaleResult = false;
                    PM.truncateResult = false;
                    PM.executeOn(originalWin.mainView);

                    starsWin = ImageWindow.windowById(starsId);
                    if (!starsWin) {
                        Console.writeln("⚠️ Stars image creation failed, but continuing with starless");
                    } else {
                        Console.writeln("✅ Generated stars using PixelMath: Original - Starless");
                    }

                    try{ originalWin.forceClose(); }catch(_){}
                } else {
                    Console.writeln("⚠️ Could not reload original image for star generation");
                }
            } catch(e) {
                Console.writeln("⚠️ Stars generation failed: " + e.message + ", but continuing with starless");
            }
        }

        if (separationAccepted) {
            if(saveCfg.starless_stretched) {
                saveAs16BitTiff_Photoshop(win, finalDir+"/Starless_Stretched_"+baseTag);
                Console.writeln("  Saved Starless (stretched): Starless_Stretched_" + baseTag + ".tif");
            }

            if(saveCfg.starless_linear) {
                Console.writeln("  ⚠️ Starless (linear) requested but image is already stretched - skipping");
            }

            if(saveCfg.stars_stretched && starsWin){
                if(isColor && enhanceRGBStarsStretched) {
                    Console.writeln("Applying color enhancement to stars...");
                    applyColorEnhanceToView(starsWin.mainView);
                    sum.starColorsEnhanced = {name:"Star colors enhanced", status:"✅", details:"S-curve and SCNR applied"};
                }
                saveAs16BitTiff_Photoshop(starsWin, finalDir+"/Stars_Stretched_"+baseTag);
                Console.writeln("  Saved Stars (stretched): Stars_Stretched_" + baseTag + ".tif");
            }
        }

        try{ if (starsWin) starsWin.forceClose(); }catch(_){}
        try{ File.remove(tempOriginalPath); }catch(_){}

        return win;

    } catch(e) {
        if (e.message.indexOf("ABORT_") === 0) throw e;

        Console.writeln("❌ Star Separation FAILED: " + e.message);
        if (win && win.isWindow) {
            debugSave(win, "After_StarSeparation_FAILED", baseTag, debugDir);
        }

        var failedStepNumber = debugStepCounter - 1;
        Console.writeln("🔄 Attempting automatic revert because Star Separation failed.");

        var restoredWin = revertToPreviousStep(win, failedStepNumber, baseTag, debugDir);

        if (restoredWin) {
            win = restoredWin;
            sum.starSeparation = {name: "Star Separation", status: "⚠️❌", details: "Failed and reverted. Error: " + e.message};
        } else {
            if (!restoredWin) {
                Console.criticalln("❌ CRITICAL: Star Separation failed, revert failed, or image window is gone.");
                throw new Error(ABORT_PROCESSING_IMAGE);
            }
            sum.starSeparation={name:"Star Separation",status:"❌",details: "Failed, and revert also failed. Error: " + e.message};
        }
        return win;
    }
}

// ############ REVISED processOne FUNCTION ############
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB, spccGraphs){
    var sum={};
    var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
    var finalDir=rootOut+"/6_final";   ensureDir(finalDir);
    var shortTag = baseTag.length > 20 ? baseTag.substring(0, 20) : baseTag;
    var debugDir = rootOut + "/debug_" + shortTag;

    debugStepCounter = 1;
    USER_ABORTED = false;

    if (DEBUG_MODE) ensureDir(debugDir);

    debugSave(win, "Initial_Integration", baseTag, debugDir);

    var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
    win.saveAs(integrationPath,false,false,false,false);
    sum.integrationSave={name:"Integration Save",status:"✅",details:"Integration saved"};

    closeAllWindowsExcept([win.mainView.id]);

    var baseline = null;
    var stretchedPath = null;

    try {
        win = finalABE(win, sum, baseTag, debugDir);
        win = autoBlackPoint(win, sum, baseTag, debugDir);
        win = runGradientCorrection(win, sum, baseTag, debugDir);
        win = runGraXpert(win, sum, baseTag, debugDir);
        win = autoBlackPoint(win, sum, baseTag, debugDir);

        if(ai.deblur1){
            win = deblur1(win, sum, baseTag, debugDir);
            if(saveCfg.deblur1 && sum.deblurV1 && sum.deblurV1.status === "✅") {
                win.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
            }
        }

        var isColor = win.mainView.image.isColor;
        if (isColor) {
            var spccProfile = isRGBCombined ? "RGB" : "OSC";

            if (solveImage(win, sum, baseTag, debugDir)) {
                performSPCC(win, sum, spccProfile, spccGraphs, baseTag, debugDir);
            } else {
                sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
            }
        } else {
            sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped (mono/NB)"};
            sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (mono/NB)"};
        }

        if(ai.deblur2){
            win = deblur2(win, sum, baseTag, debugDir);
            if(saveCfg.deblur2 && sum.deblurV2 && sum.deblurV2.status === "✅") {
                win.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
            }
        }

        closeAllWindowsExcept([win.mainView.id]);
        baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
        win.saveAs(baseline,false,false,false,false);
        sum.baselineSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};
        debugSave(win, "Baseline_Linear", baseTag, debugDir);

        stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
        if(ai.stretch){
            try {
                Console.writeln("\n=== Applying Nuclear Stretch ===");
                applyPerfectNuclearStretch(win.mainView);
                win.saveAs(stretchedPath,false,false,false,false);
                debugSave(win, "After_Nuclear_Stretch", baseTag, debugDir);
                sum.stretch={name:"Nuclear Stretch",status:"✅",details:"Applied with histogram transformation"};

                var executedStepNumber = debugStepCounter - 1;

                if (INTERACTIVE_MODE) {
                    win.show();
                    win.bringToFront();
                    win.zoomToFit();
                    processEvents();
                    msleep(1000);

                    var decision = askAcceptStep("Nuclear Stretch",
                        "Nuclear stretch has been applied.",
                        executedStepNumber);

                    if (decision === "skip") {
                        var restoredWin = revertToPreviousStep(win, executedStepNumber, baseTag, debugDir);
                        if (restoredWin) {
                            win = restoredWin;
                            sum.stretch.status = "⏭️";
                            sum.stretch.details = "Skipped by user - reverted to linear";
                            Console.writeln("⏭️ Nuclear Stretch skipped - reverted to linear");
                            stretchedPath = null;
                        } else {
                            Console.criticalln("❌ CRITICAL: Skip requested but revert failed. Aborting image.");
                            throw new Error(ABORT_PROCESSING_IMAGE);
                        }
                    }
                }
            } catch (e) {
                if (e.message.indexOf("ABORT_") === 0) throw e;
                Console.writeln("❌ Stretch FAILED: " + e.message);
                sum.stretch={name:"Nuclear Stretch",status:"❌",details:e.message};
            }
        }else{
            sum.stretch={name:"Nuclear Stretch",status:"⏭️",details:"Disabled in settings"};
        }

        if(saveCfg.final_stretched && sum.stretch && sum.stretch.status === "✅") {
            saveAs16BitTiff_Photoshop(win, finalDir+"/Final_Stretched_"+baseTag);
            Console.writeln("  Saved Final (stretched, with stars): Final_Stretched_" + baseTag + ".tif");
        }

        if(ai.denoise){
            win = denoise(win, sum, baseTag, debugDir);
            if (saveCfg.denoised && sum.denoising && sum.denoising.status === "✅") {
                var denoiseOutPath = finalDir+"/Denoised_"+baseTag+outputExtension;
                win.saveAs(denoiseOutPath, false, false, false, false);
            }
        }

        if(ai.starless){
            var needAny = (saveCfg.stars_stretched || saveCfg.starless_linear || saveCfg.starless_stretched);
            if(needAny){
                win = starSeparation(win, sum, finalDir, baseTag, saveCfg, isColor, enhanceRGBStarsStretched, debugDir);
            }else{
                sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No star/starless outputs requested"};
            }
        }

    } catch (e) {
        if (e.message === ABORT_PROCESSING) {
            Console.writeln("🛑 Processing stopped by user (Batch Abort)");
            Console.writeln("📁 Partial results available in: " + debugDir);
            throw e;
        } else if (e.message === ABORT_PROCESSING_IMAGE) {
            Console.writeln("🛑 Processing stopped for this image due to critical failure or failed revert.");
            Console.writeln("📁 Partial results available in: " + debugDir);
        } else {
            Console.criticalln("💥 Unhandled error during processOne: " + e.message);
            throw e;
        }
    }

    if(!saveCfg.baseline_linear && baseline) removeIf(baseline, true);
    if(!saveCfg.integration_linear && integrationPath) removeIf(integrationPath, true);

    if(!saveCfg.final_stretched && !saveCfg.denoised && stretchedPath) {
        try {
            var tifPath = stretchedPath;
            if (tifPath.indexOf(".") >= 0) {
                var lastDot = tifPath.lastIndexOf(".");
                tifPath = tifPath.substring(0, lastDot) + ".tif";
            } else {
                tifPath = tifPath + ".tif";
            }
            removeIf(tifPath, true);
        } catch (e) {
            Console.writeln("Warning during cleanup (stretchedPath): " + e.message);
        }
    }

    return {win: win, summary: sum, baseTag: baseTag};
}

// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog;
  dlg.windowTitle="Post-Integration Pipeline (Cross-Platform) - PixInsight BatchConverter Algorithm";
  dlg.sizer=new VerticalSizer;
  dlg.sizer.margin=10;
  dlg.sizer.spacing=8;

  var head=new Label(dlg);
  head.useRichText=true;
  head.text="<b>Utah Masterclass — Post-Integration Pipeline (Cross-Platform)</b>";
  head.textAlignment=TextAlign_Center;
  dlg.sizer.add(head);

  var gTop=new GroupBox(dlg);
  gTop.title="General";
  gTop.sizer=new VerticalSizer;
  gTop.sizer.margin=8;
  gTop.sizer.spacing=6;

  var rowIn=new HorizontalSizer;
  rowIn.spacing=6;
  var labelIn=new Label(dlg);
  labelIn.text="Input Directory:";
  labelIn.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var editIn=new Edit(dlg);
  editIn.readOnly=true;
  editIn.minWidth=560;
  editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg);
  btnIn.text="Browse...";
  btnIn.icon=dlg.scaledResource(":/icons/select-file.png");
  btnIn.onClick=function(){
    var d=new GetDirectoryDialog;
    d.caption="Select Input Directory";
    d.initialDirectory=state.inputDir||"";
    if(d.execute()){
      state.inputDir=normalizePath(d.directory);
      if (state.inputDir.charAt(state.inputDir.length-1) == "/") {
          state.inputDir = state.inputDir.substring(0, state.inputDir.length-1);
      }
      editIn.text=state.inputDir;
    }
  };
  rowIn.add(labelIn);
  rowIn.add(editIn,100);
  rowIn.add(btnIn);
  gTop.sizer.add(rowIn);

  var rowOut=new HorizontalSizer;
  rowOut.spacing=6;
  var labelOut=new Label(dlg);
  labelOut.text="Output Directory:";
  labelOut.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var editOut=new Edit(dlg);
  editOut.readOnly=true;
  editOut.minWidth=560;
  editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg);
  btnOut.text="Browse...";
  btnOut.icon=dlg.scaledResource(":/icons/select-file.png");
  btnOut.onClick=function(){
    var d=new GetDirectoryDialog;
    d.caption="Select Output Base Directory";
    d.initialDirectory=state.outputDir||"";
    if(d.execute()){
      state.outputDir=normalizePath(d.directory);
      if (state.outputDir.charAt(state.outputDir.length-1) == "/") {
          state.outputDir = state.outputDir.substring(0, state.outputDir.length-1);
      }
      editOut.text=state.outputDir;
    }
  };
  rowOut.add(labelOut);
  rowOut.add(editOut,100);
  rowOut.add(btnOut);
  gTop.sizer.add(rowOut);
  dlg.sizer.add(gTop);

  var gAI=new GroupBox(dlg);
  gAI.title="AI / Processing Steps";
  gAI.sizer=new VerticalSizer;
  gAI.sizer.margin=8;
  gAI.sizer.spacing=6;

  var rowAI1=new HorizontalSizer;
  rowAI1.spacing=12;
  var cbD1=new CheckBox(dlg);
  cbD1.text="✨ Deblur V1 (round stars)";
  cbD1.checked=state.ai.deblur1;
  var cbD2=new CheckBox(dlg);
  cbD2.text="✨ Deblur V2 (enhance)";
  cbD2.checked=state.ai.deblur2;
  var cbST=new CheckBox(dlg);
  cbST.text="🌀 Stretch (AutoSTF→HT)";
  cbST.checked=state.ai.stretch;
  var cbDN=new CheckBox(dlg);
  cbDN.text="🧼 Denoise";
  cbDN.checked=state.ai.denoise;
  var cbSL=new CheckBox(dlg);
  cbSL.text="✂️ Enable StarX (for starless/stars)";
  cbSL.checked=state.ai.starless;
  rowAI1.add(cbD1);
  rowAI1.add(cbD2);
  rowAI1.add(cbST);
  rowAI1.add(cbDN);
  rowAI1.add(cbSL);
  rowAI1.addStretch();
  gAI.sizer.add(rowAI1);

  var rowAI2=new HorizontalSizer;
  rowAI2.spacing=12;
  var cbSPCCGraph=new CheckBox(dlg);
  cbSPCCGraph.text="📊 Show SPCC Graphs (Advanced)";
  cbSPCCGraph.checked=state.spccGraphs;
  rowAI2.add(cbSPCCGraph);
  rowAI2.addStretch();
  gAI.sizer.add(rowAI2);
  dlg.sizer.add(gAI);

  var gRGB=new GroupBox(dlg);
  gRGB.title="RGB Outputs";
  gRGB.sizer=new VerticalSizer;
  gRGB.sizer.margin=8;
  gRGB.sizer.spacing=6;

  var rowRGBMaster=new HorizontalSizer;
  rowRGBMaster.spacing=10;
  var chkProcRGB=new CheckBox(gRGB);
  chkProcRGB.text="✅ Process RGB / Color";
  chkProcRGB.checked=state.processRGB;
  chkProcRGB.toolTip="Enable or disable all RGB/Color processing and outputs.";
  rowRGBMaster.add(chkProcRGB);
  rowRGBMaster.addStretch();
  gRGB.sizer.add(rowRGBMaster);

  var cbCombine=new CheckBox(dlg);
  cbCombine.text="Auto-detect & combine R+G+B masters";
  cbCombine.checked=state.combineRGB;
  gRGB.sizer.add(cbCombine);

  var rowR1=new HorizontalSizer;
  rowR1.spacing=10;
  var lR1=new Label(dlg);
  lR1.text="🏆 Finals:";
  lR1.minWidth=120;
  var rFinalS=new CheckBox(dlg);
  rFinalS.text="Final (stretched, with stars) (TIFF)";
  rFinalS.checked=state.save.rgb.final_stretched;
  var rFinalL=new CheckBox(dlg);
  rFinalL.text="Final (linear, with stars)";
  rFinalL.checked=state.save.rgb.final_linear;
  rowR1.add(lR1);
  rowR1.add(rFinalS);
  rowR1.add(rFinalL);
  rowR1.addStretch();
  gRGB.sizer.add(rowR1);

  var rowR2=new HorizontalSizer;
  rowR2.spacing=10;
  var lR2=new Label(dlg);
  lR2.text="🎭 Masks & Starless:";
  lR2.minWidth=120;
  var rStarsS=new CheckBox(dlg);
  rStarsS.text="Stars (stretched mask) (TIFF)";
  rStarsS.checked=state.save.rgb.stars_stretched;
  var rgbEnh=new CheckBox(dlg);
  rgbEnh.text="✨ Enhance with rich star colors";
  rgbEnh.checked=state.colorEnhanceRGBStarsStretched;
  var rSLessS=new CheckBox(dlg);
  rSLessS.text="Starless (stretched) (TIFF)";
  rSLessS.checked=state.save.rgb.starless_stretched;
  var rSLessL=new CheckBox(dlg);
  rSLessL.text="Starless (linear)";
  rSLessL.checked=state.save.rgb.starless_linear;
  rowR2.add(lR2);
  rowR2.add(rStarsS);
  rowR2.add(rgbEnh);
  rowR2.add(rSLessS);
  rowR2.add(rSLessL);
  rowR2.addStretch();
  gRGB.sizer.add(rowR2);
  dlg.sizer.add(gRGB);

  var gM=new GroupBox(dlg);
  gM.title="Monochrome / Narrowband Outputs";
  gM.sizer=new VerticalSizer;
  gM.sizer.margin=8;
  gM.sizer.spacing=6;

  var rowMonoMaster=new HorizontalSizer;
  rowMonoMaster.spacing=10;
  var chkProcMono=new CheckBox(gM);
  chkProcMono.text="✅ Process Monochrome / Singles";
  chkProcMono.checked=state.processMonochrome;
  chkProcMono.toolTip="Enable or disable all Monochrome/single-channel processing and outputs.";
  rowMonoMaster.add(chkProcMono);
  rowMonoMaster.addStretch();
  gM.sizer.add(rowMonoMaster);

  var rowM1=new HorizontalSizer;
  rowM1.spacing=10;
  var lM1=new Label(dlg);
  lM1.text="🏆 Finals:";
  lM1.minWidth=120;
  var mFinalS=new CheckBox(dlg);
  mFinalS.text="Final (stretched, with stars) (TIFF)";
  mFinalS.checked=state.save.mono.final_stretched;
  var mFinalL=new CheckBox(dlg);
  mFinalL.text="Final (linear, with stars)";
  mFinalL.checked=state.save.mono.final_linear;
  rowM1.add(lM1);
  rowM1.add(mFinalS);
  rowM1.add(mFinalL);
  rowM1.addStretch();
  gM.sizer.add(rowM1);

  var rowM2=new HorizontalSizer;
  rowM2.spacing=10;
  var lM2=new Label(dlg);
  lM2.text="🎭 Starless & Masks:";
  lM2.minWidth=120;
  var mStarsS=new CheckBox(dlg);
  mStarsS.text="Stars (stretched mask) (TIFF)";
  mStarsS.checked=state.save.mono.stars_stretched;
  var mSLessS=new CheckBox(dlg);
  mSLessS.text="Starless (stretched) (TIFF)";
  mSLessS.checked=state.save.mono.starless_stretched;
  var mSLessL=new CheckBox(dlg);
  mSLessL.text="Starless (linear)";
  mSLessL.checked=state.save.mono.starless_linear;
  rowM2.add(lM2);
  rowM2.add(mStarsS);
  rowM2.add(mSLessS);
  rowM2.add(mSLessL);
  rowM2.addStretch();
  gM.sizer.add(rowM2);
  dlg.sizer.add(gM);

  var rgbControls=[cbCombine,rFinalS,rFinalL,rStarsS,rgbEnh,rSLessS,rSLessL];
  var monoControls=[mFinalS,mFinalL,mStarsS,mSLessS,mSLessL];

  function toggleSection(enabled,controls){
    for(var i=0;i<controls.length;++i)controls[i].enabled=enabled;
  }

  chkProcRGB.onCheck=function(checked){
    toggleSection(checked,rgbControls);
  };

  chkProcMono.onCheck=function(checked){
    toggleSection(checked,monoControls);
  };

  toggleSection(chkProcRGB.checked,rgbControls);
  toggleSection(chkProcMono.checked,monoControls);

  var rowBtn=new HorizontalSizer;
  rowBtn.spacing=8;
  rowBtn.addStretch();
  var bStart=new PushButton(dlg);
  bStart.text="Start";
  bStart.icon=dlg.scaledResource(":/icons/ok.png");
  bStart.defaultButton=true;
  var bCancel=new PushButton(dlg);
  bCancel.text="Cancel";
  bCancel.icon=dlg.scaledResource(":/icons/close.png");
  rowBtn.add(bStart);
  rowBtn.add(bCancel);
  dlg.sizer.add(rowBtn);

  bCancel.onClick=function(){dlg.cancel();};
  bStart.onClick=function(){
    if(!state.inputDir||!File.directoryExists(state.inputDir)){
      (new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();
      return;
    }
    if(!state.outputDir||!File.directoryExists(state.outputDir)){
      (new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();
      return;
    }
    state.processRGB=chkProcRGB.checked;
    state.processMonochrome=chkProcMono.checked;
    state.combineRGB=cbCombine.checked;
    state.ai.deblur1=cbD1.checked;
    state.ai.deblur2=cbD2.checked;
    state.ai.stretch=cbST.checked;
    state.ai.denoise=cbDN.checked;
    state.ai.starless=cbSL.checked;
    state.colorEnhanceRGBStarsStretched=rgbEnh.checked;
    state.spccGraphs=cbSPCCGraph.checked;
    state.save.rgb={
      final_stretched:rFinalS.checked,
      final_linear:rFinalL.checked,
      stars_stretched:rStarsS.checked,
      starless_stretched:rSLessS.checked,
      starless_linear:rSLessL.checked,
      integration_linear:false,
      baseline_linear:false,
      deblur1:false,
      deblur2:false,
      denoised:false
    };
    state.save.mono={
      final_stretched:mFinalS.checked,
      final_linear:mFinalL.checked,
      stars_stretched:mStarsS.checked,
      starless_stretched:mSLessS.checked,
      starless_linear:mSLessL.checked,
      integration_linear:false,
      baseline_linear:false,
      deblur1:false,
      deblur2:false,
      denoised:false
    };
    dlg.ok();
  };

  return dlg.execute();
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();

  // Initialize platform configuration
  validatePlatformSetup();

  Console.writeln("=== Post-Integration Pipeline (Cross-Platform) — PixInsight BatchConverter Algorithm ===");
  Console.writeln("Platform: " + (isWindows ? "Windows" : isMacOS ? "macOS" : "Linux"));
  Console.writeln("PixInsight Base: " + PLATFORM_CONFIG.pixinsightBase);

  var settingsKey="PostIntegrationPipeline";
  var state={
    inputDir:Settings.read(settingsKey+"/InputDir",DataType_String)||defaults.inputDir,
    outputDir:Settings.read(settingsKey+"/OutputDir",DataType_String)||defaults.outputDir,
    processRGB:defaults.processRGB,
    processMonochrome:defaults.processMonochrome,
    combineRGB:defaults.combineRGB,
    ai:{
      deblur1:defaults.ai.deblur1,
      deblur2:defaults.ai.deblur2,
      stretch:defaults.ai.stretch,
      denoise:defaults.ai.denoise,
      starless:defaults.ai.starless
    },
    colorEnhanceRGBStarsStretched:defaults.colorEnhanceRGBStarsStretched,
    spccGraphs:defaults.spccGraphs,
    save:{
      rgb:defaults.save.rgb,
      mono:defaults.save.mono
    }
  };

  if(!showGUI(state)){
    Console.writeln("Cancelled.");
    return;
  }

  Settings.write(settingsKey+"/InputDir",DataType_String,state.inputDir);
  Settings.write(settingsKey+"/OutputDir",DataType_String,state.outputDir);

  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);

  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder: "+root);
  ensureDir(root+"/5_stacked");
  ensureDir(root+"/6_final");

  var allSummaries = [];

  try{
    var plan=buildWorkPlan(state.inputDir,state.combineRGB);
    var doRGB=state.processRGB&&plan.doRGB&&shouldProcessConfig(state.save.rgb);

    if(!state.processRGB&&!state.processMonochrome){
      Console.writeln("Both RGB and Monochrome processing are disabled. Exiting.");
      return;
    }

    if(doRGB){
      Console.writeln("\n→ Building RGB from:");
      Console.writeln("    R: "+File.extractName(plan.r));
      Console.writeln("    G: "+File.extractName(plan.g));
      Console.writeln("    B: "+File.extractName(plan.b));
      var combo=combineRGB(plan.r,plan.g,plan.b,root);
      var result = processOne(combo.window,combo.base,root,state.ai,state.save.rgb,true,state.colorEnhanceRGBStarsStretched,false,state.spccGraphs);
      allSummaries.push({summary: result.summary, baseTag: result.baseTag});
      try{if (result.win && result.win.isWindow) result.win.forceClose();}catch(_){}
    }else{
      Console.writeln("RGB Combination: skipped (no R+G+B set found or option disabled).");
    }

    if(plan.singles.length>0){
      Console.writeln("\n→ Processing mono/narrowband/color singles: "+plan.singles.length);
      for(var i=0;i<plan.singles.length;++i){
        var singleInfo=plan.singles[i];
        var p=singleInfo.path;
        var tag=singleInfo.tag;
        var isStackedRGB=singleInfo.isStackedRGB;
        Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");
        var w=ImageWindow.open(p);
        if(w.length===0){
          Console.writeln("  ⚠️ Could not open, skipping.");
          continue;
        }
        var win=w[0],base=tag+"_"+sanitizeBase(File.extractName(p));
        var isColor=win.mainView.image.isColor;
        var saveCfg=isColor?state.save.rgb:state.save.mono;
        var procEnabled=isColor?state.processRGB:state.processMonochrome;
        if(!procEnabled||!shouldProcessConfig(saveCfg)){
          Console.writeln("  ⏭️ Skipped (processing or outputs disabled for this image type).");
          try{win.forceClose();}catch(_){}
          continue;
        }
        var result = processOne(win,base,root,state.ai,saveCfg,false,isColor&&state.colorEnhanceRGBStarsStretched,isStackedRGB,state.spccGraphs);
        allSummaries.push({summary: result.summary, baseTag: result.baseTag});
        try{if (result.win && result.win.isWindow) result.win.forceClose();}catch(_){}
        closeAllWindowsExcept(null);
      }
    }else{
      Console.writeln("\nNo single channel (Mono/NB/Color) masters found to process.");
    }

    Console.writeln("\n" + "================================================================================");
    Console.writeln("FINAL PROCESSING SUMMARY");
    Console.writeln("================================================================================");

    for(var s=0; s<allSummaries.length; s++){
      var summaryData = allSummaries[s];
      Console.writeln("\nSummary: " + summaryData.baseTag + " —");

      var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "stretch", "denoising", "starSeparation", "starColorsEnhanced"];

      for(var i=0;i<order.length;++i){
        var k=order[i];
        if(summaryData.summary[k]){
          var it=summaryData.summary[k];
          Console.writeln("  "+it.status+" "+it.name+": "+it.details);
        }
      }
    }

    Console.writeln("\n=== Done. Output: "+root+" ===");
  }catch(err){
    if (err.message === ABORT_PROCESSING) {
        Console.writeln("\n=== Batch Processing Aborted by User ===");
    } else {
        Console.criticalln("Error: "+err.message);
        if(DEBUG_MODE){
          (new MessageBox(err.message,"Script Aborted",StdIcon_Error,StdButton_Ok)).execute();
        }else{
          throw err;
        }
    }
  }
}

run();
})(); // IIFE
