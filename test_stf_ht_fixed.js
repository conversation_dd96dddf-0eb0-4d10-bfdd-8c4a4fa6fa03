/*
 * STF Auto Stretch and Apply <PERSON><PERSON><PERSON> - Corrected and Enhanced
 *
 * This script replicates the functionality of the STF Auto-Stretch ("nuclear button")
 * by reading the user's configured preferences, and applies the stretch permanently
 * using HistogramTransformation.
 *
 * Fix: Corrects in-place modification of image statistics vectors.
 */

#feature-id    Utilities > AutoStretchAndApplyPermanent_Fixed

// Configuration for testing (used in the provided example scenario)
// Set TEST_MODE to true to run the test scenario, false to run on the active window.
var TEST_MODE = true;
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-24T06-11-29/5_stacked/RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test/";


/*
 * Function to retrieve STF AutoStretch preferences from the PixInsight environment.
 */
function getSTFPreferences() {
    // Keys used in PixInsight settings (Process > Defaults > STF AutoStretch)
    const KEY_SCLIP = "STFAutoStretch/ShadowsClipping";
    const KEY_TBGND = "STFAutoStretch/TargetBackground";
    const KEY_CLINK = "STFAutoStretch/RgbLinked";

    // Fallback defaults if settings cannot be read
    var prefs = {
        shadowsClipping: -2.80,
        targetBackground: 0.125, // A common default
        rgbLinked: true
    };

    // Attempt to read the settings from the environment
    try {
        // Use correct PixInsight Settings API
        var settings = new Settings;
        if (settings.canRead(KEY_SCLIP)) {
            prefs.shadowsClipping = settings.read(KEY_SCLIP, DataType_Float);
        }
        if (settings.canRead(KEY_TBGND)) {
            prefs.targetBackground = settings.read(KEY_TBGND, DataType_Float);
        }
        if (settings.canRead(KEY_CLINK)) {
            prefs.rgbLinked = settings.read(KEY_CLINK, DataType_Boolean);
        }
    } catch (e) {
        console.writeln("Warning: Could not read STF preferences. Using fallback defaults. Error: " + e.message);
    }

    console.writeln("Using STF AutoStretch Preferences:");
    console.writeln("  Shadows Clipping (sigma): " + prefs.shadowsClipping.toFixed(2));
    console.writeln("  Target Background:        " + prefs.targetBackground.toFixed(4));
    console.writeln("  RGB Linked:               " + prefs.rgbLinked);

    return prefs;
}


/*
 * STF Auto Stretch routine (CORRECTED)
 */
function STFAutoStretch( view, prefs )
{
   var shadowsClipping = prefs.shadowsClipping;
   var targetBackground = prefs.targetBackground;
   var rgbLinked = prefs.rgbLinked;

   var stf = new ScreenTransferFunction;
   var n = view.image.isColor ? 3 : 1;

   // 1. Calculate Statistics (Median and MAD)
   // CRITICAL FIX: We must create copies (clones) using 'new Vector()'.
   // Failure to do so allows subsequent modifications (like mad.mul)
   // to corrupt the image's cached statistics in memory because Vector.mul() is in-place.
   var median = new Vector( view.computeOrFetchProperty( "Median" ) );
   var mad = new Vector( view.computeOrFetchProperty( "MAD" ) );

   console.writeln("Original statistics computed (Linear Data):");
    for (var c = 0; c < n; ++c) {
      console.writeln("Channel " + c + ": median=" + median.at(c).toFixed(6) + ", mad=" + mad.at(c).toFixed(6));
   }

   // 2. Normalize MAD (Multiply by 1.4826 to align with sigma).
   // This now safely modifies our local copy.
   mad.mul( 1.4826 );

   // 3. Calculate Stretch Parameters
   if ( rgbLinked && n > 1)
   {
      // Linked RGB channels
      console.noteln("Calculating Linked RGB Stretch...");

      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
         if ( median.at( c ) > 0.5 )
            ++invertedChannels;

      if ( invertedChannels < n )
      {
         // Noninverted image
         // Use clearer variable names for accumulation
         var c0_sum = 0, median_sum = 0;

         for ( var c = 0; c < n; ++c )
         {
            // Robust check for zero MAD
            if ( 1 + mad.at( c ) != 1 )
               // Formula: Median + (ClippingFactor * Sigma)
               c0_sum += median.at( c ) + shadowsClipping * mad.at( c );
            median_sum += median.at( c );
         }

         // Average the shadows clipping point (c0) and clamp
         var c0 = Math.range( c0_sum/n, 0.0, 1.0 );

         // Calculate midtones balance (m) using the MTF
         var m = Math.mtf( targetBackground, median_sum/n - c0 );

         // Apply the same STF parameters to all channels
         stf.STF = [
                     [c0, 1, m, 0, 1], // R
                     [c0, 1, m, 0, 1], // G
                     [c0, 1, m, 0, 1], // B
                     [0, 1, 0.5, 0, 1] // Alpha
                   ];
         console.writeln("Linked Stretch: c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
      }
      else
      {
         // Inverted image logic (Handles highlights clipping c1)
         console.noteln("Image appears inverted. Calculating highlight clipping.");
         var c1_sum = 0, median_sum = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c1_sum += median.at( c ) - shadowsClipping * mad.at( c );
            median_sum += median.at( c );
         }
         var c1 = Math.range( c1_sum/n, 0.0, 1.0 );
         var m = Math.mtf( c1 - median_sum/n, targetBackground );

         stf.STF = [
                     [0, c1, m, 0, 1],
                     [0, c1, m, 0, 1],
                     [0, c1, m, 0, 1],
                     [0, 1, 0.5, 0, 1]
                   ];
         console.writeln("Inverted Linked Stretch: c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
      }
   }
   else
   {
      // Unlinked channels (or Grayscale image)
      console.noteln("Calculating Unlinked/Grayscale Stretch...");
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];

      for ( var c = 0; c < n; ++c )
      {
         if ( median.at( c ) < 0.5 )
         {
            // Noninverted channel
            var c0 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
            var m = Math.mtf( targetBackground, median.at( c ) - c0 );
            A[c] = [c0, 1, m, 0, 1];
            console.writeln("Channel " + c + ": c0=" + c0.toFixed(6) + ", m=" + m.toFixed(6));
         }
         else
         {
            // Inverted channel
            var c1 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
            var m = Math.mtf( c1 - median.at( c ), targetBackground );
            A[c] = [0, c1, m, 0, 1];
             console.writeln("Channel " + c + " (Inverted): c1=" + c1.toFixed(6) + ", m=" + m.toFixed(6));
         }
      }
      stf.STF = A;
   }

   return stf;
}


/*
 * Apply HistogramTransformation
 */
function ApplyHistogramTransformation( view, stf )
{
   console.writeln("\nApplying HistogramTransformation...");
   var ht = new HistogramTransformation;

   // Initialize HT parameters array (H).
   // 5 channels for HT: R/K (0), G (1), B (2), RGB/K Combined (3), Alpha (4).
   var H = [
            [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1],
            [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
           ];

   var n = view.image.isColor ? 3 : 1;

   if ( view.image.isColor )
   {
       // Check if the STF channels are linked
       var linked = true;
       for(var c = 1; c < n; ++c) {
           if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
               linked = false;
               break;
           }
       }

       if(linked) {
           // CRITICAL: If linked, apply to the combined RGB/K channel (Index 3) in HT.
           // This mirrors the manual STF drag-and-drop behavior.
           H[3][0] = stf.STF[0][0]; // shadows clipping
           H[3][1] = stf.STF[0][1]; // highlights clipping
           H[3][2] = stf.STF[0][2]; // midtones balance
           console.noteln("Applying Linked HT to RGB/K channel.");
       } else {
           // If unlinked, apply to individual channels (Indices 0, 1, 2).
           console.noteln("Applying Unlinked HT to individual R, G, B channels.");
           for ( var c = 0; c < n; ++c )
           {
              H[c][0] = stf.STF[c][0];
              H[c][1] = stf.STF[c][1];
              H[c][2] = stf.STF[c][2];
           }
       }
   }
   else
   {
       // Monochrome image (Apply to Index 0)
       console.noteln("Applying HT to K channel.");
       H[0][0] = stf.STF[0][0];
       H[0][1] = stf.STF[0][1];
       H[0][2] = stf.STF[0][2];
   }

   ht.H = H;

   // Execute the HistogramTransformation
   if (!ht.executeOn( view )) {
       console.criticalln("HistogramTransformation execution failed.");
       return false;
   }
   return true;
}

/*
 * Robustly resets the STF visualization on a window.
 */
function ResetSTF(window) {
    console.noteln("Resetting STF visualization...");
    try {
        // Preferred method
        window.disableScreenTransferFunctions();
    } catch(e) {
        // Fallback method if the above fails (e.g., in some automated contexts)
        var stfReset = new ScreenTransferFunction;
        stfReset.executeOn(window.currentView, false); // Apply identity STF
        console.writeln("STF reset using fallback method.");
    }
}

// Main execution block
function main()
{
   console.show();
    console.writeln("=== STF Auto Stretch Script (Corrected) ===");

   // 1. Retrieve user preferences for STF AutoStretch
   var stfPrefs = getSTFPreferences();

   var window;

   if (TEST_MODE) {
        // Setup for test environment
        console.noteln("\nRunning in TEST MODE.");
         try {
            if (!File.directoryExists(outputDir)) {
                File.createDirectory(outputDir, true);
                console.writeln("Created output directory: " + outputDir);
            }
        } catch(e) {
            console.noteln("Note: Could not create output directory (it may already exist).");
        }

        console.writeln("Opening: " + inputFile);
        var windows = ImageWindow.open(inputFile);
        if (windows.length === 0) {
            console.criticalln("Failed to open file: " + inputFile);
            return;
        }
        window = windows[0];
        window.show(); // Ensure window is visible

   } else {
       // Standard operation
       window = ImageWindow.activeWindow;
   }


   if ( window.isNull )
   {
      console.criticalln( "No active image window." );
      return;
   }

   var view = window.currentView;
   console.noteln( "\nProcessing view: ", view.fullId );

   // 2. Ensure clean state: Disable any existing STF visualization before analysis.
   ResetSTF(window);

   // 3. Calculate STF Auto Stretch parameters.
   var stf = STFAutoStretch( view, stfPrefs );

   // 4. Apply the calculated parameters using HistogramTransformation
   // We intentionally skip applying the temporary STF visualization (stf.executeOn(view)) for robustness.
   if (ApplyHistogramTransformation( view, stf )) {

       // 5. Reset the STF visualization again to prevent a "double stretch" display.
       ResetSTF(window);

       // 6. Save results (if in test mode)
       if (TEST_MODE) {
           var outputPath = outputDir + "STF_Nuclear_Result_FIXED.xisf";
           console.noteln("\nSaving result...");
            // Arguments: path, warnings, validate, strict, security
            if (window.saveAs(outputPath, false, false, false, false)) {
                 console.successln("Result saved: " + outputPath);
            } else {
                console.criticalln("Failed to save XISF file.");
            }
       }

       console.successln( "\n✅ STF Auto Stretch applied permanently via HistogramTransformation." );
   } else {
       console.criticalln( "\n❌ Script failed during HistogramTransformation." );
   }
}

main();
