function performSPCC(win, sum, profileSelector, showGraphs, baseTag, debugDir){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();

    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.generateGraphs=showGraphs||false; }catch(_){}
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = +2.00; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}

    // Execute and check the result
    var success = P.executeOn(win.mainView);

    if (success) {
        sum.spcc={name:"SPCC",status:"✅",details:"Applied"};
        debugSave(win, "After_SPCC", baseTag, debugDir);
        return true;
    } else {
        // Attempt to gather more error details
        var errorMessage = "SPCC execution reported failure.";
        if (P.errors && P.errors.length > 0) {
            errorMessage += " Errors: " + P.errors.join("; ");
        }
        sum.spcc={name:"SPCC",status:"❌",details:errorMessage};
        debugSave(win, "After_SPCC_Failed", baseTag, debugDir); // Optional: save the failed state for debugging
        return false;
    }
  }catch(e){
    sum.spcc={name:"SPCC",status:"❌",details:e.message};
    return false;
  }
}
