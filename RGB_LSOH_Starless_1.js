/*
 * Post-Integration Pipeline — Mixed Sets Aware (RGB if present + all others)
 * - GUI: pick input/output, "Combine RGB if found", AI toggles (Deblur V1/V2, <PERSON><PERSON>, <PERSON>less)
 * - Detects R/G/B/L/Ha/OIII/SII/etc. from filenames; builds RGB when possible; processes all remaining masters individually
 * - Sequence per output: ABE → Auto Black Point → SPCC (only if WCS present) → AI variants
 * - Exactly ONE timestamped output folder per run
 * - NOTE: No ImageSolver include → no solver popup. SPCC uses existing WCS only.
 */

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>

(function(){

// -------------------- Defaults --------------------
var outputExtension = ".xisf";
var defaults = {
  inputDir:  "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master", // set in GUI
  outputDir: "C:/Users/<USER>/OneDrive/Desktop/Input",               // set in GUI
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, denoise:true, starless:true }
};

// -------------------- Utils --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }

// -------------------- Discovery --------------------
function findAllXISF(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[], ff=new FileFind;
  if(ff.begin(dir+"/*.xisf")){ do{ v.push(dir+"/"+ff.name); }while(ff.next()); }
  return v;
}

function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  // common tokens
  if (s.indexOf("filter-red")>=0 || /\bred\b/.test(s) || /\br\b/.test(s) && s.indexOf("rgb")<0) return "R";
  if (s.indexOf("filter-green")>=0 || /\bgreen\b/.test(s) || /\bg\b/.test(s) && s.indexOf("rgb")<0) return "G";
  if (s.indexOf("filter-blue")>=0 || /\bblue\b/.test(s) || /\bb\b/.test(s) && s.indexOf("rgb")<0) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  // OSC or unknown → return null (will be processed as single anyway)
  return null;
}

function buildWorkPlan(dir, combineRGB){
  var files = findAllXISF(dir);
  var byFilter = {};          // filter → [paths]
  var unknownSingles = [];    // paths without clear filter token
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }

  // Pick first occurrence for each filter (sorted by name for stability)
  function pickFirst(arr){ if(!arr || !arr.length) return null; arr.sort(); return arr[0]; }

  var haveR = !!(byFilter.R && byFilter.R.length);
  var haveG = !!(byFilter.G && byFilter.G.length);
  var haveB = !!(byFilter.B && byFilter.B.length);

  var plan = { doRGB:false, r:null, g:null, b:null, singles:[] };

  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB = true;
    plan.r = pickFirst(byFilter.R);
    plan.g = pickFirst(byFilter.G);
    plan.b = pickFirst(byFilter.B);
  }

  // Add singles: all filters except consumed R/G/B (if used in RGB) + unknowns
  function pushSingles(filterKey){
    if (byFilter[filterKey]){
      for (var i=0;i<byFilter[filterKey].length;++i){
        var p = byFilter[filterKey][i];
        // skip the one consumed by RGB
        if (plan.doRGB && ( (filterKey==="R" && p===plan.r) || (filterKey==="G" && p===plan.g) || (filterKey==="B" && p===plan.b) ))
          continue;
        plan.singles.push({ path:p, tag:filterKey });
      }
    }
  }
  // Push L/Ha/OIII/SII if present
  ["L","Ha","OIII","SII"].forEach(pushSingles);

  // Push any leftover R/G/B that weren't consumed (e.g., RGB not built or extra versions)
  if (!plan.doRGB){ ["R","G","B"].forEach(pushSingles); }

  // Unknown singles last
  for (var k=0;k<unknownSingles.length;++k){
    plan.singles.push({ path:unknownSingles[k], tag:"Single" });
  }

  return plan;
}

// -------------------- RGB Combination --------------------
function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");

  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();

  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");

  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}

// -------------------- Processing Steps --------------------
function finalABE(win, sum){
  Console.writeln("\n=== Final Background Extraction (ABE) ===");
  try{
    var P=new AutomaticBackgroundExtractor();
    P.targetCorrection=1; P.normalize=true; P.discardBackground=true;
    var before=ImageWindow.windows; P.executeOn(win.mainView); var after=ImageWindow.windows;
    for(var k=0;k<after.length;++k){
      var isNew=true; for(var j=0;j<before.length;++j){ if(after[k].mainView.id===before[j].mainView.id){ isNew=false; break; } }
      if(isNew && after[k].mainView.id.toLowerCase().indexOf("background")!==-1){ after[k].forceClose(); break; }
    }
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE complete"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}

function autoBlackPoint(win, sum){
  Console.writeln("\n=== Automatic Black Point Adjustment ===");
  try{
    var img=win.mainView.image;
    if(!img.isColor || img.numberOfChannels<3){
      var AH=new AutoHistogram(); AH.auto=true; AH.clipLow=0.1; AH.clipHigh=0.1; AH.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"AutoHistogram (mono/NB)"}; return true;
    }
    var w=img.width,h=img.height, sampleSize=20, numSamples=20;
    var rs=[],gs=[],bs=[];
    for(var i=0;i<numSamples;++i){
      var x=Math.floor(Math.random()*(w-sampleSize)), y=Math.floor(Math.random()*(h-sampleSize));
      var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;
      for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }
      rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);
    }
    rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});
    var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0; for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; } R/=n; G/=n; B/=n;
    try{
      var L=new Levels(); L.redBlack=Math.min(R*0.8,0.02); L.greenBlack=Math.min(G*0.9,0.03); L.blueBlack=Math.min(B*0.8,0.02);
      L.redWhite=0.98; L.greenWhite=0.97; L.blueWhite=0.98; L.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Levels"};
    }catch(e2){
      var AH2=new AutoHistogram(); AH2.auto=true; AH2.clipLow=0.1; AH2.clipHigh=0.1; AH2.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};
    }
    return true;
  }catch(e){ sum.blackPoint={name:"Black Point",status:"❌",details:e.message}; return false; }
}

// WCS check → decide SPCC without solver
function hasWCS(win){
  try{
    var kws = win.keywords; // FITSKeywordArray
    var flags = {CTYPE1:false, CTYPE2:false, CRVAL1:false, CRVAL2:false};
    for (var i=0;i<kws.length;++i){
      var n=kws[i].name.toUpperCase();
      if (n==="CTYPE1") flags.CTYPE1=true;
      else if (n==="CTYPE2") flags.CTYPE2=true;
      else if (n==="CRVAL1") flags.CRVAL1=true;
      else if (n==="CRVAL2") flags.CRVAL2=true;
    }
    return (flags.CTYPE1 && flags.CTYPE2) || (flags.CRVAL1 && flags.CRVAL2);
  }catch(e){ return false; }
}

function spccIfWCS(win, sum){
  try{
    if(!win.mainView.image.isColor){ sum.spcc={name:"SPCC",status:"⏭️",details:"Mono image"}; return false; }
    if(!hasWCS(win)){ sum.spcc={name:"SPCC",status:"⏭️",details:"No WCS → skipped (no solver invoked)"}; return false; }
    Console.writeln("\n=== SPCC (using existing WCS) ===");
    var P=new SpectrophotometricColorCalibration();
    P.whiteReferenceId="AVG_G2V"; P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0;
    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied with existing WCS"};
    return true;
  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}

// AI steps
function deblur1(win, sum){
  try{ var P=new BlurXTerminator(); P.sharpenStars=0.15; P.adjustStarHalos=0; P.autoPSF=true; P.psfDiameter=0; P.sharpenNonstellar=0; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false; P.executeOn(win.mainView);
       sum.deblurV1={name:"Deblur V1",status:"✅",details:"Applied"}; return true; }
  catch(e){ sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message}; return false; }
}
function deblur2(win, sum){
  try{ var P=new BlurXTerminator(); P.sharpenStars=0.25; P.adjustStarHalos=0; P.autoPSF=true; P.psfDiameter=0; P.sharpenNonstellar=0.90; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false; P.executeOn(win.mainView);
       sum.deblurV2={name:"Deblur V2",status:"✅",details:"Applied"}; return true; }
  catch(e){ sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message}; return false; }
}
function denoise(win, sum){
  try{ var P=new NoiseXTerminator(); P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2; P.executeOn(win.mainView);
       sum.denoising={name:"Denoising",status:"✅",details:"Applied"}; return true; }
  catch(e){ sum.denoising={name:"Denoising",status:"❌",details:e.message}; return false; }
}
function starless(win, sum, finalDir, baseTag){
  try{
    var P=new StarXTerminator(); P.generateStarImage=true; P.unscreenStars=true; P.largeOverlap=false; P.executeOn(win.mainView);
    var saved=false, wins=ImageWindow.windows;
    for(var i=0;i<wins.length;++i){
      var id=wins[i].mainView.id;
      if(id.indexOf("stars")!==-1 || id.indexOf("Stars")!==-1){
        wins[i].saveAs(finalDir+"/Stars_Extracted_"+baseTag+outputExtension,false,false,false,false);
        try{ wins[i].forceClose(); }catch(_){}
        saved=true; break;
      }
    }
    sum.starSeparation={name:"Star Separation",status:"✅",details:(saved?"Saved stars":"")}; return true;
  }catch(e){ sum.starSeparation={name:"Star Separation",status:"❌",details:e.message}; return false; }
}

// -------------------- Per-Image Pipeline --------------------
function processOne(win, baseTag, rootOut, ai){
  var sum={};
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);

  win.saveAs(stackDir+"/Integration_"+baseTag+outputExtension,false,false,false,false);
  sum.finalSave={name:"Final Save",status:"✅",details:"Integration saved"};
  closeAllWindowsExcept([win.mainView.id]);

  finalABE(win,sum);             closeAllWindowsExcept([win.mainView.id]);
  autoBlackPoint(win,sum);       closeAllWindowsExcept([win.mainView.id]);
  spccIfWCS(win,sum);            closeAllWindowsExcept([win.mainView.id]);

  var baseline=finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Final Save",status:"✅",details:"Baseline saved"};

  if(ai.deblur1){ var w1=ImageWindow.open(baseline)[0]; if(w1 && deblur1(w1,sum)) w1.saveAs(finalDir+"/Deblurred_V1_StarRounding_"+baseTag+outputExtension,false,false,false,false); try{ w1.forceClose(); }catch(_){} }
  if(ai.deblur2){ var w2=ImageWindow.open(baseline)[0]; if(w2 && deblur2(w2,sum)) w2.saveAs(finalDir+"/Deblurred_V2_Enhancement_"+baseTag+outputExtension,false,false,false,false); try{ w2.forceClose(); }catch(_){} }
  if(ai.denoise){ var w3=ImageWindow.open(baseline)[0]; if(w3 && denoise(w3,sum))  w3.saveAs(finalDir+"/Denoised_"+baseTag+outputExtension,false,false,false,false); try{ w3.forceClose(); }catch(_){} }
  if(ai.starless){ var w4=ImageWindow.open(baseline)[0]; if(w4 && starless(w4,sum,finalDir,baseTag)) w4.saveAs(finalDir+"/Starless_"+baseTag+outputExtension,false,false,false,false); try{ w4.forceClose(); }catch(_){} }

  Console.writeln("\n— Summary: "+baseTag+" —");
  for(var k in sum){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
}

// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog; dlg.windowTitle="Post-Integration Pipeline (Mixed Sets)";
  dlg.sizer=new VerticalSizer; dlg.sizer.margin=10; dlg.sizer.spacing=8;

  var head=new Label(dlg); head.text="Utah Masterclass — Post-Integration Pipeline"; head.textAlignment=TextAlign_Center; dlg.sizer.add(head);

  var gIn=new GroupBox(dlg); gIn.title="Input Directory"; gIn.sizer=new VerticalSizer; gIn.sizer.margin=8; gIn.sizer.spacing=6;
  var rowIn=new HorizontalSizer; rowIn.spacing=6;
  var editIn=new Edit(dlg); editIn.readOnly=true; editIn.minWidth=480; editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg); btnIn.text="Browse...";
  btnIn.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Input Directory"; d.initialDirectory=state.inputDir; if(d.execute()){ state.inputDir=d.directory.replace(/\\/g,"/").replace(/\/$/,""); editIn.text=state.inputDir; } };
  rowIn.add(editIn,100); rowIn.add(btnIn); gIn.sizer.add(rowIn); dlg.sizer.add(gIn);

  var gOut=new GroupBox(dlg); gOut.title="Output Base Directory (a timestamped subfolder will be created)"; gOut.sizer=new VerticalSizer; gOut.sizer.margin=8; gOut.sizer.spacing=6;
  var rowOut=new HorizontalSizer; rowOut.spacing=6;
  var editOut=new Edit(dlg); editOut.readOnly=true; editOut.minWidth=480; editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg); btnOut.text="Browse...";
  btnOut.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Output Base Directory"; d.initialDirectory=state.outputDir; if(d.execute()){ state.outputDir=d.directory.replace(/\\/g,"/").replace(/\/$/,""); editOut.text=state.outputDir; } };
  rowOut.add(editOut,100); rowOut.add(btnOut); gOut.sizer.add(rowOut); dlg.sizer.add(gOut);

  var gOpt=new GroupBox(dlg); gOpt.title="Options"; gOpt.sizer=new VerticalSizer; gOpt.sizer.margin=8; gOpt.sizer.spacing=6;
  var cbCombine=new CheckBox(dlg); cbCombine.text="Combine RGB if R+G+B masters are found (also process all other masters individually)"; cbCombine.checked=state.combineRGB; gOpt.sizer.add(cbCombine);
  var rowAI1=new HorizontalSizer; rowAI1.spacing=12;
  var cbD1=new CheckBox(dlg); cbD1.text="Deblur V1 (round stars)"; cbD1.checked=state.ai.deblur1;
  var cbD2=new CheckBox(dlg); cbD2.text="Deblur V2 (enhancement)";  cbD2.checked=state.ai.deblur2;
  rowAI1.add(cbD1); rowAI1.add(cbD2); rowAI1.addStretch(); gOpt.sizer.add(rowAI1);
  var rowAI2=new HorizontalSizer; rowAI2.spacing=12;
  var cbDN=new CheckBox(dlg); cbDN.text="Denoise"; cbDN.checked=state.ai.denoise;
  var cbSL=new CheckBox(dlg); cbSL.text="Starless"; cbSL.checked=state.ai.starless;
  rowAI2.add(cbDN); rowAI2.add(cbSL); rowAI2.addStretch(); gOpt.sizer.add(rowAI2);
  dlg.sizer.add(gOpt);

  var rowBtn=new HorizontalSizer; rowBtn.spacing=8; rowBtn.addStretch();
  var bStart=new PushButton(dlg); bStart.text="Start"; bStart.defaultButton=true;
  var bCancel=new PushButton(dlg); bCancel.text="Cancel";
  rowBtn.add(bStart); rowBtn.add(bCancel); dlg.sizer.add(rowBtn);

  bCancel.onClick=function(){ dlg.cancel(); };
  bStart.onClick=function(){
    if(!File.directoryExists(state.inputDir)){ (new MessageBox("Input directory does not exist:\n"+state.inputDir,"Error",StdIcon_Error,StdButton_Ok)).execute(); return; }
    if(!File.directoryExists(state.outputDir)){ (new MessageBox("Output base directory does not exist:\n"+state.outputDir,"Error",StdIcon_Error,StdButton_Ok)).execute(); return; }
    state.combineRGB = cbCombine.checked;
    state.ai.deblur1=cbD1.checked; state.ai.deblur2=cbD2.checked; state.ai.denoise=cbDN.checked; state.ai.starless=cbSL.checked;
    dlg.ok();
  };

  return dlg.execute();
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (Mixed Sets) ===");

  var state={ inputDir:defaults.inputDir, outputDir:defaults.outputDir, combineRGB:defaults.combineRGB, ai:{ deblur1:defaults.ai.deblur1, deblur2:defaults.ai.deblur2, denoise:defaults.ai.denoise, starless:defaults.ai.starless } };
  if(!showGUI(state)){ Console.writeln("Cancelled."); return; }

  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);

  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder (single): "+root);
  ensureDir(root+"/5_stacked"); ensureDir(root+"/6_final");

  try{
    var plan = buildWorkPlan(state.inputDir, state.combineRGB);

    // Do RGB first if planned
    if (plan.doRGB){
      Console.writeln("\n→ Building RGB from:");
      Console.writeln("   R: "+File.extractName(plan.r));
      Console.writeln("   G: "+File.extractName(plan.g));
      Console.writeln("   B: "+File.extractName(plan.b));
      var combo = combineRGB(plan.r, plan.g, plan.b, root);
      processOne(combo.window, combo.base, root, state.ai);
      try{ combo.window.forceClose(); }catch(_){}
    } else {
      Console.writeln("\n(No full RGB set found or combining disabled)");
    }

    // Process all singles
    if (plan.singles.length===0){
      Console.writeln("\nNo remaining masters found to process.");
    } else {
      Console.writeln("\n→ Processing singles: "+plan.singles.length);
      for (var i=0;i<plan.singles.length;++i){
        var p = plan.singles[i].path;
        var tag = plan.singles[i].tag;
        Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");
        var w=ImageWindow.open(p);
        if(w.length===0){ Console.writeln("  ⚠️ Could not open, skipping."); continue; }
        var win=w[0], base= tag + "_" + sanitizeBase(File.extractName(p));
        processOne(win, base, root, state.ai);
        try{ win.forceClose(); }catch(_){}
        closeAllWindowsExcept(null);
      }
    }

    Console.writeln("\n=== Done. Output: "+root+" ===");
  }catch(err){
    Console.criticalln("Error: "+err.message); throw err;
  }
}

run();

})(); // IIFE
