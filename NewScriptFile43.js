/*
 * RGB_LSOH_Starless_2.js — COMPLETE REVISED (fixes File.existingDirectory, robust I/O guards)
 *
 * - Builds RGB if R/G/B present; processes singles.
 * - Flow: ABE → BlackPoint (PixelMath offset) → ImageSolver → SPCC (color) → BXT v1/v2
 *         → Stretch (AutoSTF→HT) → NXT → StarX (starless) → Stars = (orig − starless)
 *         → Optional Color Enhance on RGB STRETCHED stars.
 * - Skips mono entirely if ALL mono save options are disabled.
 * - GUI has no StdIcon_* dependency.
 */

#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>

(function(){

// -------------------- Global params --------------------
var STF_UNLINKED  = true;
var STF_K_SHADOWS = -3.0;
var STF_HIGHLIGHTS= 0.985;
var outputExtension = ".xisf";

// -------------------- Defaults --------------------
var defaults = {
  inputDir:  "",
  outputDir: "",
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },

  colorEnhanceRGBStarsStretched: true,

  save: {
    rgb: {
      final_stretched: true,
      final_linear:    true,
      stars_linear:          false,
      stars_stretched:       true,
      starless_linear:       false,
      starless_stretched:    true,
      integration_linear:    false,
      baseline_linear:       false,
      deblur1:               false,
      deblur2:               false,
      denoised:              false
    },
    mono: {
      final_stretched: false,
      final_linear:    false,
      stars_linear:          false,
      stars_stretched:       false,
      starless_linear:       false,
      starless_stretched:    true,
      integration_linear:    false,
      baseline_linear:       false,
      deblur1:               false,
      deblur2:               false,
      denoised:              false
    }
  }
};

// -------------------- Utils --------------------
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  if (!base || !File.directoryExists(base)) {
    throw new Error("Output directory is not set or does not exist.");
  }
  function p2(n){ return n<10?"0"+n:n; }
  var d=new Date;
  var stamp = d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\/$/,"")+"/Processed_"+stamp;
  ensureDir(out); ensureDir(out+"/5_stacked"); ensureDir(out+"/6_final");
  return out;
}
function sanitizeBase(s){ return s.replace(/[^\w\-\.]+/g,"_"); }
function removeIf(p, keep){ if(!keep && File.exists(p)) { try{ File.remove(p);}catch(_){ } } }
function closeAllWindowsExcept(idsOrNull){
  var keep = {};
  if(idsOrNull && idsOrNull.length) for (var i=0;i<idsOrNull.length;++i) keep[idsOrNull[i]]=true;
  var wins=ImageWindow.windows;
  for (var j=wins.length-1;j>=0;--j){
    var id=wins[j].mainView.id;
    if(idsOrNull && keep[id]) continue;
    try{ wins[j].forceClose(); }catch(_){}
  }
}
function computeSTF(img){
  var stf=new ScreenTransferFunction;
  stf.STF = stf.computeAutoStretch(img, STF_K_SHADOWS, STF_HIGHLIGHTS, STF_UNLINKED);
  return stf;
}
function applyHT(view, stf){
  var HT=new HistogramTransformation;
  HT.H = stf.asHistogramTransformation();
  HT.executeOn(view);
}

// -------------------- Background + Black Point --------------------
function finalABE(win,sum){
  try{
    var P = new AutomaticBackgroundExtractor;
    P.functionDegree = 2; P.smoothingFactor = 0.25; P.convergenceLimit = 0.001;
    P.maximumIterations=50; P.largeScale=256; P.targetBackground=0.10;
    P.generateBackgroundModel=false; P.replaceTarget=true;
    P.executeOn(win.mainView);
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE quadratic"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"❌",details:e.message}; }
}
// Robust black-point using PixelMath offset — avoids Image.getSamples()
function autoBlackPoint(win,sum){
  try{
    var PM = new PixelMath;
    PM.expression = "max($T-0.005,0)";
    PM.useSingleExpression = true;
    PM.createNewImage = false;
    PM.executeOn(win.mainView);
    sum.blackPoint={name:"Black Point",status:"✅",details:"PixelMath offset 0.005"};
  }catch(e){ sum.blackPoint={name:"Black Point",status:"❌",details:e.message}; }
}

// -------------------- Solver + SPCC --------------------
function solveImage(win,sum){
  try{
    var ok = ImageSolver.apply(win);
    sum.solver = {name:"ImageSolver",status: ok?"✅":"❌", details: ok?"Solved (GAIA DR3)":"Failed"};
    return ok;
  }catch(e){
    sum.solver = {name:"ImageSolver",status:"❌",details:e.message}; return false;
  }
}
function performSPCC(win,sum/*,profile*/){
  try{
    var P = new SPCC;
    // Leave defaults; profile enums vary across versions.
    P.applyBrightnessNormalization = true;
    P.saturationBoost = 0.12;
    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied"};
  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; }
}

// -------------------- Deblur V1/V2 --------------------
function deblur1(win,sum){
  try{
    var BXT=new BlurXTerminator;
    BXT.correctFirst = true;
    BXT.starHaloRadius = 28;
    BXT.haloControl = 0.35;
    BXT.deconvolutionIterations = 20;
    BXT.executeOn(win.mainView);
    sum.deblurV1={name:"Deblur V1",status:"✅",details:"Round stars"};
    return true;
  }catch(e){ sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message}; return false; }
}
function deblur2(win,sum){
  try{
    var BXT=new BlurXTerminator;
    BXT.correctFirst = false;
    BXT.enhance = 0.35;
    BXT.executeOn(win.mainView);
    sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};
    return true;
  }catch(e){ sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message}; return false; }
}

// -------------------- Stretch --------------------
function stretchImageAtStage(inPath, outPath, saveFlag){
  var w = ImageWindow.open(inPath)[0];
  if(!w) return null;
  var stf=computeSTF(w.mainView.image);
  applyHT(w.mainView, stf);
  if(saveFlag) w.saveAs(outPath,false,false,false,false);
  return w;
}

// -------------------- Denoise --------------------
function denoise(win,sum){
  try{
    var NXT=new NoiseXTerminator;
    NXT.detail = 0.05;
    NXT.noise = 0.9;
    NXT.linear = false;
    NXT.executeOn(win.mainView);
    sum.denoising={name:"Denoise",status:"✅",details:"NXT 0.9"};
    return true;
  }catch(e){ sum.denoising={name:"Denoise",status:"❌",details:e.message}; return false; }
}

// ---------- Color Enhance (RGB Stars stretched only) ----------
function applyColorEnhanceToView(view){
  try{
    var C=new CurvesTransformation;
    C.R = [[0,0],[1,1]]; C.G = [[0,0],[1,1]]; C.B = [[0,0],[1,1]];
    C.K = [[0,0],[1,1]]; C.A = [[0,0],[1,1]]; C.L = [[0,0],[1,1]];
    C.a = [[0,0],[1,1]]; C.b = [[0,0],[1,1]]; C.c = [[0,0],[1,1]]; C.H = [[0,0],[1,1]];
    C.S = [[0.00000,0.00000],[0.14470,0.33247],[1.00000,1.00000]];
    C.Rt=C.AkimaSubsplines; C.Gt=C.AkimaSubsplines; C.Bt=C.AkimaSubsplines;
    C.Kt=C.AkimaSubsplines; C.At=C.AkimaSubsplines; C.Lt=C.AkimaSubsplines;
    C.at=C.AkimaSubsplines; C.bt=C.AkimaSubsplines; C.ct=C.AkimaSubsplines; C.Ht=C.AkimaSubsplines; C.St=C.AkimaSubsplines;
    C.executeOn(view);
    var N=new SCNR; N.amount=0.73; N.protectionMethod=SCNR.prototype.Median; N.colorToRemove=SCNR.prototype.Green; N.preserveLightness=true;
    N.executeOn(view);
  }catch(_){}
}

// ---------- Starless/Stars by Difference ----------
function saveAndClose(win, path){
  if(win){
    win.saveAs(path,false,false,false,false);
    try{ win.forceClose(); }catch(_){}
  }
}
function pixelMathDiffOnDuplicate(newId, A_win, B_win){
  var out = A_win.duplicate();
  out.mainView.id = newId;
  var PM = new PixelMath;
  PM.expression = "A - B";
  PM.symbols = "A="+A_win.mainView.id+";B="+B_win.mainView.id+";";
  PM.useSingleExpression = true;
  PM.createNewImage = false;
  PM.executeOn(out.mainView);
  return out;
}
function buildStarlessAndStarsByDifference(finalDir, baseTag, isRGB, enhanceRGBStarsStretched,
                                           wantStarlessL, wantStarsL, wantStarlessS, wantStarsS)
{
  var ext = outputExtension;
  var pathLinearOrig    = finalDir+"/Final_Stacked_"+baseTag+ext;
  var pathStretchedOrig = finalDir+"/Stretched_"+baseTag+ext;
  var pathStarlessL = finalDir+"/Starless_"+baseTag+ext;
  var pathStarsL    = finalDir+"/Stars_"+baseTag+ext;
  var pathStarlessS = finalDir+"/Starless_Stretched_"+baseTag+ext;
  var pathStarsS    = finalDir+"/Stars_Stretched_"+baseTag+ext;

  // Linear
  if ((wantStarlessL || wantStarsL) && File.exists(pathLinearOrig)){
    var wLin = ImageWindow.open(pathLinearOrig)[0];
    if (wLin){
      try{
        var SXT = new StarXTerminator();
        SXT.generateStarImage = false;
        SXT.unscreenStars = false;
        SXT.largeOverlap = false;
        SXT.executeOn(wLin.mainView);
        if (wantStarlessL) wLin.saveAs(pathStarlessL,false,false,false,false);
      }catch(e){ Console.criticalln("StarX (linear) failed: "+e.message); }
      try{ wLin.forceClose(); }catch(_){}
    }
    if (wantStarsL && File.exists(pathStarlessL)){
      var wA = ImageWindow.open(pathLinearOrig)[0];
      var wB = ImageWindow.open(pathStarlessL)[0];
      if (wA && wB){
        var starsL = pixelMathDiffOnDuplicate("StarsL_"+baseTag, wA, wB);
        saveAndClose(starsL, pathStarsL);
      }
      try{ if(wA) wA.forceClose(); }catch(_){}
      try{ if(wB) wB.forceClose(); }catch(_){}
    }
  }

  // Stretched
  if ((wantStarlessS || wantStarsS) && File.exists(pathStretchedOrig)){
    var wStr = ImageWindow.open(pathStretchedOrig)[0];
    if (wStr){
      try{
        var SXT2 = new StarXTerminator();
        SXT2.generateStarImage = false;
        SXT2.unscreenStars = false;
        SXT2.largeOverlap = false;
        SXT2.executeOn(wStr.mainView);
        if (wantStarlessS) wStr.saveAs(pathStarlessS,false,false,false,false);
      }catch(e2){ Console.criticalln("StarX (stretched) failed: "+e2.message); }
      try{ wStr.forceClose(); }catch(_){}
    }
    if (wantStarsS && File.exists(pathStarlessS)){
      var wA2 = ImageWindow.open(pathStretchedOrig)[0];
      var wB2 = ImageWindow.open(pathStarlessS)[0];
      if (wA2 && wB2){
        var starsS = pixelMathDiffOnDuplicate("StarsS_"+baseTag, wA2, wB2);
        if (isRGB && enhanceRGBStarsStretched){
          try{ applyColorEnhanceToView(starsS.mainView); }catch(_){}
        }
        saveAndClose(starsS, pathStarsS);
      }
      try{ if(wA2) wA2.forceClose(); }catch(_){}
      try{ if(wB2) wB2.forceClose(); }catch(_){}
    }
  }
}

// -------------------- Star separation wrapper --------------------
function starSeparation(win, sum, finalDir, baseTag, opts, isRGBCombined, enhanceRGBStarsStretched){
  var need = opts.stars_linear || opts.stars_stretched || opts.starless_linear || opts.starless_stretched;
  if(!need){ sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No outputs requested"}; return true; }
  try{
    buildStarlessAndStarsByDifference(
      finalDir,
      baseTag,
      /*isRGB*/win.mainView.image.isColor,
      /*enhanceRGBStarsStretched*/(isRGBCombined && enhanceRGBStarsStretched),
      /*wantStarlessL*/opts.starless_linear,
      /*wantStarsL*/opts.stars_linear,
      /*wantStarlessS*/opts.starless_stretched,
      /*wantStarsS*/opts.stars_stretched
    );
    sum.starSeparation={name:"Star Separation",status:"✅",details:"Starless via StarX; Stars=orig−starless"};
    return true;
  }catch(e){
    sum.starSeparation={name:"Star Separation",status:"❌",details:e.message};
    return false;
  }
}

// -------------------- Per-Image Pipeline --------------------
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched){
  var sum={};

  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);

  // Save integration (linear) for pipeline continuity
  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};
  closeAllWindowsExcept([win.mainView.id]);

  // Linear cleanup
  finalABE(win,sum);             closeAllWindowsExcept([win.mainView.id]);
  autoBlackPoint(win,sum);       closeAllWindowsExcept([win.mainView.id]);

  // Solve + SPCC (color only)
  var isColor = win.mainView.image.isColor;
  if(isColor){
    var solved = solveImage(win, sum);
    if(solved){
      performSPCC(win, sum/*, isRGBCombined ? "RGB" : "OSC"*/);
    } else {
      sum.spcc = {name:"SPCC",status:"⏭️",details:"Skipped (no WCS)"};
    }
  } else {
    sum.solver = {name:"ImageSolver",status:"⏭️",details:"Skipped (mono/NB)"};
    sum.spcc   = {name:"SPCC",status:"⏭️",details:"Skipped (mono/NB)"};
  }
  closeAllWindowsExcept([win.mainView.id]);

  // Baseline (linear with stars)
  var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
  win.saveAs(baseline,false,false,false,false);
  sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};

  // Deblur V1/V2 (optional)
  if(ai.deblur1){
    var w1=ImageWindow.open(baseline)[0];
    if(w1 && deblur1(w1,sum) && saveCfg.deblur1)
      w1.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
    try{ w1.forceClose(); }catch(_){}
  }
  if(ai.deblur2){
    var w2=ImageWindow.open(baseline)[0];
    if(w2 && deblur2(w2,sum) && saveCfg.deblur2)
      w2.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
    try{ w2.forceClose(); }catch(_){}
  }

  // Stretch
  var stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
  if(ai.stretch){
    var wS = stretchImageAtStage(baseline, stretchedPath, /*saveFlag*/true);
    if(wS) try{ wS.forceClose(); }catch(_){}
  }

  // Denoise (stretched)
  if(ai.denoise && ai.stretch){
    var w3 = ImageWindow.open(stretchedPath)[0];
    if(w3 && denoise(w3,sum) && saveCfg.denoised)
      w3.saveAs(finalDir+"/Denoised_"+baseTag+outputExtension,false,false,false,false);
    try{ w3.forceClose(); }catch(_){}
  }

  // StarX + stars difference
  if(ai.starless){
    var needSXT = (saveCfg.stars_linear||saveCfg.stars_stretched||saveCfg.starless_linear||saveCfg.starless_stretched);
    if(needSXT){
      var w4=ImageWindow.open(baseline)[0];
      if(w4) starSeparation(w4,sum,finalDir,baseTag,{
        stars_linear:       saveCfg.stars_linear,
        stars_stretched:    saveCfg.stars_stretched,
        starless_linear:    saveCfg.starless_linear,
        starless_stretched: saveCfg.starless_stretched
      }, isRGBCombined, defaults.colorEnhanceRGBStarsStretched);
      try{ w4.forceClose(); }catch(_){}
    }else{
      sum.starSeparation={name:"Star Separation",status:"⏭️",details:"No outputs requested"};
    }
  }

  // Clean up intermediates if user doesn't want them
  removeIf(integrationPath, saveCfg.integration_linear===true);
  removeIf(baseline,        saveCfg.baseline_linear===true);

  if(ai.stretch){
    var needStretchedFinal = saveCfg.final_stretched===true;
    if(!needStretchedFinal && saveCfg.denoised!==true)
      removeIf(stretchedPath, /*keep*/false);
  }
  if(!saveCfg.final_linear)
    removeIf(baseline, saveCfg.baseline_linear===true);

  // Summary
  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction","blackPoint","solver","spcc","deblurV1","deblurV2","denoising","starSeparation","finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}

// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog; dlg.windowTitle="Post-Integration Pipeline (Starless + Stars by Difference)";
  dlg.sizer=new VerticalSizer; dlg.sizer.margin=10; dlg.sizer.spacing=8;

  var head=new Label(dlg); head.text="Post-Integration Pipeline — RGB + Singles"; head.textAlignment=TextAlign_Center; dlg.sizer.add(head);

  var gIn=new GroupBox(dlg); gIn.title="Input Directory"; gIn.sizer=new VerticalSizer; gIn.sizer.margin=8; gIn.sizer.spacing=6;
  var rowIn=new HorizontalSizer; rowIn.spacing=6;
  var editIn=new Edit(dlg); editIn.readOnly=true; editIn.minWidth=560; editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg); btnIn.text="Browse...";
  btnIn.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Input Folder"; if(d.execute()){ state.inputDir=(d.directory).replace(/\/$/,""); editIn.text=state.inputDir; } };
  rowIn.add(editIn,100); rowIn.add(btnIn); gIn.sizer.add(rowIn); dlg.sizer.add(gIn);

  var gOut=new GroupBox(dlg); gOut.title="Output Directory"; gOut.sizer=new VerticalSizer; gOut.sizer.margin=8; gOut.sizer.spacing=6;
  var rowOut=new HorizontalSizer; rowOut.spacing=6;
  var editOut=new Edit(dlg); editOut.readOnly=true; editOut.minWidth=560; editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg); btnOut.text="Browse...";
  btnOut.onClick=function(){ var d=new GetDirectoryDialog; d.caption="Select Output Folder"; if(d.execute()){ state.outputDir=(d.directory).replace(/\/$/,""); editOut.text=state.outputDir; } };
  rowOut.add(editOut,100); rowOut.add(btnOut); gOut.sizer.add(rowOut); dlg.sizer.add(gOut);

  var gOpt=new GroupBox(dlg); gOpt.title="Processing Options"; gOpt.sizer=new VerticalSizer; gOpt.sizer.margin=8; gOpt.sizer.spacing=6;
  var cbCombine=new CheckBox(dlg); cbCombine.text="Combine RGB if R/G/B present"; cbCombine.checked=state.combineRGB; gOpt.sizer.add(cbCombine);
  var rowAI1=new HorizontalSizer; rowAI1.spacing=12;
  var cbD1=new CheckBox(dlg); cbD1.text="Deblur V1 (round stars)"; cbD1.checked=state.ai.deblur1;
  var cbD2=new CheckBox(dlg); cbD2.text="Deblur V2 (enhance)";   cbD2.checked=state.ai.deblur2;
  var cbST=new CheckBox(dlg); cbST.text="Stretch (AutoSTF→HT)";  cbST.checked=state.ai.stretch;
  rowAI1.add(cbD1); rowAI1.add(cbD2); rowAI1.add(cbST); rowAI1.addStretch(); gOpt.sizer.add(rowAI1);
  var rowAI2=new HorizontalSizer; rowAI2.spacing=12;
  var cbDN=new CheckBox(dlg); cbDN.text="Denoise (NXT)"; cbDN.checked=state.ai.denoise;
  var cbSX=new CheckBox(dlg); cbSX.text="StarX (build starless; stars=orig−starless)"; cbSX.checked=state.ai.starless;
  rowAI2.add(cbDN); rowAI2.add(cbSX); rowAI2.addStretch(); gOpt.sizer.add(rowAI2);

  var gSave=new GroupBox(dlg); gSave.title="Save Options"; gSave.sizer=new VerticalSizer; gSave.sizer.margin=8; gSave.sizer.spacing=8;

  function buildSaveBox(title, obj){
    var gb=new GroupBox(dlg); gb.title=title; gb.sizer=new VerticalSizer; gb.sizer.margin=6; gb.sizer.spacing=4;

    var finals=new HorizontalSizer; finals.spacing=10;
    var cbFS=new CheckBox(dlg); cbFS.text="Final (stretched)"; cbFS.checked=obj.final_stretched;
    var cbFL=new CheckBox(dlg); cbFL.text="Final (linear)";    cbFL.checked=obj.final_linear;
    finals.add(cbFS); finals.add(cbFL); finals.addStretch(); gb.sizer.add(finals);

    var starsRow=new HorizontalSizer; starsRow.spacing=10;
    var cbStL=new CheckBox(dlg); cbStL.text="Stars (linear)";    cbStL.checked=obj.stars_linear;
    var cbStS=new CheckBox(dlg); cbStS.text="Stars (stretched)"; cbStS.checked=obj.stars_stretched;
    starsRow.add(cbStL); starsRow.add(cbStS); starsRow.addStretch(); gb.sizer.add(starsRow);

    var starlessRow=new HorizontalSizer; starlessRow.spacing=10;
    var cbSlL=new CheckBox(dlg); cbSlL.text="Starless (linear)";    cbSlL.checked=obj.starless_linear;
    var cbSlS=new CheckBox(dlg); cbSlS.text="Starless (stretched)"; cbSlS.checked=obj.starless_stretched;
    starlessRow.add(cbSlL); starlessRow.add(cbSlS); starlessRow.addStretch(); gb.sizer.add(starlessRow);

    var inter=new HorizontalSizer; inter.spacing=10;
    var cbI=new CheckBox(dlg); cbI.text="Save Integration (linear)"; cbI.checked=obj.integration_linear;
    var cbB=new CheckBox(dlg); cbB.text="Save Baseline (linear)";    cbB.checked=obj.baseline_linear;
    var cbD1s=new CheckBox(dlg); cbD1s.text="Save Deblur V1"; cbD1s.checked=obj.deblur1;
    var cbD2s=new CheckBox(dlg); cbD2s.text="Save Deblur V2"; cbD2s.checked=obj.deblur2;
    var cbDNs=new CheckBox(dlg); cbDNs.text="Save Denoised";   cbDNs.checked=obj.denoised;
    inter.add(cbI); inter.add(cbB); inter.add(cbD1s); inter.add(cbD2s); inter.add(cbDNs); inter.addStretch(); gb.sizer.add(inter);

    gb.get=function(){ return {
      final_stretched: cbFS.checked, final_linear: cbFL.checked,
      stars_linear: cbStL.checked, stars_stretched: cbStS.checked,
      starless_linear: cbSlL.checked, starless_stretched: cbSlS.checked,
      integration_linear: cbI.checked, baseline_linear: cbB.checked, deblur1: cbD1s.checked, deblur2: cbD2s.checked, denoised: cbDNs.checked
    }; };
    return gb;
  }

  var gRGB=buildSaveBox("RGB / Color", state.save.rgb);
  var gMono=buildSaveBox("Mono / Narrowband", state.save.mono);
  gSave.sizer.add(gRGB); gSave.sizer.add(gMono);

  var gExtra=new GroupBox(dlg); gExtra.title="Extras"; gExtra.sizer=new VerticalSizer; gExtra.sizer.margin=8; gExtra.sizer.spacing=6;
  var cbEnh=new CheckBox(dlg); cbEnh.text="Color-Enhance RGB Stars (stretched)"; cbEnh.checked=defaults.colorEnhanceRGBStarsStretched;
  gExtra.sizer.add(cbEnh);

  dlg.sizer.add(gOpt);
  dlg.sizer.add(gSave);
  dlg.sizer.add(gExtra);

  // Buttons (no StdIcon dependencies)
  var rowBtns=new HorizontalSizer; rowBtns.spacing=8; rowBtns.addStretch();
  var ok=new PushButton(dlg);     ok.text="Run";
  var cancel=new PushButton(dlg); cancel.text="Cancel";
  rowBtns.add(ok); rowBtns.add(cancel); dlg.sizer.add(rowBtns);

  cancel.onClick=function(){ dlg.cancel(); };
  ok.onClick=function(){
    state.combineRGB = cbCombine.checked;
    state.ai.deblur1 = cbD1.checked; state.ai.deblur2 = cbD2.checked; state.ai.stretch=cbST.checked;
    state.ai.denoise = cbDN.checked; state.ai.starless = cbSX.checked;
    defaults.colorEnhanceRGBStarsStretched = cbEnh.checked;
    state.save.rgb = gRGB.get(); state.save.mono = gMono.get();
    dlg.ok();
  };

  dlg.adjustToContents(); dlg.execute();
  return !dlg.cancelled;
}

// -------------------- Discovery --------------------
function listFilesInDir(dir){
  if (!dir || !File.directoryExists(dir)) return [];
  return File.find(dir.replace(/\/$/,"") + "/*");
}
function findMasters(dir){
  var files = listFilesInDir(dir);
  var masters=[];
  for (var i=0;i<files.length;++i){
    var p = files[i];
    if(p.match(/\.(xisf|fits|fit|xcf|tif|tiff)$/i)){
      var name = File.extractName(p).toLowerCase();
      masters.push({path:p, name:name, tag:guessTag(name)});
    }
  }
  return masters;
}
function guessTag(lc){
  if(/\b(r|red)\b/.test(lc)) return "R";
  if(/\b(g|green)\b/.test(lc)) return "G";
  if(/\b(b|blue)\b/.test(lc)) return "B";
  if(/\b(ha|halpha|h-alpha)\b/.test(lc)) return "Ha";
  if(/\b(oiii|oii|o3)\b/.test(lc)) return "OIII";
  if(/\b(sii|s2)\b/.test(lc)) return "SII";
  return "L";
}

// -------------------- RGB combination --------------------
function tryRGB(root, rPath,gPath,bPath, ai, saveCfg){
  Console.writeln("\n=== RGB Channel Combination ===\n");
  var wR=ImageWindow.open(rPath)[0], wG=ImageWindow.open(gPath)[0], wB=ImageWindow.open(bPath)[0];
  if(!(wR&&wG&&wB)){ Console.warningln("RGB combine: missing channel(s)"); return; }
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels = [ [wR.mainView.id,0], [wG.mainView.id,1], [wB.mainView.id,2] ];
  CC.executeOn(wR.mainView);
  try{ wG.forceClose(); }catch(_){}
  try{ wB.forceClose(); }catch(_){}
  var rgbWin = wR; // combined

  var stackDir=root+"/5_stacked"; ensureDir(stackDir);
  rgbWin.saveAs(stackDir+"/RGB_Combined"+outputExtension,false,false,false,false);
  rgbWin.saveAs(stackDir+"/Integration_RGB_Combined"+outputExtension,false,false,false,false);

  var base="RGB_Combined";
  processOne(rgbWin, base, root, ai, saveCfg, /*isRGBCombined*/true, defaults.colorEnhanceRGBStarsStretched);
  try{ rgbWin.forceClose(); }catch(_){}
}

// -------------------- Main --------------------
function promptForDir(caption){
  var d=new GetDirectoryDialog; d.caption=caption;
  return d.execute() ? (d.directory).replace(/\/$/,"") : "";
}

function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (Mixed Sets) — ImageSolver before SPCC ===");

  var state={ inputDir:defaults.inputDir, outputDir:defaults.outputDir, combineRGB:defaults.combineRGB,
              ai:{ deblur1:defaults.ai.deblur1, deblur2:defaults.ai.deblur2, stretch:defaults.ai.stretch, denoise:defaults.ai.denoise, starless:defaults.ai.starless },
              save:{ rgb:defaults.save.rgb, mono:defaults.save.mono } };

  if(!showGUI(state)){ Console.writeln("Cancelled."); return; }

  // Guard: require valid directories (prevents "/Processed_..." at root)
  if (!state.inputDir || !File.directoryExists(state.inputDir)){
    state.inputDir = promptForDir("Select INPUT folder");
    if (!state.inputDir || !File.directoryExists(state.inputDir)){
      Console.criticalln("Input directory is not set or does not exist."); return;
    }
  }
  if (!state.outputDir || !File.directoryExists(state.outputDir)){
    state.outputDir = promptForDir("Select OUTPUT folder");
    if (!state.outputDir || !File.directoryExists(state.outputDir)){
      Console.criticalln("Output directory is not set or does not exist."); return;
    }
  }

  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);

  var root;
  try{
    root=tsFolder(state.outputDir);
  }catch(e){
    Console.criticalln(e.message); return;
  }
  Console.writeln("Output folder (single): "+root);

  try{
    var masters = findMasters(state.inputDir);
    if(masters.length===0){ Console.writeln("No masters found."); return; }

    // Group by R/G/B, others as singles
    var plan={R:null,G:null,B:null,singles:[]};
    for (var i=0;i<masters.length;++i){
      var m=masters[i];
      if(m.tag==="R") plan.R=m;
      else if(m.tag==="G") plan.G=m;
      else if(m.tag==="B") plan.B=m;
      else plan.singles.push(m);
    }

    if(state.combineRGB && plan.R && plan.G && plan.B){
      Console.writeln("\n→ Building RGB from:\n   R: "+File.extractName(plan.R.path)+"\n   G: "+File.extractName(plan.G.path)+"\n   B: "+File.extractName(plan.B.path));
      tryRGB(root, plan.R.path, plan.G.path, plan.B.path, state.ai, state.save.rgb);
    } else {
      Console.writeln("\n(No full RGB set found or combining disabled)");
    }

    // Singles (mono/NB or leftover color)
    if (plan.singles.length===0){
      Console.writeln("\nNo remaining masters found to process.");
    } else {
      Console.writeln("\n→ Processing singles: "+plan.singles.length);
      for (var i=0;i<plan.singles.length;++i){
        var p = plan.singles[i].path;
        var tag = plan.singles[i].tag;
        Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");
        var w=ImageWindow.open(p);
        if(w.length===0){ Console.writeln("  ⚠️ Could not open, skipping."); continue; }
        var win=w[0], base= tag + "_" + sanitizeBase(File.extractName(p));
        var isMono = !win.mainView.image.isColor;
        var saveCfg = (isMono ? state.save.mono : state.save.rgb);

        // Skip mono entirely if ALL mono outputs are disabled
        if (isMono){
          var m = state.save.mono;
          var allOff = !m.final_stretched && !m.final_linear &&
                       !m.stars_linear && !m.stars_stretched &&
                       !m.starless_linear && !m.starless_stretched &&
                       !m.integration_linear && !m.baseline_linear &&
                       !m.deblur1 && !m.deblur2 && !m.denoised;
          if (allOff){
            Console.writeln("  ⏭️ No outputs requested for this mono image, skipping.");
            try{ win.forceClose(); }catch(_){}
            closeAllWindowsExcept(null);
            continue;
          }
        }

        processOne(win, base, root, state.ai, saveCfg, /*isRGBCombined*/false, /*enhanceStars*/false);
        try{ win.forceClose(); }catch(_){}
        closeAllWindowsExcept(null);
      }
    }

    Console.writeln("\n=== Done. Output: "+root+" ===");
  }catch(err){
    Console.criticalln("Error: "+err.message); throw err;
  }
}

run();

})(); // IIFE
