/*
 * Side-by-Side Stretch Comparison Test
 * Compares the working "perfect" stretch vs new computed STF stretch
 */

// Test image path
var inputFile = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Processed_2025-08-24T06-11-29/5_stacked/RGB_Combined.xisf";
var outputDir = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test/";

// Create output directory if it doesn't exist
try {
    if (!File.directoryExists(outputDir)) {
        File.createDirectory(outputDir, true);
        Console.writeln("Created output directory: " + outputDir);
    } else {
        Console.writeln("Output directory already exists: " + outputDir);
    }
} catch(e) {
    Console.writeln("Directory creation note: " + e.message);
    Console.writeln("Using existing directory: " + outputDir);
}

Console.show();
Console.writeln("=== Side-by-Side Stretch Comparison ===");

/*
 * Method 1: Working "Perfect" Stretch Parameters
 * These are the parameters that produced the "perfect" result
 */
function applyWorkingStretch(view) {
    Console.writeln("\n=== Method 1: Working Perfect Stretch ===");
    
    var HT = new HistogramTransformation;
    HT.H = [
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Red
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Green  
        [0.11743, 0.00698, 1.00000, 0.00000, 1.00000], // Blue
        [0.00000, 0.50000, 1.00000, 0.00000, 1.00000]  // Luminance
    ];
    
    Console.writeln("Working parameters:");
    for (var c = 0; c < HT.H.length; ++c) {
        Console.writeln("  Channel " + c + ": [" + HT.H[c].join(", ") + "]");
    }
    
    HT.executeOn(view);
    Console.writeln("Working stretch applied successfully!");
}

/*
 * Method 2: Computed STF Stretch
 * Uses the new STF algorithm to compute parameters
 */
#define DEFAULT_AUTOSTRETCH_SCLIP -2.80
#define DEFAULT_AUTOSTRETCH_TBGND 0.25
#define DEFAULT_AUTOSTRETCH_CLINK false

function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked )
{
   if ( shadowsClipping == undefined ) shadowsClipping = DEFAULT_AUTOSTRETCH_SCLIP;
   if ( targetBackground == undefined ) targetBackground = DEFAULT_AUTOSTRETCH_TBGND;
   if ( rgbLinked == undefined ) rgbLinked = DEFAULT_AUTOSTRETCH_CLINK;

   var stf = new ScreenTransferFunction;
   var n = view.image.isColor ? 3 : 1;

   var median = view.computeOrFetchProperty( "Median" );
   var mad = view.computeOrFetchProperty( "MAD" );
   mad.mul( 1.4826 );

   if ( rgbLinked && n > 1)
   {
      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
         if ( median.at( c ) > 0.5 )
            ++invertedChannels;

      if ( invertedChannels < n )
      {
         var c0 = 0, m = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c0 += median.at( c ) + shadowsClipping * mad.at( c );
            m += median.at( c );
         }
         c0 = Math.range( c0/n, 0.0, 1.0 );
         m = Math.mtf( targetBackground, m/n - c0 );

         stf.STF = [
                     [c0, 1, m, 0, 1],
                     [c0, 1, m, 0, 1],
                     [c0, 1, m, 0, 1],
                     [0, 1, 0.5, 0, 1]
                   ];
      }
      else
      {
         var c1 = 0, m = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c1 += median.at( c ) - shadowsClipping * mad.at( c );
            m += median.at( c );
         }
         c1 = Math.range( c1/n, 0.0, 1.0 );
         m = Math.mtf( c1 - m/n, targetBackground );

         stf.STF = [
                     [0, c1, m, 0, 1],
                     [0, c1, m, 0, 1],
                     [0, c1, m, 0, 1],
                     [0, 1, 0.5, 0, 1]
                   ];
      }
   }
   else
   {
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];

      for ( var c = 0; c < n; ++c )
      {
         if ( median.at( c ) < 0.5 )
         {
            var c0 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
            var m = Math.mtf( targetBackground, median.at( c ) - c0 );
            A[c] = [c0, 1, m, 0, 1];
         }
         else
         {
            var c1 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
            var m = Math.mtf( c1 - median.at( c ), targetBackground );
            A[c] = [0, c1, m, 0, 1];
         }
      }
      stf.STF = A;
   }

   return stf;
}

function applyComputedStretch(view) {
    Console.writeln("\n=== Method 2: Computed STF Stretch ===");
    
    var stf = STFAutoStretch( view, -2.80, 0.25, false );
    
    Console.writeln("Computed STF parameters:");
    for (var c = 0; c < stf.STF.length; ++c) {
        Console.writeln("  Channel " + c + ": [" + stf.STF[c].join(", ") + "]");
    }
    
    var ht = new HistogramTransformation;
    var H = [
        [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], 
        [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
    ];

    var n = view.image.isColor ? 3 : 1;
    if ( view.image.isColor )
    {
        var linked = true;
        for(var c = 1; c < n; ++c) {
            if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
                linked = false;
                break;
            }
        }

        if(linked) {
            H[3][0] = stf.STF[0][0];
            H[3][1] = stf.STF[0][1];
            H[3][2] = stf.STF[0][2];
        } else {
            for ( var c = 0; c < n; ++c )
            {
               H[c][0] = stf.STF[c][0];
               H[c][1] = stf.STF[c][1];
               H[c][2] = stf.STF[c][2];
            }
        }
    }
    else
    {
        H[0][0] = stf.STF[0][0];
        H[0][1] = stf.STF[0][1];
        H[0][2] = stf.STF[0][2];
    }

    ht.H = H;
    
    Console.writeln("Final HT parameters:");
    for (var c = 0; c < H.length; ++c) {
        Console.writeln("  HT Channel " + c + ": [" + H[c].join(", ") + "]");
    }
    
    ht.executeOn( view );
    Console.writeln("Computed stretch applied successfully!");
}

// Main execution
function main()
{
   try {
       Console.writeln("Opening: " + inputFile);
       var windows = ImageWindow.open(inputFile);
       if (windows.length === 0) {
           throw new Error("Failed to open file: " + inputFile);
       }
       
       var originalWindow = windows[0];
       Console.writeln("File opened successfully: " + originalWindow.mainView.id);

       // Create two copies for comparison
       var workingWindow = new ImageWindow(originalWindow.mainView.image.width, 
                                          originalWindow.mainView.image.height,
                                          originalWindow.mainView.image.numberOfChannels, 
                                          32, true, originalWindow.mainView.image.isColor, 
                                          "Working_Stretch");
       workingWindow.mainView.beginProcess();
       workingWindow.mainView.image.assign(originalWindow.mainView.image);
       workingWindow.mainView.endProcess();
       workingWindow.show();

       var computedWindow = new ImageWindow(originalWindow.mainView.image.width, 
                                           originalWindow.mainView.image.height,
                                           originalWindow.mainView.image.numberOfChannels, 
                                           32, true, originalWindow.mainView.image.isColor, 
                                           "Computed_Stretch");
       computedWindow.mainView.beginProcess();
       computedWindow.mainView.image.assign(originalWindow.mainView.image);
       computedWindow.mainView.endProcess();
       computedWindow.show();

       // Apply working stretch to first copy
       applyWorkingStretch(workingWindow.mainView);
       
       // Apply computed stretch to second copy
       applyComputedStretch(computedWindow.mainView);

       // Save results
       var workingPath = outputDir + "Working_Perfect_Stretch.xisf";
       workingWindow.saveAs(workingPath, false, false, false, false);
       Console.writeln("Working stretch saved: " + workingPath);

       var computedPath = outputDir + "Computed_STF_Stretch.xisf";
       computedWindow.saveAs(computedPath, false, false, false, false);
       Console.writeln("Computed stretch saved: " + computedPath);

       // Save as FITS for inspection
       var workingFits = outputDir + "Working_Perfect_Stretch.fit";
       workingWindow.saveAs(workingFits, false, false, false, true);
       
       var computedFits = outputDir + "Computed_STF_Stretch.fit";
       computedWindow.saveAs(computedFits, false, false, false, true);

       Console.writeln("\n=== Comparison Complete ===");
       Console.writeln("Compare these files:");
       Console.writeln("1. Working_Perfect_Stretch.xisf (the 'perfect' result)");
       Console.writeln("2. Computed_STF_Stretch.xisf (new algorithm result)");
       
   } catch (error) {
       Console.criticalln("Error: " + error.message);
   }
}

main();
