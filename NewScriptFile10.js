/*
 * Post-Integration Pipeline — RGB & Monochrome (Fixed Stretch Profile)
 * WORK2 VERSION - BACKUP
 * - Uses an absolute path for the GraXpert library to ensure it is found.
 * - Uses a direct library call to GraXpert for maximum reliability.
 */
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
// --- UPDATED: Using the absolute path to the GraXpert library ---
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Debugger Control --------------------
var DEBUG_MODE = true;
var INTERACTIVE_MODE = true;  // Enable interactive step-by-step review
var debugStepCounter = 1;

// Perfect nuclear stretch parameters are now embedded in applyPerfectNuclearStretch() function
var outputExtension = ".xisf";
var defaults = {
  inputDir:  "",
  outputDir: "",
  processRGB:       true,
  processMonochrome:true,
  combineRGB: true,
  ai: { deblur1:true, deblur2:true, stretch:true, denoise:true, starless:true },
  colorEnhanceRGBStarsStretched: true,
  spccGraphs: false,
  save: {
    rgb: { final_stretched: false, final_linear: false, stars_stretched: true, starless_stretched: false, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false },
    mono: { final_stretched: false, final_linear: false, stars_stretched: false, starless_stretched: true, starless_linear: false, integration_linear: false, baseline_linear: false, deblur1: false, deblur2: false, denoised: false }
  }
};
function ensureDir(p){ if(!File.directoryExists(p)) File.createDirectory(p); }
function tsFolder(base){
  function p2(n){ return n<10?"0"+n:""+n; }
  var d=new Date();
  var ts=d.getFullYear()+"-"+p2(d.getMonth()+1)+"-"+p2(d.getDate())+"T"+p2(d.getHours())+"-"+p2(d.getMinutes())+"-"+p2(d.getSeconds());
  var out=base.replace(/\\/g,"/").replace(/\/$/,"")+"/Processed_"+ts;
  ensureDir(out); return out;
}
function closeAllWindowsExcept(ids){
  var wins=ImageWindow.windows;
  for(var i=wins.length-1;i>=0;--i){
    var keep=false;
    if(ids){ for(var j=0;j<ids.length;++j){ if(wins[i].mainView.id===ids[j]){ keep=true; break; } } }
    if(!keep){ try{ wins[i].forceClose(); }catch(_){ } }
  }
}
function sanitizeBase(name){ var b=name.replace(/[^\w\-]+/g,"_"); return b.length?b:"image"; }
function fwd(p){ return p.replace(/\\/g,"/"); }
function removeIf(path, keep){ try{ if(!keep && File.exists(path)) File.remove(path); }catch(_){} }
function shouldProcessConfig(cfg){
  return !!( cfg.final_stretched || cfg.final_linear || cfg.stars_stretched || cfg.starless_stretched || cfg.starless_linear || cfg.integration_linear || cfg.baseline_linear || cfg.deblur1 || cfg.deblur2 || cfg.denoised );
}
function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) {
        Console.writeln("Debug Save Warning: Invalid window provided for step '", stepName, "'");
        return;
    }

    // Create subfolder for this step
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;

    // Create the step subfolder
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        Console.writeln("Debug Save Warning: Could not create step folder: " + stepFolder);
        // Fallback to main debug directory
        stepFolder = debugDir;
    }

    // Save both XISF and FITS formats
    let baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;

    // Save as XISF (native PixInsight format with full metadata)
    let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
    try {
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
    }

    // Save as FITS (for external inspection)
    let fitsPath = stepFolder + "/" + baseFileName + ".fit";
    try {
        win.saveAs(fitsPath, false, false, false, true);
        Console.writeln("DEBUG: Saved FITS: " + fitsPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save FITS: " + e.message);
    }

    debugStepCounter++;
}

// -------------------- Interactive Review Functions --------------------
function showStretchedPreview(win) {
    if (!INTERACTIVE_MODE) return true;

    Console.writeln("📺 Applying auto-stretch for visual review...");

    try {
        win.show();
        win.bringToFront();
        win.zoomToFit();

        var view = win.mainView;

        // Calculate proper auto-stretch parameters
        var median = view.computeOrFetchProperty("Median");
        var mad = view.computeOrFetchProperty("MAD");

        var stf = new ScreenTransferFunction;
        var n = view.image.isColor ? 3 : 1;

        // Build STF parameters array - PixInsight expects 4 channels always
        var stfParams = [
            [0, 1, 0.5, 0, 1], // Channel 0 (or R)
            [0, 1, 0.5, 0, 1], // Channel 1 (or G)
            [0, 1, 0.5, 0, 1], // Channel 2 (or B)
            [0, 1, 0.5, 0, 1]  // Channel 3 (Alpha/RGB combined)
        ];

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826; // Convert MAD to sigma

            // Auto-stretch calculation
            var c0 = Math.max(0, med - 2.8 * madVal);  // Shadow clipping
            var c1 = 1.0;  // Highlight clipping
            var m = Math.mtf(0.25, med - c0);  // Midtones balance

            stfParams[c] = [c0, c1, m, 0, 1];
        }

        // Apply the STF
        stf.STF = stfParams;
        stf.executeOn(view);

        // Force window refresh
        win.forceClose = false;
        win.show();
        win.bringToFront();

        Console.writeln("✅ Auto-stretch applied for review");
        return true;
    } catch(e) {
        Console.writeln("❌ Auto-stretch failed: " + e.message);
        return false;
    }
}

function resetStretch(win) {
    if (!INTERACTIVE_MODE) return true;

    try {
        // Try multiple methods to reset STF
        if (typeof win.disableScreenTransferFunctions === 'function') {
            win.disableScreenTransferFunctions();
        } else {
            // Fallback: apply identity STF
            var stf = new ScreenTransferFunction;
            var identitySTF = [
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1],
                [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1]
            ];
            stf.STF = identitySTF;
            stf.executeOn(win.mainView);
        }
        Console.writeln("✅ Reset to linear view");
        return true;
    } catch(e) {
        Console.writeln("⚠️ Reset failed: " + e.message + " (continuing anyway)");
        return true; // Don't fail the whole process for this
    }
}

function askAcceptStep(stepName, description, stepNumber, stepId) {
    if (!INTERACTIVE_MODE) return "accept"; // Auto-accept if not interactive

    Console.writeln("\n🔍 REVIEW STEP " + stepNumber + ": " + stepName);

    var result = (new MessageBox(
        "Step " + stepNumber + ": " + stepName + "\n\n" +
        (description || "Please review the stretched image on screen.") + "\n\n" +
        "The image shows the result with auto-stretch applied.\n\n" +
        "Choose your action:\n\n" +
        "YES = Accept and keep this step\n" +
        "NO = Skip this step (revert to previous step)\n" +
        "CANCEL = Stop processing entirely",
        "Step " + stepNumber + " - Accept, Skip, or Stop?",
        StdIcon_Question,
        StdButton_Yes, StdButton_No, StdButton_Cancel
    )).execute();

    if (result == StdButton_Yes) {
        Console.writeln("✅ ACCEPTED Step " + stepNumber + ": " + stepName);
        return "accept";
    } else if (result == StdButton_No) {
        Console.writeln("⏭️ SKIPPED Step " + stepNumber + ": " + stepName + " - reverting to previous step");
        return "skip";
    } else {
        Console.writeln("🛑 STOPPED: Processing aborted by user at Step " + stepNumber + ": " + stepName);
        USER_ABORTED = true;
        throw new Error("ABORT_PROCESSING"); // Force immediate stop
    }
}

// Global variables for interactive control
var USER_ABORTED = false;
var CURRENT_STEP_NUMBER = 0;

// Simple reversion using debug subfolders - FIXED VERSION
function revertToPreviousStep(win, currentStepNumber, baseTag, debugDir) {
    Console.writeln("🔄 REVERT: Loading from previous debug step");

    // Dynamically list all step folders
    var stepFolders = [];
    var ff = new FileFind(debugDir + "/*");
    if (ff.begin()) {
        do {
            if (ff.isDirectory && ff.name.match(/^\d{2}_/)) {
                stepFolders.push(ff.name);
            }
        } while (ff.next());
    }

    if (stepFolders.length < 2) {
        Console.writeln("⚠️ Cannot revert - insufficient debug folders");
        return null;
    }

    // Sort folders numerically (e.g., "01_", "02_")
    stepFolders.sort(function(a, b) {
        return parseInt(a.split("_")[0]) - parseInt(b.split("_")[0]);
    });

    // Previous is second-last (last is current bad step)
    var previousFolderName = stepFolders[stepFolders.length - 2];
    var previousFolder = debugDir + "/" + previousFolderName;

    // Find .xisf in previous folder
    var revertFile = null;
    var ffFiles = new FileFind(previousFolder + "/*.xisf");
    if (ffFiles.begin()) {
        do {
            if (!ffFiles.isDirectory) {
                revertFile = previousFolder + "/" + ffFiles.name;
                break; // Assume one .xisf per folder
            }
        } while (ffFiles.next());
    }

    if (!revertFile || !File.exists(revertFile)) {
        Console.writeln("❌ REVERT FAILED: No .xisf in " + previousFolder);
        return null;
    }

    try {
        Console.writeln("🔄 Loading " + revertFile);
        var currentId = win.mainView.id;
        win.forceClose();

        var windows = ImageWindow.open(revertFile);
        if (windows.length > 0) {
            var restoredWin = windows[0];
            restoredWin.mainView.id = currentId;
            restoredWin.show();
            restoredWin.bringToFront();
            Console.writeln("✅ REVERT SUCCESS: Loaded from " + previousFolderName);
            return restoredWin;
        } else {
            Console.writeln("❌ Failed to open " + revertFile);
            return null;
        }
    } catch (e) {
        Console.writeln("❌ REVERT ERROR: " + e.message);
        return null;
    }
}
function startConsoleLog() {
    let path = File.systemTempDirectory + "/pixinsight_console_log_" + Date.now() + ".txt";
    let P = new ProcessInstance("Console");
    P.arg = "-r=" + path;
    P.executeGlobal();
    return path;
}
function stopConsoleLog() {
    let P = new ProcessInstance("Console");
    P.arg = "-r=";
    P.executeGlobal();
}
function checkLogForFailure(filePath, searchText) {
    if (!filePath || !File.exists(filePath)) return false;
    var failureFound = false;
    try {
      var logFile = new File;
      logFile.openForReading(filePath);
      if (logFile.isOpen) {
        var s = logFile.read(DataType_ByteArray);
        logFile.close();
        var logContent = s.toString();
        if (logContent.indexOf(searchText) !== -1) failureFound = true;
      }
    } catch(e) {
      Console.writeln("Warning: Could not read console log file: " + e.message);
    } finally {
      try { File.remove(filePath); } catch(_) {}
    }
    return failureFound;
}
function wait(ms) {
    var start = Date.now();
    var end = start + ms;
    while (Date.now() < end) {
        processEvents(); // Keep PixInsight responsive
    }
}
function saveAs16BitTiff(win, path) {
  var tifPath = File.changeExtension(path, ".tif");
  win.saveAs(tifPath, false, false, true, false);
  Console.writeln("  Saved 16-bit TIFF: ", File.extractName(tifPath));
}
function findAllInputImages(dir){
  if(!File.directoryExists(dir)) throw new Error("Input directory does not exist: "+dir);
  var v=[];
  var ff=new FileFind;
  var supportedExtensions = [".xisf", ".fit", ".fits", ".tif", ".tiff"];
  if(ff.begin(dir+"/*.*")){
    do {
      var nameLower = ff.name.toLowerCase();
      for (var i = 0; i < supportedExtensions.length; ++i) {
        if (nameLower.endsWith(supportedExtensions[i])) {
          v.push(dir+"/"+ff.name);
          break;
        }
      }
    } while(ff.next());
  }
  return v;
}
function detectFilterFromName(fileName) {
  var s = fileName.toLowerCase();
  if (s.indexOf("filter-red")>=0 || /filter-r(?![a-z])/.test(s) || /\bred\b/.test(s)) return "R";
  if (s.indexOf("filter-green")>=0 || /filter-g(?![a-z])/.test(s) || /\bgreen\b/.test(s)) return "G";
  if (s.indexOf("filter-blue")>=0 || /filter-b(?![a-z])/.test(s) || /\bblue\b/.test(s)) return "B";
  if (/luminance|filter-l(?![a-z])|\bl\b/.test(s)) return "L";
  if (/ha|h\-?alpha|h_?a/.test(s)) return "Ha";
  if (/oiii|o3|o\-?iii/.test(s)) return "OIII";
  if (/sii|s2|s\-?ii/.test(s)) return "SII";
  return null;
}
function buildWorkPlan(dir, combineRGB){
  var files = findAllInputImages(dir);
  var byFilter = {}, unknownSingles = [];
  for (var i=0;i<files.length;++i){
    var name = File.extractName(files[i]);
    var f = detectFilterFromName(name);
    if (f){ if (!byFilter[f]) byFilter[f]=[]; byFilter[f].push(files[i]); }
    else { unknownSingles.push(files[i]); }
  }
  function pickFirst(arr){ if(!arr||!arr.length) return null; arr.sort(); return arr[0]; }
  var haveR=!!(byFilter.R&&byFilter.R.length), haveG=!!(byFilter.G&&byFilter.G.length), haveB=!!(byFilter.B&&byFilter.B.length);
  var plan={ doRGB:false, r:null, g:null, b:null, singles:[] };
  if (combineRGB && haveR && haveG && haveB){
    plan.doRGB=true; plan.r=pickFirst(byFilter.R); plan.g=pickFirst(byFilter.G); plan.b=pickFirst(byFilter.B);
  }
  function pushSingles(k){
    if(byFilter[k]) for(var i=0;i<byFilter[k].length;++i){
      var p=byFilter[k][i];
      if (plan.doRGB && ((k==="R"&&p===plan.r)||(k==="G"&&p===plan.g)||(k==="B"&&p===plan.b))) continue;
      plan.singles.push({ path:p, tag:k, isStackedRGB: false });
    }
  }
  ["L","Ha","OIII","SII"].forEach(pushSingles);
  if(!plan.doRGB) ["R","G","B"].forEach(pushSingles);
  for (var k=0;k<unknownSingles.length;++k) {
    var filePath = unknownSingles[k];
    var isColorStack = false;
    var tempWinArr = ImageWindow.open(filePath);
    if (tempWinArr.length > 0) {
        var tempWin = tempWinArr[0];
        if (tempWin.mainView.image.isColor) isColorStack = true;
        tempWin.forceClose();
    }
    plan.singles.push({ path: filePath, tag: isColorStack ? "Color" : "Single", isStackedRGB: isColorStack });
  }
  return plan;
}
function combineRGB(redPath, greenPath, bluePath, rootOut){
  Console.writeln("\n=== RGB Channel Combination ===");
  var r=ImageWindow.open(redPath), g=ImageWindow.open(greenPath), b=ImageWindow.open(bluePath);
  if(r.length===0||g.length===0||b.length===0) throw new Error("Failed to open one or more RGB masters");
  var CC=new ChannelCombination;
  CC.colorSpace=ChannelCombination.prototype.RGB;
  CC.channels=[[true,r[0].mainView.id],[true,g[0].mainView.id],[true,b[0].mainView.id]];
  CC.executeGlobal();
  var rgb=null, wins=ImageWindow.windows; for(var i=wins.length-1;i>=0;--i){ if(wins[i].mainView.image.isColor){ rgb=wins[i]; break; } }
  if(!rgb) throw new Error("Could not find RGB combined image");
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var outPath=stackDir+"/RGB_Combined"+outputExtension;
  rgb.saveAs(outPath,false,false,false,false);
  try{ r[0].close(); }catch(_){}
  try{ g[0].close(); }catch(_){}
  try{ b[0].close(); }catch(_){}
  return {window:rgb, path:outPath, base:"RGB_Combined"};
}
function finalABE(win, sum){
  try{
    Console.writeln("\n=== Running Automatic Background Extractor (ABE) ===");
    var P = new AutomaticBackgroundExtractor;
    P.tolerance = 1.000; P.deviation = 0.800; P.unbalance = 1.800; P.minBoxFraction = 0.050; P.maxBackground = 1.0000; P.minBackground = 0.0000; P.useBrightnessLimits = false; P.polyDegree = 4; P.boxSize = 5; P.boxSeparation = 5; P.modelImageSampleFormat = AutomaticBackgroundExtractor.prototype.f32; P.abeDownsample = 2.00; P.writeSampleBoxes = false; P.justTrySamples = false;
    P.targetCorrection = AutomaticBackgroundExtractor.prototype.Subtraction; P.normalize = true; P.replaceTarget = true; P.discardModel = true;
    P.correctedImageId = ""; P.correctedImageSampleFormat = AutomaticBackgroundExtractor.prototype.SameAsTarget; P.verboseCoefficients = false; P.compareModel = false; P.compareFactor = 10.00;
    P.executeOn(win.mainView);
    sum.backgroundExtraction={name:"Background Extraction",status:"✅",details:"ABE with custom settings applied"};
  }catch(e){ sum.backgroundExtraction={name:"Background Extraction",status:"⚠️",details:e.message}; }
}
function runGradientCorrection(win, sum, baseTag, debugDir) {
    try {
        Console.writeln("\n=== Running GradientCorrection Tool ===");
        var P = new GradientCorrection;
        P.reference = 0.50; P.lowThreshold = 0.20; P.lowTolerance = 0.50; P.highThreshold = 0.05; P.highTolerance = 0.00; P.iterations = 15; P.scale = 7.60; P.smoothness = 0.71; P.downsamplingFactor = 16; P.protection = true; P.protectionThreshold = 0.10; P.protectionAmount = 0.50; P.protectionSmoothingFactor = 16; P.lowClippingLevel = 0.000076; P.automaticConvergence = true; P.convergenceLimit = 0.00001000; P.maxIterations = 10; P.useSimplification = true; P.simplificationDegree = 1; P.simplificationScale = 1024; P.generateSimpleModel = false; P.generateGradientModel = false; P.generateProtectionMasks = false; P.gridSamplingDelta = 16;
        P.executeOn(win.mainView);
        sum.gradientCorrection = {name: "GradientCorrection", status: "✅", details: "Applied with custom settings"};

        // Debug save after GradientCorrection
        debugSave(win, "After_GradientCorrection", baseTag, debugDir);

        // INTERACTIVE REVIEW - GradientCorrection
        if (INTERACTIVE_MODE) {
            CURRENT_STEP_NUMBER++;

            showStretchedPreview(win);
            processEvents();
            msleep(1000);

            var decision = askAcceptStep("Gradient Correction",
                "Gradient correction has been applied.\n" +
                "Look for:\n" +
                "• Smooth, even background\n" +
                "• No artificial gradients introduced\n" +
                "• Preserved nebulosity and star field",
                CURRENT_STEP_NUMBER, "gradientCorrection");

            resetStretch(win);

            if (decision === "skip") {
                var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    CURRENT_STEP_NUMBER--;
                    sum.gradientCorrection.status = "⏭️";
                    sum.gradientCorrection.details = "Skipped by user - reverted to previous step";
                } else {
                    // Revert failed, disable and restart
                    throw new Error("RESTART_PROCESS:gradientCorrection");
                }
            } else if (decision === "accept") {
                Console.writeln("✅ GradientCorrection step accepted");
            }
        }

        return win; // Return potentially updated window reference
    } catch (e) {
        if (e.message.startsWith("RESTART_PROCESS:")) {
            throw e;
        }
        sum.gradientCorrection = {name: "GradientCorrection", status: "❌", details: e.message};
        // Debug save even on failure
        debugSave(win, "After_GradientCorrection_FAILED", baseTag, debugDir);
        return win;
    }
}
function runGraXpert(win, sum, baseTag, debugDir) {

    try {
        Console.writeln("\n=== Running GraXpert via Library Call ===");
        let gxp = new GraXpertLib;
        gxp.graxpertParameters.correction = 0;
        gxp.graxpertParameters.smoothing = 0.964;
        gxp.graxpertParameters.replaceTarget = true;
        gxp.graxpertParameters.showBackground = false;
        gxp.graxpertParameters.targetView = win.mainView;
        gxp.process();
        sum.graxpert = {name: "GraXpert", status: "✅", details: "Applied via library call"};

        // Debug save after GraXpert
        debugSave(win, "After_GraXpert", baseTag, debugDir);

        // INTERACTIVE REVIEW - GraXpert
        if (INTERACTIVE_MODE) {
            CURRENT_STEP_NUMBER++;

            showStretchedPreview(win);
            processEvents();
            msleep(1000);

            var decision = askAcceptStep("GraXpert Background Extraction",
                "GraXpert has removed the background gradient.\n" +
                "Look for:\n" +
                "• Even background across the image\n" +
                "• No over-subtraction of nebulosity\n" +
                "• Preserved star colors and brightness",
                CURRENT_STEP_NUMBER, "graXpert");

            resetStretch(win);

            if (decision === "skip") {
                // Revert using debug subfolders
                var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
                if (restoredWin) {
                    win = restoredWin;
                    CURRENT_STEP_NUMBER--; // Step back
                    sum.graxpert.status = "⏭️";
                    sum.graxpert.details = "Skipped by user - reverted to previous step";
                } else {
                    // Revert failed, disable and restart
                    throw new Error("RESTART_PROCESS:graXpert");
                }
            } else if (decision === "accept") {
                Console.writeln("✅ GraXpert step accepted");
            }
            // If decision === "abort", exception is thrown by askAcceptStep
        }

        return win; // Return potentially updated window reference
    } catch (e) {
        if (e.message.startsWith("RESTART_PROCESS:")) {
            throw e;
        }
        sum.graxpert = {name: "GraXpert", status: "❌", details: e.message};
        // Debug save even on failure
        debugSave(win, "After_GraXpert_FAILED", baseTag, debugDir);
        return win;
    }
}

// -------------------- ImageSolver + SPCC --------------------
function solveImage(win, sum){
  try{
    Console.writeln("\n=== ImageSolver (for SPCC) ===");
    var solver = new ImageSolver();
    solver.Init(win, false);
    solver.useMetadata = true;
    solver.catalog = "GAIA DR3";
    solver.useDistortionCorrection = false;
    solver.generateErrorMaps = false;
    solver.showStars = false;
    solver.showDistortion = false;
    solver.generateDistortionMaps = false;
    solver.sensitivity = 0.1;

    if (!solver.SolveImage(win)){
      Console.writeln("  ⚠️ Metadata-based solving failed, trying blind...");
      solver.useMetadata = false;
      if (!solver.SolveImage(win)) throw new Error("Plate solution not found.");
    }
    sum.solver={name:"ImageSolver",status:"✅",details:"Solved (GAIA DR3)"};

    // Debug save after ImageSolver
    debugSave(win, "After_ImageSolver", baseTag, debugDir);
    return true;
  }catch(e){
    sum.solver={name:"ImageSolver",status:"❌",details:e.message};
    return false;
  }
}

// profileSelector: "OSC" or "RGB" (curves/filters as provided)
function performSPCC(win, sum, profileSelector, showGraphs){
  try{
    Console.writeln("\n=== SPCC (using fresh WCS) — profile: "+profileSelector+" ===");
    var P=new SpectrophotometricColorCalibration();

    // Your requested settings
    try{ P.whiteReferenceId = "Average Spiral Galaxy"; }catch(_){ try{ P.whiteReferenceId="AVG_G2V"; }catch(_){ } }
    try{ P.qeCurve = "Sony IMX411/455/461/533/571"; }catch(_){}
    try{
      if(profileSelector==="RGB"){
        P.redFilter   = "Astronomik Typ 2c R";
        P.greenFilter = "Astronomik Typ 2c G";
        P.blueFilter  = "Astronomik Typ 2c B";
      }else{
        P.redFilter   = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter  = "Sony Color Sensor B";
      }
    }catch(_){}
    try{ P.narrowbandMode=false; }catch(_){}
    try{ P.generateGraphs=showGraphs||false; }catch(_){}  // Only show graphs if enabled
    try{ P.generateStarMaps=false; }catch(_){}
    try{ P.generateTextFiles=false; }catch(_){}
    try{ P.applyCalibration=true; }catch(_){}
    try{ P.catalog="Gaia DR3/SP"; }catch(_){}
    try{ P.automaticLimitMagnitude=true; }catch(_){}
    try{ P.backgroundNeutralization = true; P.lowerLimit = -2.80; P.upperLimit = +2.00; }catch(_){}
    try{ P.structureLayers=5; P.saturationThreshold=0.99; P.backgroundReferenceViewId=""; P.limitMagnitude=16.0; }catch(_){}

    P.executeOn(win.mainView);
    sum.spcc={name:"SPCC",status:"✅",details:"Applied"};

    // Debug save after SPCC
    debugSave(win, "After_SPCC", baseTag, debugDir);
    return true;

  }catch(e){ sum.spcc={name:"SPCC",status:"❌",details:e.message}; return false; }
}

function autoBlackPoint(win, sum, baseTag, debugDir, stepId){
  try{
    var img = win.mainView.image;
    if (!img.readSamples) {
      var AH_fallback = new AutoHistogram();
      AH_fallback.auto=true;
      AH_fallback.clipLow=0.1;
      AH_fallback.clipHigh=0.1;
      AH_fallback.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};

      // Debug save after Black Point correction
      debugSave(win, "After_BlackPoint_Fallback", baseTag, debugDir);

      // INTERACTIVE REVIEW - Black Point
      if (INTERACTIVE_MODE) {
          CURRENT_STEP_NUMBER++;

          showStretchedPreview(win);
          processEvents();
          msleep(1000);

          var decision = askAcceptStep("Black Point Correction",
              "Black point has been adjusted.\n" +
              "Look for:\n" +
              "• Proper black levels (not too dark/bright)\n" +
              "• Good contrast without clipping\n" +
              "• Natural shadow detail",
              CURRENT_STEP_NUMBER, stepId || "blackPoint");

          resetStretch(win);

          if (decision === "skip") {
              var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
              if (restoredWin) {
                  win = restoredWin;
                  CURRENT_STEP_NUMBER--;
                  sum.blackPoint.status = "⏭️";
                  sum.blackPoint.details = "Skipped by user - reverted to previous step";
              } else {
                  // Revert failed, disable and restart
                  throw new Error("RESTART_PROCESS:blackPoint");
              }
          } else if (decision === "accept") {
              Console.writeln("✅ Black Point step accepted");
          }
      }

      return true;
    }
    if(!img.isColor || img.numberOfChannels<3){
      var AH=new AutoHistogram();
      AH.auto=true;
      AH.clipLow=0.1;
      AH.clipHigh=0.1;
      AH.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"AutoHistogram (mono/NB)"};

      // Debug save after Black Point correction (mono)
      debugSave(win, "After_BlackPoint_Mono", baseTag, debugDir);

      // INTERACTIVE REVIEW - Black Point
      if (INTERACTIVE_MODE) {
          CURRENT_STEP_NUMBER++;

          showStretchedPreview(win);
          processEvents();
          msleep(1000);

          var decision = askAcceptStep("Black Point Correction",
              "Black point has been adjusted.\n" +
              "Look for:\n" +
              "• Proper black levels (not too dark/bright)\n" +
              "• Good contrast without clipping\n" +
              "• Natural shadow detail",
              CURRENT_STEP_NUMBER, stepId || "blackPoint");

          resetStretch(win);

          if (decision === "skip") {
              var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
              if (restoredWin) {
                  win = restoredWin;
                  CURRENT_STEP_NUMBER--;
                  sum.blackPoint.status = "⏭️";
                  sum.blackPoint.details = "Skipped by user - reverted to previous step";
              } else {
                  // Revert failed, disable and restart
                  throw new Error("RESTART_PROCESS:blackPoint");
              }
          } else if (decision === "accept") {
              Console.writeln("✅ Black Point step accepted");
          }
      }

      return true;
    }
    var w=img.width,h=img.height, sampleSize=20, numSamples=20;
    var rs=[],gs=[],bs=[];
    for(var i=0;i<numSamples;++i){
      var x=Math.floor(Math.random()*(w-sampleSize)), y=Math.floor(Math.random()*(h-sampleSize));
      var rect=new Rect(x,y,x+sampleSize,y+sampleSize), s=img.readSamples(rect), cnt=sampleSize*sampleSize, r=0,g=0,b=0;
      for(var p=0;p<cnt;++p){ r+=s[p*3]; g+=s[p*3+1]; b+=s[p*3+2]; }
      rs.push(r/cnt); gs.push(g/cnt); bs.push(b/cnt);
    }
    rs.sort(function(a,b){return a-b;}); gs.sort(function(a,b){return a-b;}); bs.sort(function(a,b){return a-b;});
    var n=Math.max(1,Math.floor(numSamples*0.25)), R=0,G=0,B=0;
    for(var m=0;m<n;++m){ R+=rs[m]; G+=gs[m]; B+=bs[m]; }
    R/=n; G/=n; B/=n;
    try{
      var L=new Levels();
      L.redBlack=Math.min(R*0.8,0.02);
      L.greenBlack=Math.min(G*0.9,0.03);
      L.blueBlack=Math.min(B*0.8,0.02);
      L.redWhite=0.98;
      L.greenWhite=0.97;
      L.blueWhite=0.98;
      L.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Levels"};
    }catch(e2){
      var AH2=new AutoHistogram();
      AH2.auto=true;
      AH2.clipLow=0.1;
      AH2.clipHigh=0.1;
      AH2.executeOn(win.mainView);
      sum.blackPoint={name:"Black Point",status:"✅",details:"Fallback AutoHistogram"};

      // Debug save after Black Point correction (color)
      debugSave(win, "After_BlackPoint_Color", baseTag, debugDir);
    }

    // INTERACTIVE REVIEW - Black Point (for color)
    if (INTERACTIVE_MODE && img.isColor) {
        CURRENT_STEP_NUMBER++;

        showStretchedPreview(win);
        processEvents();
        msleep(1000);

        var decision = askAcceptStep("Black Point Correction",
            "Black point has been adjusted.\n" +
            "Look for:\n" +
            "• Proper black levels (not too dark/bright)\n" +
            "• Good contrast without clipping\n" +
            "• Natural shadow detail",
            CURRENT_STEP_NUMBER, stepId || "blackPoint");

        resetStretch(win);

        if (decision === "skip") {
            var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
            if (restoredWin) {
                win = restoredWin;
                CURRENT_STEP_NUMBER--;
                sum.blackPoint.status = "⏭️";
                sum.blackPoint.details = "Skipped by user - reverted to previous step";
            } else {
                // Revert failed, disable and restart
                throw new Error("RESTART_PROCESS:blackPoint");
            }
        } else if (decision === "accept") {
            Console.writeln("✅ Black Point step accepted");
        }
    }

    return true;
  }catch(e){
    if (e.message.startsWith("RESTART_PROCESS:")) {
        throw e;
    }
    sum.blackPoint={name:"Black Point",status:"❌",details:e.message};
    return false;
  }
}

// -------------------- AI steps --------------------
function deblur1(win, sum, baseTag, debugDir){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.00; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.00;
    P.autoPSF=true; P.correctOnly=true; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV1={name:"Deblur V1",status:"✅",details:"Round stars"};

    // Debug save after Deblur V1
    debugSave(win, "After_DeblurV1_RoundStars", baseTag, debugDir);

    // INTERACTIVE REVIEW - Deblur V1
    if (INTERACTIVE_MODE) {
        CURRENT_STEP_NUMBER++;
        showStretchedPreview(win);
        processEvents();
        msleep(1000);

        var decision = askAcceptStep("BlurX Round Stars",
            "BlurXTerminator has corrected round stars.\n" +
            "Look for:\n" +
            "• Rounder, tighter star shapes\n" +
            "• No over-processing artifacts\n" +
            "• Preserved star colors and brightness\n" +
            "• Natural star appearance",
            CURRENT_STEP_NUMBER, "deblur1");

        resetStretch(win);

        if (decision === "skip") {
            var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
            if (restoredWin) {
                win = restoredWin;
                CURRENT_STEP_NUMBER--;
                sum.deblurV1.status = "⏭️";
                sum.deblurV1.details = "Skipped by user - reverted to previous step";
            } else {
                // Revert failed, disable and restart
                throw new Error("RESTART_PROCESS:deblur1");
            }
        } else if (decision === "accept") {
            Console.writeln("✅ Deblur V1 step accepted");
        }
    }

    return true;
  }catch(e){
    if (e.message.startsWith("RESTART_PROCESS:")) {
        throw e;
    }
    sum.deblurV1={name:"Deblur V1",status:"❌",details:e.message};
    // Debug save even on failure
    debugSave(win, "After_DeblurV1_RoundStars_FAILED", baseTag, debugDir);
    return false;
  }
}

function deblur2(win, sum, baseTag, debugDir){
  try{
    var P=new BlurXTerminator();
    P.sharpenStars=0.25; P.adjustStarHalos=0.00; P.psfDiameter=0.00; P.sharpenNonstellar=0.90;
    P.autoPSF=true; P.correctOnly=false; P.correctFirst=false; P.nonstellarThenStellar=false; P.luminanceOnly=false;
    P.executeOn(win.mainView);
    sum.deblurV2={name:"Deblur V2",status:"✅",details:"Enhance"};

    // Debug save after Deblur V2
    debugSave(win, "After_DeblurV2_Enhance", baseTag, debugDir);

    // INTERACTIVE REVIEW - Deblur V2
    if (INTERACTIVE_MODE) {
        CURRENT_STEP_NUMBER++;
        showStretchedPreview(win);
        processEvents();
        msleep(1000);

        var decision = askAcceptStep("BlurX Enhanced",
            "BlurXTerminator enhanced processing has been applied.\n" +
            "Look for:\n" +
            "• Improved star sharpness and detail\n" +
            "• Enhanced nebula structure\n" +
            "• No over-sharpening artifacts\n" +
            "• Natural image appearance",
            CURRENT_STEP_NUMBER, "deblur2");

        resetStretch(win);

        if (decision === "skip") {
            var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
            if (restoredWin) {
                win = restoredWin;
                CURRENT_STEP_NUMBER--;
                sum.deblurV2.status = "⏭️";
                sum.deblurV2.details = "Skipped by user - reverted to previous step";
            } else {
                // Revert failed, disable and restart
                throw new Error("RESTART_PROCESS:deblur2");
            }
        } else if (decision === "accept") {
            Console.writeln("✅ Deblur V2 step accepted");
        }
    }

    return true;
  }catch(e){
    if (e.message.startsWith("RESTART_PROCESS:")) {
        throw e;
    }
    sum.deblurV2={name:"Deblur V2",status:"❌",details:e.message};
    // Debug save even on failure
    debugSave(win, "After_DeblurV2_Enhance_FAILED", baseTag, debugDir);
    return false;
  }
}

function denoise(win, sum, baseTag, debugDir){
  try{
    var P=new NoiseXTerminator();
    P.intensityColorSeparation=false; P.frequencySeparation=false; P.denoise=0.90; P.iterations=2;
    P.executeOn(win.mainView);
    sum.denoising={name:"Denoising",status:"✅",details:"Applied"};

    // Debug save after Denoising
    debugSave(win, "After_Denoising", baseTag, debugDir);

    // INTERACTIVE REVIEW - Denoising
    if (INTERACTIVE_MODE) {
        CURRENT_STEP_NUMBER++;
        win.show();
        win.bringToFront();
        win.zoomToFit();
        processEvents();
        msleep(1000);

        var decision = askAcceptStep("NoiseX Denoising",
            "NoiseXTerminator has reduced image noise.\n" +
            "Look for:\n" +
            "• Reduced noise in background areas\n" +
            "• Preserved fine details and structure\n" +
            "• No over-smoothing of nebulosity\n" +
            "• Natural texture appearance",
            CURRENT_STEP_NUMBER, "denoising");

        if (decision === "skip") {
            var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
            if (restoredWin) {
                win = restoredWin;
                CURRENT_STEP_NUMBER--;
                sum.denoising.status = "⏭️";
                sum.denoising.details = "Skipped by user - reverted to previous step";
            } else {
                // Revert failed, disable and restart
                throw new Error("RESTART_PROCESS:denoising");
            }
        } else if (decision === "accept") {
            Console.writeln("✅ Denoising step accepted");
        }
    }

    return true;
  }catch(e){
    if (e.message.startsWith("RESTART_PROCESS:")) {
        throw e;
    }
    sum.denoising={name:"Denoising",status:"❌",details:e.message};
    // Debug save even on failure
    debugSave(win, "After_Denoising_FAILED", baseTag, debugDir);
    return false;
  }
}

// -------------------- Perfect Nuclear Stretch Function --------------------
/*
 * STF Auto Stretch routine - WORKING VERSION FROM TEST
 */
function STFAutoStretch( view, shadowsClipping, targetBackground, rgbLinked )
{
   if (shadowsClipping === undefined) shadowsClipping = -2.80;
   if (targetBackground === undefined) targetBackground = 0.25;
   if (rgbLinked === undefined) rgbLinked = true;

   var stf = new ScreenTransferFunction;
   var n = view.image.isColor ? 3 : 1;

   // 1. Calculate Statistics - CRITICAL FIX: Create copies to prevent corruption
   var median = new Vector( view.computeOrFetchProperty( "Median" ) );
   var mad = new Vector( view.computeOrFetchProperty( "MAD" ) );

   // 2. Normalize MAD (1.4826 * MAD ≈ sigma) - Safe on local copy
   mad.mul( 1.4826 );

   // 3. Calculate Stretch Parameters - STF Parameter Order: [c0, c1, m, r0, r1]
   if ( rgbLinked && n > 1)
   {
      // Linked RGB channels
      var invertedChannels = 0;
      for ( var c = 0; c < n; ++c )
         if ( median.at( c ) > 0.5 )
            ++invertedChannels;

      if ( invertedChannels < n )
      {
         // Noninverted image
         var c0_sum = 0, median_sum = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c0_sum += median.at( c ) + shadowsClipping * mad.at( c );
            median_sum += median.at( c );
         }
         var c0 = Math.range( c0_sum/n, 0.0, 1.0 );
         var m = Math.mtf( targetBackground, median_sum/n - c0 );

         stf.STF = [
                     [c0, 1, m, 0, 1], // R
                     [c0, 1, m, 0, 1], // G
                     [c0, 1, m, 0, 1], // B
                     [0, 1, 0.5, 0, 1] // Alpha
                   ];
      }
      else
      {
         // Inverted image logic
         var c1_sum = 0, median_sum = 0;
         for ( var c = 0; c < n; ++c )
         {
            if ( 1 + mad.at( c ) != 1 )
               c1_sum += median.at( c ) - shadowsClipping * mad.at( c );
            median_sum += median.at( c );
         }
         var c1 = Math.range( c1_sum/n, 0.0, 1.0 );
         var m = Math.mtf( c1 - median_sum/n, targetBackground );

         stf.STF = [
                     [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, c1, m, 0, 1], [0, 1, 0.5, 0, 1]
                   ];
      }
   }
   else
   {
      // Unlinked channels (or Grayscale image)
      var A = [ [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1], [0, 1, 0.5, 0, 1] ];

      for ( var c = 0; c < n; ++c )
      {
         if ( median.at( c ) < 0.5 )
         {
            var c0 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) + shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 0.0;
            var m = Math.mtf( targetBackground, median.at( c ) - c0 );
            A[c] = [c0, 1, m, 0, 1];
         }
         else
         {
            var c1 = (1 + mad.at( c ) != 1) ?
                     Math.range( median.at( c ) - shadowsClipping * mad.at( c ), 0.0, 1.0 ) : 1.0;
            var m = Math.mtf( c1 - median.at( c ), targetBackground );
            A[c] = [0, c1, m, 0, 1];
         }
      }
      stf.STF = A;
   }

   return stf;
}

/*
 * Apply HistogramTransformation - WORKING VERSION WITH CORRECT MAPPING
 */
function ApplyHistogramTransformation( view, stf )
{
   var ht = new HistogramTransformation;

   // HT Parameter Order: [c0, m, c1, r0, r1] <-- CRITICAL DIFFERENCE FROM STF
   var HT_IDENTITY = [0, 0.5, 1, 0, 1];
   var H = [
            HT_IDENTITY.slice(), HT_IDENTITY.slice(), HT_IDENTITY.slice(),
            HT_IDENTITY.slice(), HT_IDENTITY.slice()
           ];

   var n = view.image.isColor ? 3 : 1;

   // Helper function to map STF array to HT array
   // STF input indices: [0=c0, 1=c1, 2=m, 3=r0, 4=r1]
   // HT output indices: [0=c0, 1=m, 2=c1, 3=r0, 4=r1]
   function mapSTFtoHT(stf_channel) {
       return [
           stf_channel[0], // c0
           stf_channel[2], // m (CRITICAL: Swapped index 1 and 2)
           stf_channel[1], // c1 (CRITICAL: Swapped index 1 and 2)
           stf_channel[3], // r0
           stf_channel[4]  // r1
       ];
   }

   if ( view.image.isColor )
   {
       // Check if the STF channels are linked
       var linked = true;
       for(var c = 1; c < n; ++c) {
           if(stf.STF[c][0] != stf.STF[0][0] || stf.STF[c][1] != stf.STF[0][1] || stf.STF[c][2] != stf.STF[0][2]) {
               linked = false;
               break;
           }
       }

       if(linked) {
           // Apply linked transform to the combined RGB/K channel (Index 3)
           H[3] = mapSTFtoHT(stf.STF[0]);
       } else {
           // Apply unlinked transforms to individual channels (Indices 0, 1, 2)
           for ( var c = 0; c < n; ++c )
           {
              H[c] = mapSTFtoHT(stf.STF[c]);
           }
       }
   }
   else
   {
       // Monochrome image (Apply to Index 0)
       H[0] = mapSTFtoHT(stf.STF[0]);
   }

   ht.H = H;
   return ht.executeOn( view );
}

function applyPerfectNuclearStretch(view, sum, baseTag, debugDir) {
   if (!view || !view.image || view.image.isNull)
       throw new Error("No active view for nuclear stretch.");

   // Calculate STF Auto Stretch parameters with nuclear settings (linked RGB for color preservation)
   var stf = STFAutoStretch( view, -2.80, 0.25, true );

   // Apply the calculated parameters using HistogramTransformation with correct mapping
   ApplyHistogramTransformation( view, stf );

   // Debug save after Nuclear Stretch
   debugSave(view.window, "After_Nuclear_Stretch", baseTag, debugDir);

   // INTERACTIVE REVIEW - Nuclear Stretch
   if (INTERACTIVE_MODE) {
       CURRENT_STEP_NUMBER++;
       // For nuclear stretch, we show the actual stretched result (no additional STF needed)
       view.window.show();
       view.window.bringToFront();
       view.window.zoomToFit();
       processEvents();
       msleep(1000);

       var decision = askAcceptStep("Nuclear Stretch",
           "Nuclear stretch has been applied to bring out details.\n" +
           "Look for:\n" +
           "• Good overall brightness and contrast\n" +
           "• Visible nebula details without over-stretching\n" +
           "• Natural color balance\n" +
           "• No excessive noise amplification",
           CURRENT_STEP_NUMBER, "stretch");

       if (decision === "skip") {
           var restoredWin = revertToPreviousStep(view.window, CURRENT_STEP_NUMBER, baseTag, debugDir);
           if (restoredWin) {
               view = restoredWin.mainView;
               CURRENT_STEP_NUMBER--;
               sum.stretch.status = "⏭️";
               sum.stretch.details = "Skipped by user - reverted to previous step";
           } else {
               // Revert failed, disable and restart
               throw new Error("RESTART_PROCESS:stretch");
           }
       } else if (decision === "accept") {
           Console.writeln("✅ Nuclear Stretch step accepted");
       }
   }
}

function stretchImageAtStage(inPath, outPath, saveFlag){
  var w = ImageWindow.open(inPath);
  if(!w.length) return null;
  var win=w[0];

  // Apply perfect nuclear stretch
  applyPerfectNuclearStretch(win.mainView);

  if(saveFlag) {
    if(outPath.toLowerCase().endsWith('.xisf')) {
      win.saveAs(outPath, false, false, false, false);
    } else {
      saveAs16BitTiff(win, outPath);
    }
  }
  return win;
}

// -------------------- Color Enhance helpers (RGB Stars stretched only) --------------------
function applyColorEnhanceToView(view){
  // Curves: S channel enhancement from your working code
  var C = new CurvesTransformation;
  C.R = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Rt = CurvesTransformation.prototype.AkimaSubsplines;
  C.G = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Gt = CurvesTransformation.prototype.AkimaSubsplines;
  C.B = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Bt = CurvesTransformation.prototype.AkimaSubsplines;
  C.K = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Kt = CurvesTransformation.prototype.AkimaSubsplines;
  C.A = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.At = CurvesTransformation.prototype.AkimaSubsplines;
  C.L = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Lt = CurvesTransformation.prototype.AkimaSubsplines;
  C.a = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.at = CurvesTransformation.prototype.AkimaSubsplines;
  C.b = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.bt = CurvesTransformation.prototype.AkimaSubsplines;
  C.c = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.ct = CurvesTransformation.prototype.AkimaSubsplines;
  C.H = [[0.00000, 0.00000], [1.00000, 1.00000]];
  C.Ht = CurvesTransformation.prototype.AkimaSubsplines;
  C.S = [[0.00000, 0.00000], [0.14470, 0.33247], [1.00000, 1.00000]];
  C.St = CurvesTransformation.prototype.AkimaSubsplines;
  C.executeOn(view);

  var N = new SCNR;
  N.amount = 0.73;
  N.protectionMethod = SCNR.prototype.AverageNeutral;
  N.colorToRemove = SCNR.prototype.Green;
  N.preserveLightness = true;
  N.executeOn(view);
}

function pixelMathSubtract(targetView,aId,bId,newId){
  var pm=new PixelMath;
  pm.expression=aId+" - "+bId;
  pm.useSingleExpression=true;
  pm.rescale=true;
  pm.rescaleLower=0;
  pm.rescaleUpper=1;
  pm.truncate=false;
  pm.createNewImage=true;
  pm.newImageId=newId;
  pm.newImageWidth=0;
  pm.newImageHeight=0;
  pm.newImageSampleFormat=0;
  pm.executeOn(targetView,false);
  var outWin=ImageWindow.windowById(newId);
  if(!outWin)throw new Error("PixelMath subtraction failed: "+newId);
  var outView=outWin.mainView;
  outView.beginProcess(UndoFlag_NoSwapFile);
  outView.image.truncate(0,1);
  outView.endProcess();
  return outWin;
}

function subtractFilesToPath_StretchedMask(starsPath,starlessPath,outPath){
  var wA=ImageWindow.open(starsPath);
  if(!wA.length)throw new Error("Open failed: "+starsPath);
  var wB=ImageWindow.open(starlessPath);
  if(!wB.length)throw new Error("Open failed: "+starlessPath);
  var A=wA[0],B=wB[0];
  var aimg=A.mainView.image,bimg=B.mainView.image;
  if(aimg.width!==bimg.width||aimg.height!==bimg.height||aimg.numberOfChannels!==bimg.numberOfChannels||aimg.sampleType!==bimg.sampleType||aimg.bitsPerSample!==bimg.bitsPerSample)
    throw new Error("Geometry/sample mismatch between stars and starless.");
  var commonSTF=computeSTF(aimg);
  applyHT(A.mainView,commonSTF);
  applyHT(B.mainView,commonSTF);
  A.mainView.id="StarsObj_A";
  B.mainView.id="Starless_B";
  var out=pixelMathSubtract(A.mainView,"StarsObj_A","Starless_B","StarsMinusStarless");
  saveAs16BitTiff(out,outPath);
  try{out.forceClose();}catch(_){}
  try{A.forceClose();}catch(_){}
  try{B.forceClose();}catch(_){}
}

function starSeparationAndStretchedMask(win, sum, finalDir, baseTag, opts, isRGBCombined, enhanceRGBStarsStretched, debugDir) {
  try{
    var P=new StarXTerminator;
    P.generateStarImage=false;
    P.unscreenStars=false;
    P.largeOverlap=false;
    P.executeOn(win.mainView);
    var pathStarlessL=finalDir+"/Starless_"+baseTag+outputExtension;
    var pathStarlessS=finalDir+"/Starless_Stretched_"+baseTag;
    if(opts.starless_linear)win.saveAs(pathStarlessL,false,false,false,false);
    if(opts.starless_stretched){
      if(!File.exists(pathStarlessL)){
        win.saveAs(pathStarlessL,false,false,false,false);
      }
      var starlessLinearWinArr=ImageWindow.open(pathStarlessL);
      if(starlessLinearWinArr.length>0){
        var wDup=starlessLinearWinArr[0];
        var pathBaselineL=finalDir+"/Final_Stacked_"+baseTag+outputExtension;
        var baselineArr=ImageWindow.open(pathBaselineL);
        var stf1;
        if(baselineArr.length>0){
          var baselineW=baselineArr[0];
          stf1=computeSTF(baselineW.mainView.image);
          baselineW.forceClose();
        }else{
          throw new Error("Failed to open baseline for STF computation.");
        }
        applyHT(wDup.mainView,stf1);
        saveAs16BitTiff(wDup,pathStarlessS);
        wDup.forceClose();
      }else{
        throw new Error("Failed to reopen starless linear image for stretching.");
      }
    }
    if(opts.stars_stretched){
      var pathBaselineL=finalDir+"/Final_Stacked_"+baseTag+outputExtension;
      if(!File.exists(pathStarlessL))win.saveAs(pathStarlessL,false,false,false,false);
      var outStarsS=finalDir+"/Stars_Stretched_"+baseTag;
      subtractFilesToPath_StretchedMask(pathBaselineL,pathStarlessL,outStarsS);
      if(isRGBCombined&&enhanceRGBStarsStretched){
        var wS=ImageWindow.open(File.changeExtension(outStarsS,".tif"));
        if(wS.length){
          applyColorEnhanceToView(wS[0].mainView);
          saveAs16BitTiff(wS[0],outStarsS);
          try{wS[0].forceClose();}catch(_){}
        }
      }
    }

    // Debug save after Star Separation
    debugSave(win, "After_StarSeparation", baseTag, debugDir);

    // INTERACTIVE REVIEW - Star Separation
    if (INTERACTIVE_MODE) {
        CURRENT_STEP_NUMBER++;
        win.show();
        win.bringToFront();
        win.zoomToFit();
        processEvents();
        msleep(1000);

        var decision = askAcceptStep("StarX Separation",
            "StarXTerminator has separated stars from nebulosity.\n" +
            "Look for:\n" +
            "• Clean star removal from nebula areas\n" +
            "• Preserved nebula structure and detail\n" +
            "• No artifacts or halos around star positions\n" +
            "• Natural nebula appearance",
            CURRENT_STEP_NUMBER, "starSeparation");

        if (decision === "skip") {
            var restoredWin = revertToPreviousStep(win, CURRENT_STEP_NUMBER, baseTag, debugDir);
            if (restoredWin) {
                win = restoredWin;
                CURRENT_STEP_NUMBER--;
                sum.starSeparation.status = "⏭️";
                sum.starSeparation.details = "Skipped by user - reverted to previous step";
            } else {
                // Revert failed, disable and restart
                throw new Error("RESTART_PROCESS:starSeparation");
            }
        } else if (decision === "accept") {
            Console.writeln("✅ Star Separation step accepted");
        }
    }

    sum.starSeparation={name:"Star Separation",status:"✅",details:"Starless made with SXT; star mask by stretched subtraction with common STF"};
    return true;
  }catch(e){
    if (e.message.startsWith("RESTART_PROCESS:")) {
        throw e;
    }
    sum.starSeparation={name:"Star Separation",status:"❌",details:e.message};
    return false;
  }
}

// -------------------- Per-Image Pipeline --------------------
function processOne(win, baseTag, rootOut, ai, saveCfg, isRGBCombined, enhanceRGBStarsStretched, isStackedRGB = false, spccGraphs = false){
  var sum={};
  var stackDir=rootOut+"/5_stacked"; ensureDir(stackDir);
  var finalDir=rootOut+"/6_final";   ensureDir(finalDir);
  var debugDir = rootOut + "/debug";
  if (DEBUG_MODE) ensureDir(debugDir);
  debugStepCounter = 1;

  var integrationPath = stackDir+"/Integration_"+baseTag+outputExtension;
  win.saveAs(integrationPath,false,false,false,false);
  sum.finalSave={name:"Integration Save",status:"✅",details:"Integration saved"};

  closeAllWindowsExcept([win.mainView.id]);

  let disableSteps = {
    gradientCorrection: false,
    graXpert: false,
    blackPoint: false,
    deblur1: false,
    deblur2: false,
    stretch: false,
    denoising: false,
    starSeparation: false
  };

  let success = false;
  while (!success) {
    try {
      // Reset step counter and abort flag
      CURRENT_STEP_NUMBER = 0;
      USER_ABORTED = false;
      sum = {}; // Reset summary on restart

      finalABE(win,sum);
      autoBlackPoint(win,sum, baseTag, debugDir, "blackPoint");

      if (!disableSteps.gradientCorrection) {
        win = runGradientCorrection(win, sum, baseTag, debugDir);
      }

      if (!disableSteps.graXpert) {
        win = runGraXpert(win, sum, baseTag, debugDir);
      }

      if (!disableSteps.blackPoint) {
        autoBlackPoint(win,sum, baseTag, debugDir, "blackPoint");
      }

      // Step 1: BlurX Round Stars (if enabled) - LINEAR
      if(ai.deblur1 && !disableSteps.deblur1){
        deblur1(win, sum, baseTag, debugDir);
        if(saveCfg.deblur1) win.saveAs(finalDir+"/RoundStars_DeblurV1_"+baseTag+outputExtension,false,false,false,false);
      }

      // Step 2: SPCC (Color calibration) - LINEAR
      var isColor = win.mainView.image.isColor;
      // Check if this is RGB combined or a mono/single image
      if (isColor && isRGBCombined) {
        if (isStackedRGB) {
            sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped for pre-stacked color image"};
            sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped for pre-stacked color image"};
        } else {
            if (solveImage(win, sum)) {
                // For RGB combined images: use "RGB" profile
                var spccProfile = "RGB";
                performSPCC(win, sum, spccProfile, spccGraphs);
            } else {
                sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
            }
        }
      } else if (isColor && !isRGBCombined) {
        // Single color images (OSC)
        if (solveImage(win, sum)) {
            var spccProfile = "OSC";
            performSPCC(win, sum, spccProfile, spccGraphs);
        } else {
            sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (no WCS)"};
        }
      } else {
        // Mono images always skip ImageSolver and SPCC
        sum.solver = {name:"ImageSolver", status:"⏭️", details:"Skipped (mono/NB)"};
        sum.spcc = {name:"SPCC", status:"⏭️", details:"Skipped (mono/NB)"};
      }

      // Step 3: BlurX Enhanced - LINEAR
      if(ai.deblur2 && !disableSteps.deblur2){
        deblur2(win, sum, baseTag, debugDir);
        if(saveCfg.deblur2) win.saveAs(finalDir+"/Enhance_DeblurV2_"+baseTag+outputExtension,false,false,false,false);
      }

      closeAllWindowsExcept([win.mainView.id]);
      var baseline = finalDir+"/Final_Stacked_"+baseTag+outputExtension;
      win.saveAs(baseline,false,false,false,false);
      sum.finalSave={name:"Baseline Save",status:"✅",details:"Baseline saved"};

      // Step 4: Nuclear Screen Stretch with STF - NOW STRETCHED
      var stretchedPath = finalDir+"/Stretched_"+baseTag+outputExtension;
      if(ai.stretch && !disableSteps.stretch){
        Console.writeln("\n=== Applying Nuclear Stretch with ScreenTransferFunction ===");
        applyPerfectNuclearStretch(win.mainView, sum, baseTag, debugDir);
        win.saveAs(stretchedPath,false,false,false,false);
        Console.writeln("Nuclear STF stretch applied and saved: " + File.extractName(stretchedPath));

        sum.stretch={name:"Nuclear Stretch",status:"✅",details:"Applied with histogram transformation"};
      }else{
        sum.stretch={name:"Nuclear Stretch",status:"⏭️",details:"Disabled"};
      }

      // Save Final (stretched, with stars) TIFF if requested
      if(saveCfg.final_stretched) {
        saveAs16BitTiff(win, finalDir+"/Final_Stretched_"+baseTag);
        Console.writeln("  Saved Final (stretched, with stars): Final_Stretched_" + baseTag + ".tif");
      }

      // Step 5: NoiseX - STRETCHED
      if(ai.denoise && !disableSteps.denoising){
        denoise(win,sum, baseTag, debugDir);
        if(saveCfg.denoised) {
             var denoiseOutPath = finalDir+"/Denoised_"+baseTag+outputExtension;
             win.saveAs(denoiseOutPath, false, false, false, false);
        }
      }

      // Step 6: StarX and save star screen - using working method from Starmask works.js
      if(ai.starless && !disableSteps.starSeparation){
        starSeparationAndStretchedMask(win,sum,finalDir,baseTag,saveCfg,isRGBCombined,enhanceRGBStarsStretched, debugDir);
      }

      if(!saveCfg.baseline_linear) removeIf(baseline, true);
      if(!saveCfg.integration_linear) removeIf(integrationPath, true);
      if(!saveCfg.final_stretched && !saveCfg.denoised) {
          removeIf(File.changeExtension(stretchedPath, ".tif"), true);
      }

      success = true;
    } catch (e) {
      if (e.message.startsWith("RESTART_PROCESS:")) {
        let disabledStep = e.message.split(":")[1];
        disableSteps[disabledStep] = true;
        closeAllWindowsExcept(null);
        let w = ImageWindow.open(integrationPath);
        if (w.length === 0) throw new Error("Cannot re-open integration file for restart");
        win = w[0];
        if (File.directoryExists(debugDir)) {
          File.removeDirectory(debugDir, true);
        }
        ensureDir(debugDir);
        debugStepCounter = 1;
      } else if (e.message === "ABORT_PROCESSING") {
        throw e;
      } else {
        throw e;
      }
    }
  }

  Console.writeln("\n— Summary: "+baseTag+" —");
  var order = ["backgroundExtraction", "gradientCorrection", "graxpert", "blackPoint", "solver", "spcc", "deblurV1", "deblurV2", "stretch", "denoising", "starSeparation", "finalSave"];
  for(var i=0;i<order.length;++i){
    var k=order[i]; if(sum[k]){ var it=sum[k]; Console.writeln("  "+it.status+" "+it.name+": "+it.details); }
  }
}

// -------------------- GUI --------------------
function showGUI(state){
  var dlg=new Dialog;
  dlg.windowTitle="Post-Integration Pipeline (RGB & Mono) - CORRECTED STRETCH";
  dlg.sizer=new VerticalSizer;
  dlg.sizer.margin=10;
  dlg.sizer.spacing=8;

  var head=new Label(dlg);
  head.useRichText=true;
  head.text="<b>Utah Masterclass — Post-Integration Pipeline (CORRECTED STRETCH)</b>";
  head.textAlignment=TextAlign_Center;
  dlg.sizer.add(head);

  var gTop=new GroupBox(dlg);
  gTop.title="General";
  gTop.sizer=new VerticalSizer;
  gTop.sizer.margin=8;
  gTop.sizer.spacing=6;

  var rowIn=new HorizontalSizer;
  rowIn.spacing=6;
  var labelIn=new Label(dlg);
  labelIn.text="Input Directory:";
  labelIn.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var editIn=new Edit(dlg);
  editIn.readOnly=true;
  editIn.minWidth=560;
  editIn.text=state.inputDir;
  var btnIn=new PushButton(dlg);
  btnIn.text="Browse...";
  btnIn.icon=dlg.scaledResource(":/icons/select-file.png");
  btnIn.onClick=function(){
    var d=new GetDirectoryDialog;
    d.caption="Select Input Directory";
    d.initialDirectory=state.inputDir||"";
    if(d.execute()){
      state.inputDir=fwd(d.directory).replace(/\/$/,"");
      editIn.text=state.inputDir;
    }
  };
  rowIn.add(labelIn);
  rowIn.add(editIn,100);
  rowIn.add(btnIn);
  gTop.sizer.add(rowIn);

  var rowOut=new HorizontalSizer;
  rowOut.spacing=6;
  var labelOut=new Label(dlg);
  labelOut.text="Output Directory:";
  labelOut.textAlignment=TextAlign_Right|TextAlign_VertCenter;
  var editOut=new Edit(dlg);
  editOut.readOnly=true;
  editOut.minWidth=560;
  editOut.text=state.outputDir;
  var btnOut=new PushButton(dlg);
  btnOut.text="Browse...";
  btnOut.icon=dlg.scaledResource(":/icons/select-file.png");
  btnOut.onClick=function(){
    var d=new GetDirectoryDialog;
    d.caption="Select Output Base Directory";
    d.initialDirectory=state.outputDir||"";
    if(d.execute()){
      state.outputDir=fwd(d.directory).replace(/\/$/,"");
      editOut.text=state.outputDir;
    }
  };
  rowOut.add(labelOut);
  rowOut.add(editOut,100);
  rowOut.add(btnOut);
  gTop.sizer.add(rowOut);
  dlg.sizer.add(gTop);

  var gAI=new GroupBox(dlg);
  gAI.title="AI / Processing Steps";
  gAI.sizer=new VerticalSizer;
  gAI.sizer.margin=8;
  gAI.sizer.spacing=6;

  var rowAI1=new HorizontalSizer;
  rowAI1.spacing=12;
  var cbD1=new CheckBox(dlg);
  cbD1.text="✨ Deblur V1 (round stars)";
  cbD1.checked=state.ai.deblur1;
  var cbD2=new CheckBox(dlg);
  cbD2.text="✨ Deblur V2 (enhance)";
  cbD2.checked=state.ai.deblur2;
  var cbST=new CheckBox(dlg);
  cbST.text="🌀 Stretch (AutoSTF→HT)";
  cbST.checked=state.ai.stretch;
  var cbDN=new CheckBox(dlg);
  cbDN.text="🧼 Denoise";
  cbDN.checked=state.ai.denoise;
  var cbSL=new CheckBox(dlg);
  cbSL.text="✂️ Enable StarX (for starless/stars)";
  cbSL.checked=state.ai.starless;
  rowAI1.add(cbD1);
  rowAI1.add(cbD2);
  rowAI1.add(cbST);
  rowAI1.add(cbDN);
  rowAI1.add(cbSL);
  rowAI1.addStretch();
  gAI.sizer.add(rowAI1);

  var rowAI2=new HorizontalSizer;
  rowAI2.spacing=12;
  var cbSPCCGraph=new CheckBox(dlg);
  cbSPCCGraph.text="📊 Show SPCC Graphs (Advanced)";
  cbSPCCGraph.checked=state.spccGraphs;
  rowAI2.add(cbSPCCGraph);
  rowAI2.addStretch();
  gAI.sizer.add(rowAI2);
  dlg.sizer.add(gAI);

  var gRGB=new GroupBox(dlg);
  gRGB.title="RGB Outputs";
  gRGB.sizer=new VerticalSizer;
  gRGB.sizer.margin=8;
  gRGB.sizer.spacing=6;

  var rowRGBMaster=new HorizontalSizer;
  rowRGBMaster.spacing=10;
  var chkProcRGB=new CheckBox(gRGB);
  chkProcRGB.text="✅ Process RGB / Color";
  chkProcRGB.checked=state.processRGB;
  chkProcRGB.toolTip="Enable or disable all RGB/Color processing and outputs.";
  rowRGBMaster.add(chkProcRGB);
  rowRGBMaster.addStretch();
  gRGB.sizer.add(rowRGBMaster);

  var cbCombine=new CheckBox(dlg);
  cbCombine.text="Auto-detect & combine R+G+B masters";
  cbCombine.checked=state.combineRGB;
  gRGB.sizer.add(cbCombine);

  var rowR1=new HorizontalSizer;
  rowR1.spacing=10;
  var lR1=new Label(dlg);
  lR1.text="🏆 Finals:";
  lR1.minWidth=120;
  var rFinalS=new CheckBox(dlg);
  rFinalS.text="Final (stretched, with stars) (TIFF)";
  rFinalS.checked=state.save.rgb.final_stretched;
  var rFinalL=new CheckBox(dlg);
  rFinalL.text="Final (linear, with stars)";
  rFinalL.checked=state.save.rgb.final_linear;
  rowR1.add(lR1);
  rowR1.add(rFinalS);
  rowR1.add(rFinalL);
  rowR1.addStretch();
  gRGB.sizer.add(rowR1);

  var rowR2=new HorizontalSizer;
  rowR2.spacing=10;
  var lR2=new Label(dlg);
  lR2.text="🎭 Masks & Starless:";
  lR2.minWidth=120;
  var rStarsS=new CheckBox(dlg);
  rStarsS.text="Stars (stretched mask) (TIFF)";
  rStarsS.checked=state.save.rgb.stars_stretched;
  var rgbEnh=new CheckBox(dlg);
  rgbEnh.text="✨ Enhance with rich star colors";
  rgbEnh.checked=state.colorEnhanceRGBStarsStretched;
  var rSLessS=new CheckBox(dlg);
  rSLessS.text="Starless (stretched) (TIFF)";
  rSLessS.checked=state.save.rgb.starless_stretched;
  var rSLessL=new CheckBox(dlg);
  rSLessL.text="Starless (linear)";
  rSLessL.checked=state.save.rgb.starless_linear;
  rowR2.add(lR2);
  rowR2.add(rStarsS);
  rowR2.add(rgbEnh);
  rowR2.add(rSLessS);
  rowR2.add(rSLessL);
  rowR2.addStretch();
  gRGB.sizer.add(rowR2);
  dlg.sizer.add(gRGB);

  var gM=new GroupBox(dlg);
  gM.title="Monochrome / Narrowband Outputs";
  gM.sizer=new VerticalSizer;
  gM.sizer.margin=8;
  gM.sizer.spacing=6;

  var rowMonoMaster=new HorizontalSizer;
  rowMonoMaster.spacing=10;
  var chkProcMono=new CheckBox(gM);
  chkProcMono.text="✅ Process Monochrome / Singles";
  chkProcMono.checked=state.processMonochrome;
  chkProcMono.toolTip="Enable or disable all Monochrome/single-channel processing and outputs.";
  rowMonoMaster.add(chkProcMono);
  rowMonoMaster.addStretch();
  gM.sizer.add(rowMonoMaster);

  var rowM1=new HorizontalSizer;
  rowM1.spacing=10;
  var lM1=new Label(dlg);
  lM1.text="🏆 Finals:";
  lM1.minWidth=120;
  var mFinalS=new CheckBox(dlg);
  mFinalS.text="Final (stretched, with stars) (TIFF)";
  mFinalS.checked=state.save.mono.final_stretched;
  var mFinalL=new CheckBox(dlg);
  mFinalL.text="Final (linear, with stars)";
  mFinalL.checked=state.save.mono.final_linear;
  rowM1.add(lM1);
  rowM1.add(mFinalS);
  rowM1.add(mFinalL);
  rowM1.addStretch();
  gM.sizer.add(rowM1);

  var rowM2=new HorizontalSizer;
  rowM2.spacing=10;
  var lM2=new Label(dlg);
  lM2.text="🎭 Starless & Masks:";
  lM2.minWidth=120;
  var mStarsS=new CheckBox(dlg);
  mStarsS.text="Stars (stretched mask) (TIFF)";
  mStarsS.checked=state.save.mono.stars_stretched;
  var mSLessS=new CheckBox(dlg);
  mSLessS.text="Starless (stretched) (TIFF)";
  mSLessS.checked=state.save.mono.starless_stretched;
  var mSLessL=new CheckBox(dlg);
  mSLessL.text="Starless (linear)";
  mSLessL.checked=state.save.mono.starless_linear;
  rowM2.add(lM2);
  rowM2.add(mStarsS);
  rowM2.add(mSLessS);
  rowM2.add(mSLessL);
  rowM2.addStretch();
  gM.sizer.add(rowM2);
  dlg.sizer.add(gM);

  var rgbControls=[cbCombine,rFinalS,rFinalL,rStarsS,rgbEnh,rSLessS,rSLessL];
  var monoControls=[mFinalS,mFinalL,mStarsS,mSLessS,mSLessL];

  function toggleSection(enabled,controls){
    for(var i=0;i<controls.length;++i)controls[i].enabled=enabled;
  }

  chkProcRGB.onCheck=function(checked){
    toggleSection(checked,rgbControls);
  };

  chkProcMono.onCheck=function(checked){
    toggleSection(checked,monoControls);
  };

  toggleSection(chkProcRGB.checked,rgbControls);
  toggleSection(chkProcMono.checked,monoControls);

  var rowBtn=new HorizontalSizer;
  rowBtn.spacing=8;
  rowBtn.addStretch();
  var bStart=new PushButton(dlg);
  bStart.text="Start";
  bStart.icon=dlg.scaledResource(":/icons/ok.png");
  bStart.defaultButton=true;
  var bCancel=new PushButton(dlg);
  bCancel.text="Cancel";
  bCancel.icon=dlg.scaledResource(":/icons/close.png");
  rowBtn.add(bStart);
  rowBtn.add(bCancel);
  dlg.sizer.add(rowBtn);

  bCancel.onClick=function(){dlg.cancel();};
  bStart.onClick=function(){
    if(!state.inputDir||!File.directoryExists(state.inputDir)){
      (new MessageBox("Input directory does not exist:\n"+(state.inputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();
      return;
    }
    if(!state.outputDir||!File.directoryExists(state.outputDir)){
      (new MessageBox("Output base directory does not exist:\n"+(state.outputDir||"<empty>"),"Error",StdIcon_Error,StdButton_Ok)).execute();
      return;
    }
    state.processRGB=chkProcRGB.checked;
    state.processMonochrome=chkProcMono.checked;
    state.combineRGB=cbCombine.checked;
    state.ai.deblur1=cbD1.checked;
    state.ai.deblur2=cbD2.checked;
    state.ai.stretch=cbST.checked;
    state.ai.denoise=cbDN.checked;
    state.ai.starless=cbSL.checked;
    state.colorEnhanceRGBStarsStretched=rgbEnh.checked;
    state.spccGraphs=cbSPCCGraph.checked;
    state.save.rgb={
      final_stretched:rFinalS.checked,
      final_linear:rFinalL.checked,
      stars_stretched:rStarsS.checked,
      starless_stretched:rSLessS.checked,
      starless_linear:rSLessL.checked,
      integration_linear:false,
      baseline_linear:false,
      deblur1:false,
      deblur2:false,
      denoised:false
    };
    state.save.mono={
      final_stretched:mFinalS.checked,
      final_linear:mFinalL.checked,
      stars_stretched:mStarsS.checked,
      starless_stretched:mSLessS.checked,
      starless_linear:mSLessL.checked,
      integration_linear:false,
      baseline_linear:false,
      deblur1:false,
      deblur2:false,
      denoised:false
    };
    dlg.ok();
  };

  return dlg.execute();
}

// -------------------- Entrypoint --------------------
function run(){
  Console.show();
  Console.writeln("=== Post-Integration Pipeline (RGB & Mono) — CORRECTED Stretched Star Mask ===");

  var settingsKey="PostIntegrationPipeline";
  var state={
    inputDir:Settings.read(settingsKey+"/InputDir",DataType_String)||defaults.inputDir,
    outputDir:Settings.read(settingsKey+"/OutputDir",DataType_String)||defaults.outputDir,
    processRGB:defaults.processRGB,
    processMonochrome:defaults.processMonochrome,
    combineRGB:defaults.combineRGB,
    ai:{
      deblur1:defaults.ai.deblur1,
      deblur2:defaults.ai.deblur2,
      stretch:defaults.ai.stretch,
      denoise:defaults.ai.denoise,
      starless:defaults.ai.starless
    },
    colorEnhanceRGBStarsStretched:defaults.colorEnhanceRGBStarsStretched,
    spccGraphs:defaults.spccGraphs,
    save:{
      rgb:defaults.save.rgb,
      mono:defaults.save.mono
    }
  };

  if(!showGUI(state)){
    Console.writeln("Cancelled.");
    return;
  }

  Settings.write(settingsKey+"/InputDir",DataType_String,state.inputDir);
  Settings.write(settingsKey+"/OutputDir",DataType_String,state.outputDir);

  Console.writeln("Input dir : "+state.inputDir);
  Console.writeln("Output dir: "+state.outputDir);

  var root=tsFolder(state.outputDir);
  Console.writeln("Output folder: "+root);
  ensureDir(root+"/5_stacked");
  ensureDir(root+"/6_final");

  var plan=buildWorkPlan(state.inputDir,state.combineRGB);
  var doRGB=state.processRGB&&plan.doRGB&&shouldProcessConfig(state.save.rgb);

  if(!state.processRGB&&!state.processMonochrome){
    Console.writeln("Both RGB and Monochrome processing are disabled. Exiting.");
    return;
  }

  if(doRGB){
    Console.writeln("\n→ Building RGB from:");
    Console.writeln("   R: "+File.extractName(plan.r));
    Console.writeln("   G: "+File.extractName(plan.g));
    Console.writeln("   B: "+File.extractName(plan.b));
    var combo=combineRGB(plan.r,plan.g,plan.b,root);
    processOne(combo.window,combo.base,root,state.ai,state.save.rgb,true,state.colorEnhanceRGBStarsStretched,false,state.spccGraphs);
    try{combo.window.forceClose();}catch(_){}
  }else{
    Console.writeln("RGB Combination: skipped (no R+G+B set found or option disabled).");
  }

  if(plan.singles.length>0){
    Console.writeln("\n→ Processing mono/narrowband/color singles: "+plan.singles.length);
    for(var i=0;i<plan.singles.length;++i){
      var singleInfo=plan.singles[i];
      var p=singleInfo.path;
      var tag=singleInfo.tag;
      var isStackedRGB=singleInfo.isStackedRGB;
      Console.writeln("\n— ["+(i+1)+"/"+plan.singles.length+"] "+File.extractName(p)+"  ["+tag+"]");
      var w=ImageWindow.open(p);
      if(w.length===0){
        Console.writeln("  ⚠️ Could not open, skipping.");
        continue;
      }
      var win=w[0],base=tag+"_"+sanitizeBase(File.extractName(p));
      var isColor=win.mainView.image.isColor;
      var saveCfg=isColor?state.save.rgb:state.save.mono;
      var procEnabled=isColor?state.processRGB:state.processMonochrome;
      if(!procEnabled||!shouldProcessConfig(saveCfg)){
        Console.writeln("  ⏭️ Skipped (processing or outputs disabled for this image type).");
        try{win.forceClose();}catch(_){}
        continue;
      }
      processOne(win,base,root,state.ai,saveCfg,false,isColor&&state.colorEnhanceRGBStarsStretched,isStackedRGB,state.spccGraphs);
      try{win.forceClose();}catch(_){}
      closeAllWindowsExcept(null);
    }
  }else{
    Console.writeln("\nNo single channel (Mono/NB/Color) masters found to process.");
  }

  Console.writeln("\n=== Done. Output: "+root+" ===");
}

try {
  run();
} catch(err){
  Console.criticalln("Error: "+err.message);
  if(DEBUG_MODE){
    (new MessageBox(err.message,"Script Aborted",StdIcon_Error,StdButton_Ok)).execute();
  }else{
    throw err;
  }
}

})(); // IIFE
