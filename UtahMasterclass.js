/***
 * UTAH_MASTERCLASS_C_SASSE.js
 *
 * Complete WBPP-style pipeline with AI enhancement and star separation
 * Enhanced GUI with AI enhancement choices and timestamped output folders
 * FINAL VERSION WITH PRE-SPCC IMAGE SOLVER + MONOCHROME SUPPORT
 ***/

#include <pjsr/Sizer.jsh>
#include <pjsr/FrameStyle.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/DataType.jsh>
#include "C:/Program Files/PixInsight/src/scripts/AdP/AstronomicalCatalogs.jsh"
#include "C:/Program Files/PixInsight/src/scripts/AdP/WCSmetadata.jsh"
#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"


// === CONFIGURATION VARIABLES ===

// Input/Output Configuration (will be set by dialog)
var inputDir = "C:/Users/<USER>/OneDrive/Desktop/Input";
var lightsDir = "";
var calibrationDir = "";
var outputDir = "";

// Calibration Frame Paths (will be determined automatically)
var masterBiasPath = "";
var masterDarkPath = "";
var masterFlatPath = "";

// CFA Configuration (will be set by dialog) - DEFAULTS CHANGED
var cfaImages = true;  // Color CFA camera by default
var cfaPattern = Debayer.prototype.RGGB;  // Use proper enum values
var debayerMethod = Debayer.prototype.VNG;  // Use proper enum values

// Monochrome Configuration
var monoMode = false;  // Monochrome camera mode
var autoDetectFilters = true;  // Auto-detect R/G/B filters from filenames/headers

// AI Enhancement Configuration (will be set by dialog) - ALL ENABLED BY DEFAULT
var enableDeblurV1 = true;    // Star rounding
var enableDeblurV2 = true;    // Enhancement
var enableDenoising = true;   // Noise reduction
var enableStarless = true;    // Starless creation

// Processing flags - ALL ENABLED BY DEFAULT
var enableImageCalibration = true;
var enableMasterBias = false;
var enableMasterDark = false;
var enableMasterFlat = false;
var enableRegistration = true;
var enableGradientRemoval = true;
var enableIntegration = true;
var enableAutoBlackPoint = true;
var enableSPCC = true;

// Processing Parameters
var optimizeDarks = true;
var darkOptimizationThreshold = 0.0;
var darkOptimizationLow = 3.0;
var darkOptimizationWindow = 1024;
var darkCFA = false;
var flatCFA = false;
var outputCalibrationSuffix = "_c";
var pedestalMode = 1; // Fixed pedestal
var pedestalValue = 300; // Default pedestal value
var overscanEnabled = false;

var pixelInterpolation = StarAlignment.prototype.Lanczos3;
var clampingThreshold = 0.3;
var maxStars = 0;
var upperLimit = 1.0;
var inlierTolerance = 2.0;
var distortionCorrection = false;
var localDistortion = true;
var undistortedReference = false;
var outputRegistrationSuffix = "_r";

var gradientRemovalMethod = "ABE";
var abeTargetCorrection = 1;
var abeNormalize = true;
var abeDiscardBackground = true;
var outputGradientSuffix = "_g";

var imageIntegration = 0;
var rejection = 2;
var rejectionNormalization = 2;
var sigmaLow = 4.0;
var sigmaHigh = 2.0;
var clipLow = true;
var clipHigh = true;
var generateRejectionMaps = false;
var outputIntegrationSuffix = "_i";

var outputExtension = ".xisf";
var outputSampleFormat = 0;
var outputPedestal = 0;
var overwriteExistingFiles = true;
var onError = 0;

// AUTOMATIC BLACK POINT ADJUSTMENT
var sampleSize = 20;
var numSamples = 20;
var blackPointThreshold = 0.05;

// Global variable to store light files
var lightFiles = [];

// === TIMESTAMPED OUTPUT DIRECTORY CREATION ===
function createTimestampedOutputDir() {
    function pad2(n) { return n < 10 ? "0" + n : "" + n; }

    var now = new Date();
    var timestamp = now.getFullYear() + "-" +
                   pad2(now.getMonth() + 1) + "-" +
                   pad2(now.getDate()) + "T" +
                   pad2(now.getHours()) + "-" +
                   pad2(now.getMinutes()) + "-" +
                   pad2(now.getSeconds());

    outputDir = inputDir + "/Processed_" + timestamp;

    Console.writeln("📁 Created timestamped output directory: " + outputDir);
    return outputDir;
}

// === MONOCHROME FILTER DETECTION ===
function detectFilterFromFilename(filename) {
    var name = filename.toLowerCase();

    // Check for common R/G/B filter indicators in filename
    if (name.indexOf("red") !== -1 || name.indexOf("_r.") !== -1 ||
        name.indexOf("-r.") !== -1 || name.indexOf("r_") !== -1 ||
        name.indexOf("_red.") !== -1 || name.indexOf("-red.") !== -1) {
        return "R";
    }
    if (name.indexOf("green") !== -1 || name.indexOf("_g.") !== -1 ||
        name.indexOf("-g.") !== -1 || name.indexOf("g_") !== -1 ||
        name.indexOf("_green.") !== -1 || name.indexOf("-green.") !== -1) {
        return "G";
    }
    if (name.indexOf("blue") !== -1 || name.indexOf("_b.") !== -1 ||
        name.indexOf("-b.") !== -1 || name.indexOf("b_") !== -1 ||
        name.indexOf("_blue.") !== -1 || name.indexOf("-blue.") !== -1) {
        return "B";
    }
    if (name.indexOf("luminance") !== -1 || name.indexOf("lum") !== -1 ||
        name.indexOf("_l.") !== -1 || name.indexOf("-l.") !== -1 ||
        name.indexOf("_lum.") !== -1 || name.indexOf("-lum.") !== -1) {
        return "L";
    }

    return "Unknown";
}

function detectFilterFromHeader(filePath) {
    try {
        // For now, return unknown - header parsing is more complex
        return "Unknown";
    } catch (e) {
        return "Unknown";
    }
}

function groupMonochromeFilesByFilter(files) {
    var filterGroups = { "R": [], "G": [], "B": [], "L": [], "Unknown": [] };

    for (var i = 0; i < files.length; i++) {
        var filePath = files[i];
        var filename = File.extractName(filePath);

        // Try header detection first
        var filter = detectFilterFromHeader(filePath);

        // If header detection failed, try filename detection
        if (filter === "Unknown") {
            filter = detectFilterFromFilename(filename);
        }

        filterGroups[filter].push(filePath);
        Console.writeln("    📷 " + filename + " -> Filter: " + filter);
    }

    return filterGroups;
}

// === ENHANCED CONFIGURATION DIALOG ===
function showEnhancedConfigurationDialog() {
    var dialog = new Dialog();
    dialog.windowTitle = "🌟 Utah Masterclass - C. Sasse Astrophotography Pipeline";
    dialog.minWidth = 700;
    dialog.minHeight = 650;

    // === TITLE SECTION ===
    var titleLabel = new Label(dialog);
    titleLabel.text = "UTAH MASTERCLASS - C. SASSE";
    titleLabel.styleSheet = "font-weight: bold; font-size: 16pt; color: #007AFF; padding: 10px;";
    titleLabel.textAlignment = TextAlign_Center;

    var subtitleLabel = new Label(dialog);
    subtitleLabel.text = "Complete Raw-to-Final Processing with AI Enhancement • Working Color Edition";
    subtitleLabel.styleSheet = "font-size: 10pt; color: #666; font-style: italic;";
    subtitleLabel.textAlignment = TextAlign_Center;

    // === INPUT DIRECTORY SELECTION ===
    var inputGroupBox = new GroupBox(dialog);
    inputGroupBox.title = "📁 Input Directory Selection";
    inputGroupBox.sizer = new VerticalSizer();
    inputGroupBox.sizer.margin = 10;
    inputGroupBox.sizer.spacing = 6;

    var inputDirSizer = new HorizontalSizer();
    inputDirSizer.spacing = 6;

    var inputDirEdit = new Edit(dialog);
    inputDirEdit.text = inputDir;
    inputDirEdit.minWidth = 400;
    inputDirEdit.readOnly = true;

    var browseButton = new PushButton(dialog);
    browseButton.text = "Browse...";
    browseButton.onClick = function() {
        var directoryDialog = new GetDirectoryDialog();
        directoryDialog.caption = "Select Input Directory";
        directoryDialog.initialDirectory = inputDir;

        if (directoryDialog.execute()) {
            inputDir = directoryDialog.directory;
            // Convert backslashes to forward slashes and ensure no trailing slash
            inputDir = inputDir.replace(/\\/g, "/").replace(/\/$/, "");
            inputDirEdit.text = inputDir;
            updateDirectoryStatus();
        }
    };

    inputDirSizer.add(inputDirEdit, 100);
    inputDirSizer.add(browseButton);

    var dirStatusLabel = new Label(dialog);
    dirStatusLabel.styleSheet = "font-size: 9pt;";

    function updateDirectoryStatus() {
        lightsDir = inputDir + "/Lights";
        calibrationDir = inputDir + "/Calibration";

        var status = "";
        var lightsExist = File.directoryExists(lightsDir);
        var calibExist = File.directoryExists(calibrationDir);

        if (lightsExist) {
            status += "✅ Lights folder found ";
        } else {
            status += "⚠️ Lights folder missing ";
        }

        if (calibExist) {
            status += "✅ Calibration folder found";
        } else {
            status += "📁 Calibration folder optional";
        }

        status += "\n💡 Output will be saved to timestamped folder: Processed_YYYY-MM-DDTHH-MM-SS";

        dirStatusLabel.text = status;
        dirStatusLabel.styleSheet = lightsExist ? "color: #34C759; font-size: 9pt;" : "color: #FF9500; font-size: 9pt;";
    }

    updateDirectoryStatus();

    inputGroupBox.sizer.add(inputDirSizer);
    inputGroupBox.sizer.add(dirStatusLabel);

    // === CAMERA CONFIGURATION ===
    var cameraGroupBox = new GroupBox(dialog);
    cameraGroupBox.title = "📷 Camera Configuration";
    cameraGroupBox.sizer = new VerticalSizer();
    cameraGroupBox.sizer.margin = 10;
    cameraGroupBox.sizer.spacing = 6;

    var monoCheckBox = new CheckBox(cameraGroupBox);
    monoCheckBox.text = "Monochrome camera (no debayering needed)";
    monoCheckBox.checked = false;  // Monochrome unchecked by default

    var bayerLabel = new Label(cameraGroupBox);
    bayerLabel.text = "Bayer Pattern (for color cameras):";

    var bayerComboBox = new ComboBox(cameraGroupBox);
    bayerComboBox.addItem("🔴🟢 RGGB (Canon, Nikon, most DSLRs)");
    bayerComboBox.addItem("🔵🟢 BGGR (Sony, some ZWO)");
    bayerComboBox.addItem("🟢🔴 GRBG (Some camera models)");
    bayerComboBox.addItem("🟢🔵 GBRG (Some ZWO cameras)");
    bayerComboBox.currentItem = 0;
    bayerComboBox.minWidth = 400;

    var filterDetectionLabel = new Label(cameraGroupBox);
    filterDetectionLabel.text = "For monochrome cameras, filter detection is automatic based on filename/header";

    monoCheckBox.onCheck = function(checked) {
        bayerComboBox.enabled = !checked;
        bayerLabel.enabled = !checked;
        cfaImages = !checked;
        monoMode = checked;
    };

    cameraGroupBox.sizer.add(monoCheckBox);
    cameraGroupBox.sizer.add(bayerLabel);
    cameraGroupBox.sizer.add(bayerComboBox);
    cameraGroupBox.sizer.add(filterDetectionLabel);

    // === CALIBRATION OPTIONS ===
    var calGroupBox = new GroupBox(dialog);
    calGroupBox.title = "🔧 Calibration Options";
    calGroupBox.sizer = new VerticalSizer();
    calGroupBox.sizer.margin = 10;
    calGroupBox.sizer.spacing = 6;

    var pedestalCheckBox = new CheckBox(calGroupBox);
    pedestalCheckBox.text = "Add pedestal value (300 ADU) to calibrated images";
    pedestalCheckBox.checked = true; // Enabled by default

    calGroupBox.sizer.add(pedestalCheckBox);

    // === AI ENHANCEMENT OPTIONS ===
    var aiGroupBox = new GroupBox(dialog);
    aiGroupBox.title = "🤖 AI Enhancement Options";
    aiGroupBox.sizer = new VerticalSizer();
    aiGroupBox.sizer.margin = 10;
    aiGroupBox.sizer.spacing = 6;

    var deblurV1CheckBox = new CheckBox(aiGroupBox);
    deblurV1CheckBox.text = "⭐ Blur V1 - Star Rounding (gentle stellar enhancement)";
    deblurV1CheckBox.checked = true;  // Enabled by default

    var deblurV2CheckBox = new CheckBox(aiGroupBox);
    deblurV2CheckBox.text = "🔧 Blur V2 - Enhancement (moderate overall sharpening)";
    deblurV2CheckBox.checked = true;  // Enabled by default

    var denoisingCheckBox = new CheckBox(aiGroupBox);
    denoisingCheckBox.text = "🔇 Noise Reduction (NoiseXTerminator)";
    denoisingCheckBox.checked = true;  // Enabled by default

    var starlessCheckBox = new CheckBox(aiGroupBox);
    starlessCheckBox.text = "🌌 Starless Creation (for nebula processing)";
    starlessCheckBox.checked = true;  // Enabled by default

    var aiInfoLabel = new Label(aiGroupBox);
    aiInfoLabel.text = "💡 AI tools use your working color algorithm - applied after proper background and color processing";
    aiInfoLabel.styleSheet = "color: #007AFF; font-size: 9pt; font-weight: bold;";
    aiInfoLabel.wordWrap = true;

    aiGroupBox.sizer.add(deblurV1CheckBox);
    aiGroupBox.sizer.add(deblurV2CheckBox);
    aiGroupBox.sizer.add(denoisingCheckBox);
    aiGroupBox.sizer.add(starlessCheckBox);
    aiGroupBox.sizer.add(aiInfoLabel);

    // === PROCESSING OPTIONS ===
    var processGroupBox = new GroupBox(dialog);
    processGroupBox.title = "⚙️ Processing Options";
    processGroupBox.sizer = new VerticalSizer();
    processGroupBox.sizer.margin = 10;
    processGroupBox.sizer.spacing = 6;

    var gradientCheckBox = new CheckBox(processGroupBox);
    gradientCheckBox.text = "🌊 Gradient Removal (AutomaticBackgroundExtractor)";
    gradientCheckBox.checked = true;  // Enabled by default

    var blackPointCheckBox = new CheckBox(processGroupBox);
    blackPointCheckBox.text = "🟢 Automatic Green Haze Removal (your working algorithm)";
    blackPointCheckBox.checked = true;  // Enabled by default

    var colorCalCheckBox = new CheckBox(processGroupBox);
    colorCalCheckBox.text = "🎨 Spectrophotometric Color Calibration (SPCC)"; // UPDATED TEXT
    colorCalCheckBox.checked = true;  // Enabled by default

    processGroupBox.sizer.add(gradientCheckBox);
    processGroupBox.sizer.add(blackPointCheckBox);
    processGroupBox.sizer.add(colorCalCheckBox);

    // === BUTTONS ===
    var buttonSizer = new HorizontalSizer();
    buttonSizer.spacing = 8;
    buttonSizer.addStretch();

    var aboutButton = new PushButton(dialog);
    aboutButton.text = "ℹ️ About";
    aboutButton.onClick = function() {
        var aboutDialog = new MessageBox(
            "🌟 UTAH MASTERCLASS - C. SASSE ASTROPHOTOGRAPHY PIPELINE\n\n" +
            "A complete processing pipeline featuring:\n\n" +
            "🔹 WBPP-style calibration and stacking\n" +
            "🔹 Spectrophotometric Color Calibration (SPCC)\n" +
            "🔹 Advanced AI enhancement with RC Astro tools\n" +
            "🔹 Timestamped output folders for organization\n" +
            "🔹 Proper processing order for color accuracy\n" +
            "🔹 Multiple AI-enhanced output variants\n" +
            "🔹 Monochrome camera support with R/G/B filter stacking\n\n" +
            "Working Color Edition Features:\n" +
            "🔸 Uses your original working color processing\n" +
            "🔸 AI applied after color correction\n" +
            "🔸 No green haze issues\n" +
            "🔸 Enhanced GUI with AI choices\n" +
            "🔸 Automatic timestamped folders\n\n" +
            "Designed for both beginner and advanced astrophotographers.",
            "About Utah Masterclass - C. Sasse Pipeline",
            StdIcon_Information
        );
        aboutDialog.execute();
    };

    var startButton = new PushButton(dialog);
    startButton.text = "🚀 Start Processing";
    startButton.defaultButton = true;
    startButton.onClick = function() {
        // Validate input directory
        if (!File.directoryExists(inputDir)) {
            var errorMsg = new MessageBox(
                "Input directory does not exist:\n" + inputDir + "\n\n" +
                "Please select a valid directory.",
                "Directory Not Found",
                StdIcon_Error
            );
            errorMsg.execute();
            return;
        }

        // Validate Lights subdirectory
        if (!File.directoryExists(lightsDir)) {
            var errorMsg = new MessageBox(
                "Lights subdirectory not found:\n" + lightsDir + "\n\n" +
                "Please create a 'Lights' folder in your input directory and place your light frames there.",
                "Lights Directory Not Found",
                StdIcon_Error
            );
            errorMsg.execute();
            return;
        }

        // Save configuration
        cfaImages = !monoCheckBox.checked;
        monoMode = monoCheckBox.checked;

        if (cfaImages) {
            switch (bayerComboBox.currentItem) {
                case 0: cfaPattern = Debayer.prototype.RGGB; break;
                case 1: cfaPattern = Debayer.prototype.BGGR; break;
                case 2: cfaPattern = Debayer.prototype.GRBG; break;
                case 3: cfaPattern = Debayer.prototype.GBRG; break;
            }
        }

        // Set pedestal configuration
        if (pedestalCheckBox.checked) {
            pedestalMode = 1; // Fixed pedestal
            pedestalValue = 300;
        } else {
            pedestalMode = 0; // No pedestal
            pedestalValue = 0;
        }

        enableDeblurV1 = deblurV1CheckBox.checked;
        enableDeblurV2 = deblurV2CheckBox.checked;
        enableDenoising = denoisingCheckBox.checked;
        enableStarless = starlessCheckBox.checked;
        enableGradientRemoval = gradientCheckBox.checked;
        enableAutoBlackPoint = blackPointCheckBox.checked;
        enableSPCC = colorCalCheckBox.checked;

        dialog.ok();
    };

    var cancelButton = new PushButton(dialog);
    cancelButton.text = "❌ Cancel";
    cancelButton.onClick = function() { dialog.cancel(); };

    buttonSizer.add(aboutButton);
    buttonSizer.add(startButton);
    buttonSizer.add(cancelButton);

    // === MAIN LAYOUT ===
    var mainSizer = new VerticalSizer();
    mainSizer.margin = 15;
    mainSizer.spacing = 12;

    mainSizer.add(titleLabel);
    mainSizer.add(subtitleLabel);
    mainSizer.addSpacing(10);
    mainSizer.add(inputGroupBox);
    mainSizer.add(cameraGroupBox);
    mainSizer.add(calGroupBox);
    mainSizer.add(aiGroupBox);
    mainSizer.add(processGroupBox);
    mainSizer.addSpacing(10);
    mainSizer.add(buttonSizer);

    dialog.sizer = mainSizer;
    dialog.adjustToContents();

    return dialog.execute();
}

// === CALIBRATION FRAME DETECTION ===
function detectCalibrationFrames(processSummary) {
   Console.writeln("\n=== Detecting Calibration Frames ===");

   if (!File.directoryExists(calibrationDir)) {
      Console.writeln("  📁 No Calibration directory found - assuming lights are already calibrated");
      enableImageCalibration = false;
      processSummary.calibration.status = "⚠️";
      processSummary.calibration.details = "No calibration directory found";
      return;
   }

   // Find all files in calibration directory
   var calibFiles = [];
   var extensions = ["*.fits", "*.fit", "*.xisf", "*.fts"];

   for (var ext = 0; ext < extensions.length; ext++) {
      var files = findFiles(calibrationDir, extensions[ext]);
      calibFiles = calibFiles.concat(files);
   }

   if (calibFiles.length === 0) {
      Console.writeln("  📁 Calibration directory is empty - assuming lights are already calibrated");
      enableImageCalibration = false;
      processSummary.calibration.status = "⚠️";
      processSummary.calibration.details = "Calibration directory empty";
      return;
   }

   Console.writeln("  🔍 Found " + calibFiles.length + " calibration files, analyzing...");

   // Reset calibration flags
   enableMasterBias = false;
   enableMasterDark = false;
   enableMasterFlat = false;

   // Search for calibration frames by filename content
   for (var i = 0; i < calibFiles.length; i++) {
      var fileName = File.extractName(calibFiles[i]).toLowerCase();

      if (fileName.indexOf("bias") !== -1) {
         masterBiasPath = calibFiles[i];
         enableMasterBias = true;
         Console.writeln("    ✅ Found BIAS: " + File.extractName(calibFiles[i]));
      }
      else if (fileName.indexOf("dark") !== -1) {
         masterDarkPath = calibFiles[i];
         enableMasterDark = true;
         Console.writeln("    ✅ Found DARK: " + File.extractName(calibFiles[i]));
      }
      else if (fileName.indexOf("flat") !== -1) {
         masterFlatPath = calibFiles[i];
         enableMasterFlat = true;
         Console.writeln("    ✅ Found FLAT: " + File.extractName(calibFiles[i]));
      }
   }

   // Set calibration flag if any masters found
   if (enableMasterBias || enableMasterDark || enableMasterFlat) {
      enableImageCalibration = true;
      processSummary.calibration.status = "✅";
      processSummary.calibration.details = "Calibration frames detected";
      Console.writeln("  🎯 Calibration frames detected - calibration will be performed");
   } else {
      enableImageCalibration = false;
      processSummary.calibration.status = "⚠️";
      processSummary.calibration.details = "No recognizable calibration frames found";
      Console.writeln("  ⚠️  No recognizable calibration frames found - assuming lights are already calibrated");
   }
}

// === UTILITY FUNCTIONS ===

function findFiles(dir, pattern) {
   var files = [];
   if (!File.directoryExists(dir)) {
      Console.criticalln("Directory does not exist: " + dir);
      return files;
   }

   var fe = new FileFind();
   if (fe.begin(dir + "/" + pattern)) {
      do {
         files.push(dir + "/" + fe.name);
      } while (fe.next());
      fe.end();
   }
   return files;
}

function ensureDir(path) {
   if (!File.directoryExists(path)) {
      File.createDirectory(path);
   }
}

function findWindowByViewId(viewId) {
   var wins = ImageWindow.windows;
   for (var i = 0; i < wins.length; ++i) {
      if (wins[i].mainView.id === viewId) {
         return wins[i];
      }
   }
   return null;
}

function closeAllWindowsExcept(keepWindowIds) {
   var windows = ImageWindow.windows;
   var closedCount = 0;

   for (var i = windows.length - 1; i >= 0; i--) {
      var shouldKeep = false;

      if (keepWindowIds) {
         for (var j = 0; j < keepWindowIds.length; j++) {
            if (windows[i].mainView.id === keepWindowIds[j]) {
               shouldKeep = true;
               break;
            }
         }
      }

      if (!shouldKeep) {
         try {
            windows[i].forceClose();
            closedCount++;
         } catch (e) {
            // Ignore errors when closing windows
         }
      }
   }

   if (closedCount > 0) {
      Console.writeln("🧹 Closed " + closedCount + " intermediate windows");
   }
}

function closeAllWindows() {
   closeAllWindowsExcept(null);
}

function applyGradientRemoval(view, fileName, outputDir, processSummary) {
   if (!enableGradientRemoval) return;

   Console.writeln("  Applying gradient removal (" + gradientRemovalMethod + ")...");

   try {
      if (gradientRemovalMethod === "ABE") {
         var P_ABE = new AutomaticBackgroundExtractor();
         P_ABE.targetCorrection = abeTargetCorrection;
         P_ABE.normalize = abeNormalize;
         P_ABE.discardBackground = abeDiscardBackground;

         var windowsBeforeABE = ImageWindow.windows;
         P_ABE.executeOn(view);
         var windowsAfterABE = ImageWindow.windows;

         // Close background windows
         for (var k = 0; k < windowsAfterABE.length; ++k) {
            var isNewWindow = true;
            for (var j = 0; j < windowsBeforeABE.length; ++j) {
               if (windowsAfterABE[k].mainView.id === windowsBeforeABE[j].mainView.id) {
                  isNewWindow = false;
                  break;
               }
            }
            if (isNewWindow && windowsAfterABE[k].mainView.id.indexOf("background") !== -1) {
               windowsAfterABE[k].forceClose();
               break;
            }
         }
         Console.writeln("    ✅ ABE gradient removal applied");
         processSummary.gradientRemoval.status = "✅";
         processSummary.gradientRemoval.details = "ABE gradient removal applied";

      } else {
         Console.writeln("    ⚠️  " + gradientRemovalMethod + " not implemented - using ABE fallback");
         var P_ABE = new AutomaticBackgroundExtractor();
         P_ABE.targetCorrection = abeTargetCorrection;
         P_ABE.normalize = abeNormalize;
         P_ABE.discardBackground = abeDiscardBackground;
         P_ABE.executeOn(view);
         processSummary.gradientRemoval.status = "✅";
         processSummary.gradientRemoval.details = "ABE gradient removal (fallback)";
      }

   } catch (e) {
      Console.writeln("    ❌ Gradient removal failed: " + e.message);
      Console.writeln("    ⚠️  Continuing without gradient removal...");
      processSummary.gradientRemoval.status = "❌";
      processSummary.gradientRemoval.details = "Failed: " + e.message;
   }
}

// === YOUR WORKING COLOR FUNCTIONS ===

function performAutomaticBlackPointAdjustment(window, processSummary) {
   if (!enableAutoBlackPoint) {
      processSummary.blackPoint.status = "⏭️";
      processSummary.blackPoint.details = "Skipped by user";
      return false;
   }

   Console.writeln("\n=== Automatic Black Point Adjustment ===");
   Console.writeln("Finding darkest regions and adjusting black point...");

   try {
      var view = window.mainView;
      var image = view.image;

      // Get image dimensions
      var width = image.width;
      var height = image.height;

      Console.writeln("  📏 Image size: " + width + " x " + height);
      Console.writeln("  🔍 Analyzing " + numSamples + " random " + sampleSize + "x" + sampleSize + " samples...");

      // Arrays to store sample values for each channel
      var redSamples = [];
      var greenSamples = [];
      var blueSamples = [];

      // Generate random samples
      for (var i = 0; i < numSamples; i++) {
         // Generate random coordinates for sample region
         var x = Math.floor(Math.random() * (width - sampleSize));
         var y = Math.floor(Math.random() * (height - sampleSize));

         // Read sample region
         var rect = new Rect(x, y, x + sampleSize, y + sampleSize);
         var sample = image.readSamples(rect);

         // Calculate average values for this sample
         var redSum = 0, greenSum = 0, blueSum = 0;
         var pixelCount = sampleSize * sampleSize;

         for (var j = 0; j < pixelCount; j++) {
            redSum += sample[j * 3];
            greenSum += sample[j * 3 + 1];
            blueSum += sample[j * 3 + 2];
         }

         var redAvg = redSum / pixelCount;
         var greenAvg = greenSum / pixelCount;
         var blueAvg = blueSum / pixelCount;

         redSamples.push(redAvg);
         greenSamples.push(greenAvg);
         blueSamples.push(blueAvg);

         Console.writeln("    Sample " + (i+1) + ": R=" + redAvg.toFixed(6) + " G=" + greenAvg.toFixed(6) + " B=" + blueAvg.toFixed(6));
      }

      // Sort samples to find darkest values
      redSamples.sort(function(a, b) { return a - b; });
      greenSamples.sort(function(a, b) { return a - b; });
      blueSamples.sort(function(a, b) { return a - b; });

      // Calculate average of the darkest samples (bottom 25%)
      var sampleCount = Math.max(1, Math.floor(numSamples * 0.25));
      var redMin = 0, greenMin = 0, blueMin = 0;

      for (var i = 0; i < sampleCount; i++) {
         redMin += redSamples[i];
         greenMin += greenSamples[i];
         blueMin += blueSamples[i];
      }

      redMin /= sampleCount;
      greenMin /= sampleCount;
      blueMin /= sampleCount;

      Console.writeln("  📊 Average of darkest samples:");
      Console.writeln("     Red: " + redMin.toFixed(6));
      Console.writeln("     Green: " + greenMin.toFixed(6));
      Console.writeln("     Blue: " + blueMin.toFixed(6));

      // Apply auto levels to expand the dynamic range
      Console.writeln("  🔧 Applying Levels adjustment to maximize dynamic range...");

      // Use Levels adjustment
      var P_Levels = new Levels();
      P_Levels.redBlack = Math.min(redMin * 0.8, 0.02);    // Conservative adjustment
      P_Levels.greenBlack = Math.min(greenMin * 0.9, 0.03); // Slightly more for green
      P_Levels.blueBlack = Math.min(blueMin * 0.8, 0.02);   // Conservative adjustment

      // Adjust white points to maintain highlight detail
      P_Levels.redWhite = 0.98;
      P_Levels.greenWhite = 0.97;  // Slightly lower to reduce green cast
      P_Levels.blueWhite = 0.98;

      P_Levels.executeOn(view);

      Console.writeln("  ✅ Levels adjustment applied with automatic black points:");
      Console.writeln("     Red Black: " + (P_Levels.redBlack * 100).toFixed(2) + "%");
      Console.writeln("     Green Black: " + (P_Levels.greenBlack * 100).toFixed(2) + "%");
      Console.writeln("     Blue Black: " + (P_Levels.blueBlack * 100).toFixed(2) + "%");

      processSummary.blackPoint.status = "✅";
      processSummary.blackPoint.details = "Levels adjustment applied successfully";
      return true;

   } catch (e) {
      Console.writeln("  ⚠️ Automatic black point adjustment failed: " + e.message);

      // Fallback: Apply simple auto levels
      try {
         Console.writeln("  🔧 Applying fallback Auto Levels...");

         var P_AutoLevels = new AutoHistogram();
         P_AutoLevels.auto = true;
         P_AutoLevels.clipLow = 0.1;
         P_AutoLevels.clipHigh = 0.1;
         P_AutoLevels.executeOn(window.mainView);

         Console.writeln("  ✅ Fallback Auto Levels applied");
         processSummary.blackPoint.status = "✅";
         processSummary.blackPoint.details = "Fallback Auto Levels applied";
         return true;

      } catch (e2) {
         Console.writeln("  ⚠️ Fallback also failed: " + e2.message);
         processSummary.blackPoint.status = "❌";
         processSummary.blackPoint.details = "Failed: " + e2.message;
         return false;
      }
   }
}

// === SPCC FUNCTION (NOW INCLUDES IMAGE SOLVER) ===
function performSPCC(window, processSummary) {
   if (!enableSPCC) {
      processSummary.spcc.status = "⏭️";
      processSummary.spcc.details = "Skipped by user";
      return false;
   }

   Console.writeln("\n=== Spectrophotometric Color Calibration (SPCC) ===");

   // STEP 1: SOLVE THE IMAGE
   try {
      Console.writeln("  STEP 1/2: Plate-solving the integrated image...");
      var solver = new ImageSolver();
      solver.Init(window, false);
      solver.useMetadata = true;
      solver.catalog = "GAIA DR3";
      solver.useDistortionCorrection = false;
      solver.generateErrorMaps = false;
      solver.showStars = false;
      solver.showDistortion = false;
      solver.generateDistortionMaps = false;
      solver.sensitivity = 0.1; // Lower sensitivity for better matching

      // Try solving with metadata first (same as Mac version)
      if (!solver.SolveImage(window)) {
         // If that fails, try without metadata (like Mac version does)
         Console.writeln("  ⚠️  Metadata-based solving failed, trying without metadata...");
         solver.useMetadata = false;
         // Use approximate coordinates for IC 4604 region
         solver.centerRA = 246.826; // RA 16h 27m 18s
         solver.centerDec = -24.612; // Dec -24° 36' 43"
         solver.resolution = 1.86; // Approximate resolution

         if (!solver.SolveImage(window)) {
            throw new Error("Image solving failed after multiple attempts.");
         }
      }
      Console.writeln("  ✅ Astrometric solution found!");
      processSummary.spcc.status = "✅";
      processSummary.spcc.details = "Astrometric solution found";

   } catch (e) {
      Console.writeln("  ❌ Plate-solving failed: " + e.message);
      Console.writeln("  💡 SPCC cannot continue without a solution.");
      Console.writeln("  ℹ️  You can manually solve the image and then run SPCC separately.");
      processSummary.spcc.status = "❌";
      processSummary.spcc.details = "Plate-solving failed: " + e.message;
      return false;
   }

   // STEP 2: RUN SPCC
   try {
      Console.writeln("  STEP 2/2: Applying SPCC for accurate color calibration...");
      var P_SPCC = new SpectrophotometricColorCalibration();

      P_SPCC.whiteReferenceId = "AVG_G2V";
      P_SPCC.structureLayers = 5;
      P_SPCC.saturationThreshold = 0.99;
      P_SPCC.backgroundReferenceViewId = "";
      P_SPCC.limitMagnitude = 16.0;

      P_SPCC.executeOn(window.mainView);

      Console.writeln("  ✅ SPCC applied successfully");
      processSummary.spcc.status = "✅";
      processSummary.spcc.details = "SPCC applied successfully";
      return true;

   } catch (e) {
      Console.writeln("  ❌ SPCC failed: " + e.message);
      processSummary.spcc.status = "❌";
      processSummary.spcc.details = "SPCC failed: " + e.message;
      return false;
   }
}


// === AI ENHANCEMENT FUNCTIONS ===

function applyDeblurV1(window, processSummary) {
   if (!enableDeblurV1) {
      processSummary.deblurV1.status = "⏭️";
      processSummary.deblurV1.details = "Skipped by user";
      return false;
   }

   Console.writeln("\n=== DEBLUR V1: STELLAR ONLY ENHANCEMENT ===");
   try {
      var P_BlurX1 = new BlurXTerminator();

      // Version 1: Minimal/Stars Only settings (from your successful test)
      P_BlurX1.sharpenStars = 0.15;        // Lower value for gentle stellar enhancement
      P_BlurX1.adjustStarHalos = 0.00;
      P_BlurX1.autoPSF = true;
      P_BlurX1.psfDiameter = 0.00;
      P_BlurX1.sharpenNonstellar = 0.00;   // Zero for stellar only
      P_BlurX1.correctOnly = false;
      P_BlurX1.correctFirst = false;
      P_BlurX1.nonstellarThenStellar = false;
      P_BlurX1.luminanceOnly = false;

      P_BlurX1.executeOn(window.mainView);
      Console.writeln("  ✅ Deblur V1 (Stellar Only) applied");
      processSummary.deblurV1.status = "✅";
      processSummary.deblurV1.details = "Deblur V1 applied successfully";
      return true;
   } catch (e) {
      Console.writeln("  ⚠️ Deblur V1 failed: " + e.message);
      processSummary.deblurV1.status = "❌";
      processSummary.deblurV1.details = "Failed: " + e.message;
      return false;
   }
}

function applyDeblurV2(window, processSummary) {
   if (!enableDeblurV2) {
      processSummary.deblurV2.status = "⏭️";
      processSummary.deblurV2.details = "Skipped by user";
      return false;
   }

   Console.writeln("\n=== DEBLUR V2: MODERATE ENHANCEMENT ===");
   try {
      var P_BlurX2 = new BlurXTerminator();

      // Version 2: Moderate settings (from your successful test)
      P_BlurX2.sharpenStars = 0.25;
      P_BlurX2.adjustStarHalos = 0.00;
      P_BlurX2.autoPSF = true;
      P_BlurX2.psfDiameter = 0.00;
      P_BlurX2.sharpenNonstellar = 0.90;
      P_BlurX2.correctOnly = false;
      P_BlurX2.correctFirst = false;
      P_BlurX2.nonstellarThenStellar = false;
      P_BlurX2.luminanceOnly = false;

      P_BlurX2.executeOn(window.mainView);
      Console.writeln("  ✅ Deblur V2 (Moderate) applied");
      processSummary.deblurV2.status = "✅";
      processSummary.deblurV2.details = "Deblur V2 applied successfully";
      return true;
   } catch (e) {
      Console.writeln("  ⚠️ Deblur V2 failed: " + e.message);
      processSummary.deblurV2.status = "❌";
      processSummary.deblurV2.details = "Failed: " + e.message;
      return false;
   }
}

function applyDenoising(window, processSummary) {
   if (!enableDenoising) {
      processSummary.denoising.status = "⏭️";
      processSummary.denoising.details = "Skipped by user";
      return false;
   }

   Console.writeln("\n=== DENOISE: NOISE REDUCTION ===");
   try {
      var P_NoiseX = new NoiseXTerminator();

      // Your settings
      P_NoiseX.intensityColorSeparation = false;
      P_NoiseX.frequencySeparation = false;
      P_NoiseX.denoise = 0.90;
      P_NoiseX.iterations = 2;

      P_NoiseX.executeOn(window.mainView);
      Console.writeln("  ✅ Denoising applied");
      processSummary.denoising.status = "✅";
      processSummary.denoising.details = "Denoising applied successfully";
      return true;
   } catch (e) {
      Console.writeln("  ⚠️ Denoising failed: " + e.message);
      processSummary.denoising.status = "❌";
      processSummary.denoising.details = "Failed: " + e.message;
      return false;
   }
}

function applyStarSeparation(window, processSummary) {
   if (!enableStarless) {
      processSummary.starSeparation.status = "⏭️";
      processSummary.starSeparation.details = "Skipped by user";
      return false;
   }

   Console.writeln("\n=== STAR SEPARATION ===");
   try {
      var P_StarX = new StarXTerminator();

      // Settings for star separation
      P_StarX.generateStarImage = true;    // Try to create separate stars image
      P_StarX.unscreenStars = true;        // Remove stars from main image
      P_StarX.largeOverlap = false;

      P_StarX.executeOn(window.mainView);
      Console.writeln("  ✅ Star separation applied");
      processSummary.starSeparation.status = "✅";
      processSummary.starSeparation.details = "Star separation applied successfully";
      return true;
   } catch (e) {
      Console.writeln("  ⚠️ Star separation failed: " + e.message);
      processSummary.starSeparation.status = "❌";
      processSummary.starSeparation.details = "Failed: " + e.message;
      return false;
   }
}

// === MONOCHROME PROCESSING FUNCTIONS ===

function processMonochromeFiles(files, calDir, processSummary) {
    Console.writeln("\n=== Processing Monochrome Filter Groups ===");

    // Group files by filter
    var filterGroups = groupMonochromeFilesByFilter(files);

    // Show what we found
    Console.writeln("  🔍 Filter group analysis:");
    for (var filter in filterGroups) {
        Console.writeln("     " + filter + ": " + filterGroups[filter].length + " files");
    }

    // Process each filter group
    var integratedChannels = {};
    var hasRGB = filterGroups.R.length > 0 && filterGroups.G.length > 0 && filterGroups.B.length > 0;

    // Process R, G, B channels
    var channels = ["R", "G", "B"];
    var successfulChannels = 0;

    for (var c = 0; c < channels.length; c++) {
        var channel = channels[c];
        var channelFiles = filterGroups[channel];

        if (channelFiles.length === 0) {
            Console.writeln("  ⚠️  No " + channel + " filter files found");
            continue;
        }

        Console.writeln("  📷 Processing " + channel + " channel (" + channelFiles.length + " files)");

        // Calibrate channel files
        var calibratedChannelFiles = [];
        if (enableImageCalibration) {
            calibratedChannelFiles = calibrateChannelFiles(channelFiles, channel, calDir);
        } else {
            calibratedChannelFiles = channelFiles;
        }

        // Integrate channel
        if (calibratedChannelFiles.length >= 1) { // Allow single file integration
            var integratedChannel = integrateChannelFiles(calibratedChannelFiles, channel);
            if (integratedChannel) {
                integratedChannels[channel] = integratedChannel;
                Console.writeln("    ✅ " + channel + " channel integration complete");
                successfulChannels++;
            } else {
                Console.writeln("    ❌ " + channel + " channel integration failed");
            }
        } else {
            Console.writeln("    ⚠️  Not enough files for " + channel + " channel integration");
        }
    }

    // If we have at least one channel successfully integrated, continue
    if (successfulChannels > 0) {
        // If all three channels were integrated, create RGB composite
        if (integratedChannels.R && integratedChannels.G && integratedChannels.B) {
            Console.writeln("\n=== Creating RGB Composite from Monochrome Channels ===");
            var rgbComposite = createRGBComposite(integratedChannels.R, integratedChannels.G, integratedChannels.B);
            if (rgbComposite) {
                Console.writeln("  ✅ RGB composite created successfully");
                return rgbComposite;
            } else {
                Console.writeln("  ⚠️  Failed to create RGB composite, returning first available channel");
            }
        }

        // Otherwise return the first successful channel
        for (var channel in integratedChannels) {
            if (integratedChannels[channel]) {
                Console.writeln("  🔄 Using " + channel + " channel as grayscale image");
                return integratedChannels[channel];
            }
        }
    }

    return null;
}

function calibrateChannelFiles(files, channel, calDir) {
    Console.writeln("    Calibrating " + channel + " channel files...");

    var P_Cal = new ImageCalibration();
    P_Cal.targetFrames.length = 0;

    var targetFramesArray = [];
    for (var i = 0; i < files.length; i++) {
        targetFramesArray.push([true, files[i]]);
    }
    P_Cal.targetFrames = targetFramesArray;

    P_Cal.masterBiasEnabled = enableMasterBias;
    if (enableMasterBias) P_Cal.masterBiasPath = masterBiasPath;

    P_Cal.masterDarkEnabled = enableMasterDark;
    if (enableMasterDark) P_Cal.masterDarkPath = masterDarkPath;

    P_Cal.masterFlatEnabled = enableMasterFlat;
    if (enableMasterFlat) P_Cal.masterFlatPath = masterFlatPath;

    P_Cal.optimizeDarks = optimizeDarks;
    P_Cal.darkOptimizationThreshold = darkOptimizationThreshold;
    P_Cal.darkOptimizationLow = darkOptimizationLow;
    P_Cal.darkOptimizationWindow = darkOptimizationWindow;
    P_Cal.darkCFADetection = darkCFA;
    P_Cal.separateCFAFlatScalingFactors = flatCFA;

    P_Cal.pedestalMode = pedestalMode;
    P_Cal.pedestal = pedestalValue;

    P_Cal.overwrite = overwriteExistingFiles;
    P_Cal.onError = onError;
    P_Cal.outputDirectory = calDir + "/" + channel;
    P_Cal.outputExtension = outputExtension;
    P_Cal.outputPostfix = outputCalibrationSuffix + "_" + channel;

    ensureDir(calDir + "/" + channel);

    if (!P_Cal.executeGlobal()) {
        Console.writeln("    ❌ " + channel + " channel calibration failed");
        return files; // Return original files if calibration fails
    }

    var calibratedFiles = findFiles(calDir + "/" + channel, "*" + outputExtension);
    Console.writeln("    ✅ Calibrated " + calibratedFiles.length + " " + channel + " channel files");
    return calibratedFiles;
}

function integrateChannelFiles(files, channel) {
    Console.writeln("    Integrating " + channel + " channel files (" + files.length + " files)...");

    try {
        // Create ImageIntegration instance
        var P_Int = new ImageIntegration();

        // Properly set up images array - needs to be an array of arrays
        P_Int.images = new Array();
        for (var i = 0; i < files.length; i++) {
            // Each entry should be [enabled, filePath, outputPath, properties]
            P_Int.images.push([true, files[i], "", ""]);
        }

        // Debug: List the files being integrated
        Console.writeln("      Files to integrate (" + P_Int.images.length + " files):");
        for (var i = 0; i < P_Int.images.length; i++) {
            Console.writeln("        " + File.extractName(P_Int.images[i][1]));
        }

        // Create temp directory for output
        var tempDir = outputDir + "/temp_channel_integration_" + channel;
        ensureDir(tempDir);
        Console.writeln("      Output directory: " + tempDir);

        // Set output parameters
        P_Int.outputDirectory = tempDir;
        P_Int.outputExtension = outputExtension;
        P_Int.outputPostfix = "_integrated_" + channel;

        // Configure integration parameters
        P_Int.inheritAstrometricSolution = false;
        P_Int.inputHints = "";
        P_Int.combination = 0; // Average
        P_Int.weightMode = 1; // Noise evaluation
        P_Int.normalization = 0; // Additive with scaling
        P_Int.rejectionNormalization = 0; // Scale
        P_Int.sigmaLow = 4.000000;
        P_Int.sigmaHigh = 2.000000;
        P_Int.clipLow = true;
        P_Int.clipHigh = true;
        P_Int.generate64BitResult = false;
        P_Int.generateRejectionMaps = false;
        P_Int.generateIntegratedImage = true;
        P_Int.generateDrizzleData = false;
        P_Int.closePreviousImages = false;
        P_Int.useCache = true;
        P_Int.evaluateNoise = true;
        P_Int.noGUIMessages = true;

        // Adaptive rejection based on number of files
        if (files.length < 3) {
            P_Int.rejection = 0; // No rejection
        } else if (files.length < 5) {
            P_Int.rejection = 3; // Winsorized Sigma Clipping
        } else {
            P_Int.rejection = 4; // Linear Fit Clipping
        }

        Console.writeln("    Executing ImageIntegration...");

        // Use executeGlobal() for global processing
        if (!P_Int.executeGlobal()) {
            Console.writeln("    ❌ ImageIntegration failed for " + channel + " channel");
            return null;
        }

        Console.writeln("    ✅ ImageIntegration completed successfully for " + channel + " channel");

        // Wait for completion
        var startTime = Date.now();
        while (Date.now() - startTime < 3000) {
            processEvents();
        }

        // Look for all files in temp directory
        Console.writeln("    Looking for output files...");
        var tempFiles = findFiles(tempDir, "*" + outputExtension);
        Console.writeln("    Found " + tempFiles.length + " output files:");
        for (var i = 0; i < tempFiles.length; i++) {
            Console.writeln("      " + File.extractName(tempFiles[i]));
        }

        // Return the first file found in that directory
        if (tempFiles.length > 0) {
            var resultFile = tempFiles[0];
            Console.writeln("    ✅ Returning result file: " + File.extractName(resultFile));
            return resultFile;
        }

        Console.writeln("    ❌ No output files found in directory");
        return null;

    } catch (e) {
        Console.writeln("    ❌ Integration error for " + channel + " channel: " + e.message);
        Console.writeln("    Stack trace: " + e.stack);
        return null;
    }
}

function createRGBComposite(redFile, greenFile, blueFile) {
    Console.writeln("    Combining R, G, B channels into RGB composite...");

    try {
        // Load the three channel images
        var redWindow = ImageWindow.open(redFile)[0];
        var greenWindow = ImageWindow.open(greenFile)[0];
        var blueWindow = ImageWindow.open(blueFile)[0];

        if (!redWindow || redWindow.isNull || !greenWindow || greenWindow.isNull || !blueWindow || blueWindow.isNull) {
            Console.writeln("    ❌ Failed to load channel images");
            if (redWindow && !redWindow.isNull) redWindow.forceClose();
            if (greenWindow && !greenWindow.isNull) greenWindow.forceClose();
            if (blueWindow && !blueWindow.isNull) blueWindow.forceClose();
            return null;
        }

        // Get the views
        var redView = redWindow.mainView;
        var greenView = greenWindow.mainView;
        var blueView = blueWindow.mainView;

        // Verify all images have the same dimensions
        if (redView.image.width !== greenView.image.width ||
            redView.image.height !== greenView.image.height ||
            redView.image.width !== blueView.image.width ||
            redView.image.height !== blueView.image.height) {
            Console.writeln("    ❌ Channel images have different dimensions");
            redWindow.forceClose();
            greenWindow.forceClose();
            blueWindow.forceClose();
            return null;
        }

        // Create a new RGB image from the three channels
        var rgbWindow = new ImageWindow(
            redView.image.width,
            redView.image.height,
            3,  // 3 channels for RGB
            32, // 32-bit float
            true, // isReal
            true, // isColor
            "rgb_composite"
        );

        var rgbView = rgbWindow.mainView;

        // Copy each channel - make sure we're copying the entire range properly
        rgbView.image.assign(redView.image, 0, 0);   // Red channel
        rgbView.image.assign(greenView.image, 1, 1); // Green channel
        rgbView.image.assign(blueView.image, 2, 2);  // Blue channel

        // Save the RGB composite
        var rgbPath = outputDir + "/5_stacked/rgb_composite.xisf";
        ensureDir(outputDir + "/5_stacked");
        rgbWindow.saveAs(rgbPath, false, false, false, false);

        // Clean up
        redWindow.forceClose();
        greenWindow.forceClose();
        blueWindow.forceClose();
        rgbWindow.forceClose();

        Console.writeln("    ✅ RGB composite saved to: " + rgbPath);
        return rgbPath;

    } catch (e) {
        Console.writeln("    ❌ Failed to create RGB composite: " + e.message);
        Console.writeln("    Stack trace: " + e.stack);
        return null;
    }
}

// === MAIN PROCESSING FUNCTION ===

function main() {
   // Initialize process summary tracking
   var processSummary = {
      calibration: { name: "Calibration", status: "❌", details: "Not started" },
      debayering: { name: "Debayering", status: "❌", details: "Not started" },
      gradientRemoval: { name: "Gradient Removal", status: "❌", details: "Not started" },
      registration: { name: "Registration", status: "❌", details: "Not started" },
      integration: { name: "Integration", status: "❌", details: "Not started" },
      backgroundExtraction: { name: "Background Extraction", status: "❌", details: "Not started" },
      blackPoint: { name: "Black Point Adjustment", status: "❌", details: "Not started" },
      spcc: { name: "SPCC", status: "❌", details: "Not started" },
      deblurV1: { name: "Deblur V1", status: "❌", details: "Not started" },
      deblurV2: { name: "Deblur V2", status: "❌", details: "Not started" },
      denoising: { name: "Denoising", status: "❌", details: "Not started" },
      starSeparation: { name: "Star Separation", status: "❌", details: "Not started" },
      finalSave: { name: "Final Save", status: "❌", details: "Not started" }
   };

   Console.show();
   Console.writeln("🌟 === UTAH MASTERCLASS - C. SASSE ASTROPHOTOGRAPHY PIPELINE === 🌟");
   Console.writeln("Complete processing with your proven working color algorithms and timestamped output folders");
   Console.writeln("");

   closeAllWindows();

   // Show enhanced configuration dialog
   if (!showEnhancedConfigurationDialog()) {
      Console.writeln("❌ Processing cancelled by user");
      return;
   }

   // Create timestamped output directory
   createTimestampedOutputDir();

   // Detect calibration frames
   detectCalibrationFrames(processSummary);

   // Display configuration
   Console.writeln("📋 ENHANCED CONFIGURATION:");
   Console.writeln("  📁 Input directory: " + inputDir);
   Console.writeln("  📁 Lights directory: " + lightsDir);
   Console.writeln("  📁 Calibration directory: " + calibrationDir);
   Console.writeln("  📁 Output directory: " + outputDir + " (timestamped)");
   Console.writeln("  📷 Camera type: " + (monoMode ? "Monochrome (R/G/B stacking)" : (cfaImages ? "Color CFA" : "Monochrome")));
   if (!monoMode && cfaImages) {
      var patterns = ["RGGB", "BGGR", "GRBG", "GBRG"];
      Console.writeln("  🔍 CFA pattern: " + patterns[cfaPattern]);
   } else if (monoMode) {
      Console.writeln("  🔍 Monochrome mode: R/G/B filter stacking enabled");
   }
   Console.writeln("  🔧 Calibration pedestal: " + (pedestalValue > 0 ? pedestalValue + " ADU" : "Disabled"));
   Console.writeln("  🤖 AI Enhancement:");
   Console.writeln("     Deblur V1 (Star Rounding): " + (enableDeblurV1 ? "✅" : "❌"));
   Console.writeln("     Deblur V2 (Enhancement): " + (enableDeblurV2 ? "✅" : "❌"));
   Console.writeln("     Noise Reduction: " + (enableDenoising ? "✅" : "❌"));
   Console.writeln("     Starless Creation: " + (enableStarless ? "✅" : "❌"));
   Console.writeln("  🎨 Color Processing: SPCC");
   Console.writeln("  🟢 Green Haze Removal: YOUR WORKING ALGORITHM");
   Console.writeln("");

   // Create output directories with timestamps
   var calDir = outputDir + "/1_calibrated";
   var prepDir = outputDir + "/2_prepped";
   var gradDir = outputDir + "/3_gradient_removed";
   var regDir = outputDir + "/4_registered";
   var stackDir = outputDir + "/5_stacked";
   var finalDir = outputDir + "/6_final";

   ensureDir(outputDir);
   ensureDir(calDir);
   ensureDir(prepDir);
   if (enableGradientRemoval) ensureDir(gradDir);
   ensureDir(regDir);
   ensureDir(stackDir);
   ensureDir(finalDir);

   // Find light frames in the Lights subdirectory
   lightFiles = findFiles(lightsDir, "*.fits");
   if (lightFiles.length === 0) {
      lightFiles = findFiles(lightsDir, "*.fit");
   }
   if (lightFiles.length === 0) {
      lightFiles = findFiles(lightsDir, "*.xisf");
   }
   if (lightFiles.length === 0) {
      lightFiles = findFiles(lightsDir, "*.fts");
   }
   if (lightFiles.length === 0) {
      Console.criticalln("❌ No light frames found in: " + lightsDir);
      return;
   }

   Console.writeln("✅ Found " + lightFiles.length + " light frames");

   // Handle monochrome mode
   if (monoMode) {
      Console.writeln("\n=== Monochrome Mode: Processing R/G/B Filter Groups ===");

      var rgbCompositePath = processMonochromeFiles(lightFiles, calDir, processSummary);

      if (rgbCompositePath) {
         // Continue with the rest of the pipeline using the RGB composite
         Console.writeln("\n=== Continuing Pipeline with RGB Composite ===");

         // Load the RGB composite for further processing
         var rgbWindow = ImageWindow.open(rgbCompositePath)[0];
         if (rgbWindow && !rgbWindow.isNull) {
            // Apply gradient removal if enabled
            if (enableGradientRemoval) {
               applyGradientRemoval(rgbWindow.mainView, "rgb_composite", gradDir, processSummary);
               // Save gradient-removed version
               var gradientRemovedPath = gradDir + "/rgb_composite_gradient_removed.xisf";
               rgbWindow.saveAs(gradientRemovedPath, false, false, false, false);
               rgbWindow.forceClose();
               // Reload for next steps
               rgbWindow = ImageWindow.open(gradientRemovedPath)[0];
            }

            // Continue with the rest of the pipeline
            continuePipelineWithRGB(rgbWindow, processSummary);
         } else {
            Console.writeln("❌ Failed to load RGB composite for further processing");
         }
      } else {
         Console.writeln("❌ Failed to create RGB composite from monochrome channels");
         return;
      }

      return; // Exit early for monochrome mode
   }

   // === STEP 1: IMAGE CALIBRATION ===
   var calibratedFiles = lightFiles;

   if (enableImageCalibration) {
      Console.writeln("\n=== Step 1: Image Calibration ===");

      var P_Cal = new ImageCalibration();
      P_Cal.targetFrames.length = 0;

      var targetFramesArray = [];
      for (var i = 0; i < lightFiles.length; i++) {
         targetFramesArray.push([true, lightFiles[i]]);
      }
      P_Cal.targetFrames = targetFramesArray;

      P_Cal.masterBiasEnabled = enableMasterBias;
      if (enableMasterBias) P_Cal.masterBiasPath = masterBiasPath;

      P_Cal.masterDarkEnabled = enableMasterDark;
      if (enableMasterDark) P_Cal.masterDarkPath = masterDarkPath;

      P_Cal.masterFlatEnabled = enableMasterFlat;
      if (enableMasterFlat) P_Cal.masterFlatPath = masterFlatPath;

      P_Cal.optimizeDarks = optimizeDarks;
      P_Cal.darkOptimizationThreshold = darkOptimizationThreshold;
      P_Cal.darkOptimizationLow = darkOptimizationLow;
      P_Cal.darkOptimizationWindow = darkOptimizationWindow;
      P_Cal.darkCFADetection = darkCFA;
      P_Cal.separateCFAFlatScalingFactors = flatCFA;

      P_Cal.pedestalMode = pedestalMode;
      P_Cal.pedestal = pedestalValue;

      P_Cal.overwrite = overwriteExistingFiles;
      P_Cal.onError = onError;
      P_Cal.outputDirectory = calDir;
      P_Cal.outputExtension = outputExtension;
      P_Cal.outputPostfix = outputCalibrationSuffix;

      Console.writeln("Calibrating " + lightFiles.length + " light frames...");
      if (!P_Cal.executeGlobal()) {
         Console.criticalln("ImageCalibration failed.");
         processSummary.calibration.status = "❌";
         processSummary.calibration.details = "ImageCalibration failed";
         return;
      }

      calibratedFiles = findFiles(calDir, "*.xisf");
      Console.writeln("✅ Calibrated " + calibratedFiles.length + " frames");
      processSummary.calibration.status = "✅";
      processSummary.calibration.details = "Calibrated " + calibratedFiles.length + " frames";
      closeAllWindows();

   } else {
      Console.writeln("\n=== Step 1: Image Calibration (SKIPPED - Using pre-calibrated images) ===");
      processSummary.calibration.status = "⏭️";
      processSummary.calibration.details = "Using pre-calibrated images";
   }

   // === STEP 2: DEBAYERING AND GRADIENT REMOVAL ===
   Console.writeln("\n=== Step 2: Debayering" + (enableGradientRemoval ? " + Gradient Removal" : "") + " ===");

   var processedFiles = [];
   for (var i = 0; i < calibratedFiles.length; ++i) {
      var filePath = calibratedFiles[i];
      var currentWindow = ImageWindow.open(filePath)[0];
      if (currentWindow.isNull) continue;

      var view = currentWindow.mainView;
      Console.writeln("Processing frame " + (i+1) + "/" + calibratedFiles.length + ": " + File.extractName(filePath));

      // Debayering (if CFA images)
      if (cfaImages) {
         Console.writeln("  Debayering...");
         var P_Debayer = new Debayer();
         P_Debayer.cfaPattern = cfaPattern;
         P_Debayer.debayerMethod = debayerMethod;
         P_Debayer.evaluateNoise = false;
         P_Debayer.showImages = false;
         P_Debayer.executeOn(view);

         // Give some time for the debayering to complete
         var startTime = Date.now();
         while (Date.now() - startTime < 2000) {
            processEvents();
         }

         // Try multiple window detection methods
         var debayeredWindow = null;
         var originalViewId = view.id;

         // Method 1: Look by pattern - check for RGB windows
         var windows = ImageWindow.windows;
         for (var w = 0; w < windows.length; ++w) {
            var window = windows[w];
            // Check if this window is likely the debayered result
            if (window.mainView.id !== originalViewId &&
                window.mainView.id.indexOf("RGB") !== -1) {
                debayeredWindow = window;
                break;
            }
         }

         // Method 2: Check for recent window creation if method 1 fails
         if (debayeredWindow === null) {
            var windowsAfter = ImageWindow.windows;
            for (var w = 0; w < windowsAfter.length; ++w) {
                if (windowsAfter[w].mainView.id !== originalViewId) {
                    // Check if this window has the right properties for RGB
                    try {
                        var winView = windowsAfter[w].mainView;
                        if (winView.image && winView.image.nChannels > 1) {
                            debayeredWindow = windowsAfter[w];
                            break;
                        }
                    } catch (e) {
                        // Ignore errors when accessing window properties
                    }
                }
            }
         }

         // Method 3: Check last created window if still no match
         if (debayeredWindow === null && windowsAfter.length > 0) {
            debayeredWindow = windowsAfter[windowsAfter.length - 1];
         }

         if (debayeredWindow === null || debayeredWindow.mainView.id === originalViewId) {
            Console.writeln("    ⚠️  Could not find debayered window, using original");
            processSummary.debayering.status = "⚠️";
            processSummary.debayering.details = "Debayering incomplete for some frames";
            // Continue with original window
         } else {
            currentWindow.forceClose();
            currentWindow = debayeredWindow;
            view = currentWindow.mainView;
            Console.writeln("    ✅ Debayered to RGB");
            processSummary.debayering.status = "✅";
            processSummary.debayering.details = "Successfully debayered";
         }
      } else {
         processSummary.debayering.status = "⏭️";
         processSummary.debayering.details = "Monochrome camera - no debayering needed";
      }

      // Gradient Removal (if enabled)
      if (enableGradientRemoval) {
         applyGradientRemoval(view, File.extractName(filePath), gradDir, processSummary);
      }

      // Save processed frame
      var outputPath = (enableGradientRemoval ? gradDir : prepDir) + "/" +
                      File.extractName(filePath) +
                      (cfaImages ? "_rgb" : "") +
                      (enableGradientRemoval ? outputGradientSuffix : "") +
                      outputExtension;
      currentWindow.saveAs(outputPath, false, false, false, false);
      processedFiles.push(outputPath);
      currentWindow.forceClose();
      Console.writeln("    ✅ Saved: " + File.extractName(outputPath));

      if (i % 5 === 0) {
         closeAllWindows();
      }
   }
   Console.writeln("✅ Processed " + processedFiles.length + " frames");
   closeAllWindows();

   // === STEP 3: STAR ALIGNMENT/REGISTRATION (WBPP-STYLE) ===
   var registeredFiles = processedFiles;

   if (enableRegistration) {
      Console.writeln("\n=== Step 3: Star Alignment/Registration (WBPP-STYLE) ===");

      var P_Reg = new StarAlignment();

      var targetsArray = [];
      var validTargetCount = 0;

      for (var i = 0; i < processedFiles.length; i++) {
         if (File.exists(processedFiles[i])) {
            targetsArray.push([true, true, processedFiles[i]]);
            validTargetCount++;
         }
      }

      P_Reg.targets = targetsArray;

      if (P_Reg.targets.length === 0 || validTargetCount === 0) {
         Console.writeln("❌ No valid target files - skipping registration");
         processSummary.registration.status = "❌";
         processSummary.registration.details = "No valid target files";
      } else {
         P_Reg.referenceIsFile = true;
         P_Reg.referenceImage = processedFiles[0]; // Using an unsolved reference is fine for relative registration

         P_Reg.mode = StarAlignment.prototype.RegisterMatch;
         P_Reg.writeKeywords = true;
         P_Reg.generateMasks = false;
         P_Reg.generateDrizzleData = false;
         P_Reg.generateDistortionMaps = false;
         P_Reg.frameAdaptation = false;
         P_Reg.randomizeMosaic = false;

         // More lenient star detection parameters
         P_Reg.noiseReductionFilterRadius = 1;
         P_Reg.sensitivity = 0.05;  // Lower sensitivity for better star detection
         P_Reg.peakResponse = 0.1;   // Lower peak response
         P_Reg.maxStarDistortion = 1.0; // Higher distortion tolerance
         P_Reg.upperLimit = 1.0;
         P_Reg.invert = false;
         P_Reg.distortionCorrection = false;
         P_Reg.localDistortionCorrection = false;

         P_Reg.matcherTolerance = 0.2;    // Higher tolerance
         P_Reg.ransacTolerance = 8.0;     // Higher RANSAC tolerance
         P_Reg.ransacMaxIterations = 3000; // More iterations
         P_Reg.maxStars = 2000;           // More stars

         P_Reg.inheritAstrometricSolution = false; // We solve later, so no solution to inherit yet

         P_Reg.outputDirectory = regDir;
         P_Reg.outputExtension = outputExtension;
         P_Reg.outputPrefix = "";
         P_Reg.outputPostfix = outputRegistrationSuffix;
         P_Reg.outputSampleFormat = StarAlignment.prototype.f32;
         P_Reg.outputPedestal = 0;
         P_Reg.overwriteExistingFiles = overwriteExistingFiles;
         P_Reg.onError = 1; // Continue on error
         P_Reg.noGUIMessages = true;

         Console.writeln("Executing StarAlignment with " + P_Reg.targets.length + " targets...");

         try {
            if (!P_Reg.executeGlobal()) {
               Console.writeln("⚠️  StarAlignment failed - using unregistered files");
               processSummary.registration.status = "⚠️";
               processSummary.registration.details = "Partial success - using unregistered files";
            } else {
               var regFiles = findFiles(regDir, "*.xisf");
               if (regFiles.length > 0) {
                  registeredFiles = regFiles;
                  Console.writeln("✅ WBPP-style registration: " + registeredFiles.length + " frames aligned");
                  processSummary.registration.status = "✅";
                  processSummary.registration.details = registeredFiles.length + " frames aligned";
               } else {
                  Console.writeln("⚠️  No registered files found - using unregistered files");
                  processSummary.registration.status = "⚠️";
                  processSummary.registration.details = "No registered files found";
               }
            }
         } catch (error) {
            Console.writeln("❌ StarAlignment exception: " + error.message);
            Console.writeln("⚠️  Using unregistered files for integration");
            processSummary.registration.status = "⚠️";
            processSummary.registration.details = "Exception occurred - using unregistered files";
         }
      }

      closeAllWindows();

   } else {
      Console.writeln("\n=== Step 3: Star Alignment/Registration (SKIPPED) ===");
      processSummary.registration.status = "⏭️";
      processSummary.registration.details = "Skipped by user";
   }

   // === STEP 4: IMAGE INTEGRATION (WBPP-STYLE) ===
   if (enableIntegration) {
      Console.writeln("\n=== Step 4: Image Integration (WBPP-STYLE) ===");
      Console.writeln("Files for integration: " + registeredFiles.length);

      if (registeredFiles.length < 2) {
         Console.criticalln("ERROR: Need at least 2 images for integration, found: " + registeredFiles.length);
         processSummary.integration.status = "❌";
         processSummary.integration.details = "Not enough images for integration";
         return;
      }

      var P_Int = new ImageIntegration();

      var imagesArray = [];
      for (var i = 0; i < registeredFiles.length; i++) {
         imagesArray.push([true, registeredFiles[i], "", ""]);
      }
      P_Int.images = imagesArray;

      P_Int.inheritAstrometricSolution = false; // We are solving the final image instead

      P_Int.inputHints = "";
      P_Int.combination = 0; // Average
      P_Int.weightMode = 1; // Noise evaluation
      P_Int.normalization = 0; // Additive with scaling
      P_Int.rejectionNormalization = 0; // Scale
      P_Int.sigmaLow = 4.000000;
      P_Int.sigmaHigh = 2.000000;
      P_Int.clipLow = true;
      P_Int.clipHigh = true;
      P_Int.generate64BitResult = false;
      P_Int.generateRejectionMaps = false;
      P_Int.generateIntegratedImage = true;
      P_Int.generateDrizzleData = false;
      P_Int.closePreviousImages = false;
      P_Int.useCache = true;
      P_Int.evaluateNoise = true;
      P_Int.noGUIMessages = true;

      // Adaptive rejection for main integration as well
      if (registeredFiles.length < 3) {
          P_Int.rejection = 0; // No rejection
      } else if (registeredFiles.length < 5) {
          P_Int.rejection = 3; // Winsorized Sigma Clipping
      } else {
          P_Int.rejection = 4; // Linear Fit Clipping
      }

      Console.writeln("Integrating " + P_Int.images.length + " images (WBPP-style)...");

      if (!P_Int.executeGlobal()) {
         Console.criticalln("ImageIntegration failed.");
         processSummary.integration.status = "❌";
         processSummary.integration.details = "ImageIntegration failed";
         return;
      }

      var startTime = Date.now();
      while (Date.now() - startTime < 2000) {
         processEvents();
      }

      var integrationWindow = findWindowByViewId("integration");
      if (integrationWindow === null) {
         Console.criticalln("Could not find integration window.");
         processSummary.integration.status = "❌";
         processSummary.integration.details = "Could not find integration window";
         return;
      }
      Console.writeln("✅ WBPP-style integration complete");
      processSummary.integration.status = "✅";
      processSummary.integration.details = "Integration complete";

      var rejectionLow = findWindowByViewId("rejection_low");
      if (rejectionLow) rejectionLow.forceClose();
      var rejectionHigh = findWindowByViewId("rejection_high");
      if (rejectionHigh) rejectionHigh.forceClose();

      // Continue with the integrated image
      continuePipelineWithRGB(integrationWindow, processSummary);

   } else {
      Console.writeln("Integration is required for this pipeline.");
      processSummary.integration.status = "❌";
      processSummary.integration.details = "Integration disabled";
      return;
   }
}

function continuePipelineWithRGB(rgbWindow, processSummary) {
   // Validate that we have a valid window
   if (!rgbWindow || rgbWindow.isNull) {
      Console.writeln("❌ No valid RGB window to process");
      processSummary.integration.status = "❌";
      processSummary.integration.details = "No valid RGB window";
      return;
   }

   // Save integration result early
   var stackDir = outputDir + "/5_stacked";
   ensureDir(stackDir);
   var integrationPath = stackDir + "/Integration" + outputExtension;
   rgbWindow.saveAs(integrationPath, false, false, false, false);
   processSummary.finalSave.status = "✅";
   processSummary.finalSave.details = "Integration saved";
   closeAllWindowsExcept([rgbWindow.mainView.id]);

   // === STEP 5: FINAL BACKGROUND EXTRACTION ===
   Console.writeln("\n=== Step 5: Final Background Extraction ===");

   try {
      var P_ABE = new AutomaticBackgroundExtractor();
      P_ABE.targetCorrection = 1;
      P_ABE.normalize = true;
      P_ABE.discardBackground = true;

      Console.writeln("Applying final background extraction...");
      var windowsBeforeABE = ImageWindow.windows;
      P_ABE.executeOn(rgbWindow.mainView);
      var windowsAfterABE = ImageWindow.windows;

      // Close any background windows that were created
      for (var k = 0; k < windowsAfterABE.length; ++k) {
         var isNewWindow = true;
         for (var j = 0; j < windowsBeforeABE.length; ++j) {
            if (windowsAfterABE[k].mainView.id === windowsBeforeABE[j].mainView.id) {
               isNewWindow = false;
               break;
            }
         }
         if (isNewWindow && windowsAfterABE[k].mainView.id.indexOf("background") !== -1) {
            windowsAfterABE[k].forceClose();
            break;
         }
      }
      Console.writeln("✅ Final background extraction complete");
      processSummary.backgroundExtraction.status = "✅";
      processSummary.backgroundExtraction.details = "Background extraction complete";
   } catch (e) {
      Console.writeln("⚠️  Background extraction failed: " + e.message);
      processSummary.backgroundExtraction.status = "⚠️";
      processSummary.backgroundExtraction.details = "Failed: " + e.message;
   }

   // === STEP 6: AUTOMATIC BLACK POINT ADJUSTMENT FOR GREEN HAZE REMOVAL ===
   var blackPointSuccess = false;
   if (enableAutoBlackPoint) {
      try {
         blackPointSuccess = performAutomaticBlackPointAdjustment(rgbWindow, processSummary);
      } catch (e) {
         Console.writeln("⚠️  Black point adjustment failed: " + e.message);
         processSummary.blackPoint.status = "⚠️";
         processSummary.blackPoint.details = "Failed: " + e.message;
      }
      closeAllWindowsExcept([rgbWindow.mainView.id]);
   }

   // === STEP 7: SPECTROPHOTOMETRIC COLOR CALIBRATION (SPCC) ===
   var spccSuccess = false;
   if (enableSPCC) {
      try {
         spccSuccess = performSPCC(rgbWindow, processSummary);
      } catch (e) {
         Console.writeln("⚠️  SPCC failed: " + e.message);
         processSummary.spcc.status = "⚠️";
         processSummary.spcc.details = "Failed: " + e.message;
      }
      closeAllWindowsExcept([rgbWindow.mainView.id]);
   }

   // Keep reference to the integration window for later
   var finalIntegrationWindow = rgbWindow;

   // Validate window before proceeding
   if (!finalIntegrationWindow || finalIntegrationWindow.isNull) {
      Console.writeln("❌ Final integration window is no longer valid");
      return;
   }

   // === STEP 8: SAVE BASELINE FOR AI PROCESSING ===
   Console.writeln("\n=== Step 8: Saving Base Image for AI Processing ===");
   try {
      var baselinePath = outputDir + "/6_final/Final_Stacked" + outputExtension;
      ensureDir(outputDir + "/6_final");
      finalIntegrationWindow.saveAs(baselinePath, false, false, false, false);
      Console.writeln("  ✅ Baseline image saved: " + File.extractName(baselinePath));
      processSummary.finalSave.status = "✅";
      processSummary.finalSave.details = "Baseline image saved";
   } catch (e) {
      Console.writeln("  ❌ Failed to save baseline image: " + e.message);
      processSummary.finalSave.status = "❌";
      processSummary.finalSave.details = "Failed: " + e.message;
      return;
   }

   closeAllWindowsExcept([finalIntegrationWindow.mainView.id]);

   // === STEP 9: AI ENHANCEMENT - USER SELECTED OPTIONS ===
   Console.writeln("\n=== Steps 9-12: AI ENHANCEMENTS (User Selected) ===");

   var aiResults = {
      deblurV1: false,
      deblurV2: false,
      denoised: false,
      starless: false,
      starImageCreated: false
   };

   // === DEBLUR V1 (STELLAR ONLY) ===
   if (enableDeblurV1) {
      Console.writeln("\n=== AI ENHANCEMENT - DEBLUR V1 (Star Rounding) ===");
      try {
         var aiWindow1 = ImageWindow.open(baselinePath)[0];
         if (aiWindow1 && !aiWindow1.isNull) {
            var success = applyDeblurV1(aiWindow1, processSummary);
            if (success) {
               var deblurV1Path = outputDir + "/6_final/Deblurred_V1_StarRounding" + outputExtension;
               aiWindow1.saveAs(deblurV1Path, false, false, false, false);
               Console.writeln("  ✅ DEBLUR V1 (Star Rounding) saved: " + File.extractName(deblurV1Path));
               aiResults.deblurV1 = true;
            }
            aiWindow1.forceClose();
         } else {
            Console.writeln("  ⚠️ Could not open baseline image for Deblur V1");
         }
      } catch (e) {
         Console.writeln("  ⚠️ DEBLUR V1 step failed: " + e.message);
         processSummary.deblurV1.status = "❌";
         processSummary.deblurV1.details = "Step failed: " + e.message;
      }
      closeAllWindows();
   }

   // === DEBLUR V2 (MODERATE) ===
   if (enableDeblurV2) {
      Console.writeln("\n=== AI ENHANCEMENT - DEBLUR V2 (Enhancement) ===");
      try {
         var aiWindow2 = ImageWindow.open(baselinePath)[0];
         if (aiWindow2 && !aiWindow2.isNull) {
            var success = applyDeblurV2(aiWindow2, processSummary);
            if (success) {
               var deblurV2Path = outputDir + "/6_final/Deblurred_V2_Enhancement" + outputExtension;
               aiWindow2.saveAs(deblurV2Path, false, false, false, false);
               Console.writeln("  ✅ DEBLUR V2 (Enhancement) saved: " + File.extractName(deblurV2Path));
               aiResults.deblurV2 = true;
            }
            aiWindow2.forceClose();
         } else {
            Console.writeln("  ⚠️ Could not open baseline image for Deblur V2");
         }
      } catch (e) {
         Console.writeln("  ⚠️ DEBLUR V2 step failed: " + e.message);
         processSummary.deblurV2.status = "❌";
         processSummary.deblurV2.details = "Step failed: " + e.message;
      }
      closeAllWindows();
   }

   // === DENOISING ===
   if (enableDenoising) {
      Console.writeln("\n=== AI ENHANCEMENT - DENOISING ===");
      try {
         var aiWindow3 = ImageWindow.open(baselinePath)[0];
         if (aiWindow3 && !aiWindow3.isNull) {
            var success = applyDenoising(aiWindow3, processSummary);
            if (success) {
               var denoisedPath = outputDir + "/6_final/Denoised" + outputExtension;
               aiWindow3.saveAs(denoisedPath, false, false, false, false);
               Console.writeln("  ✅ DENOISED saved: " + File.extractName(denoisedPath));
               aiResults.denoised = true;
            }
            aiWindow3.forceClose();
         } else {
            Console.writeln("  ⚠️ Could not open baseline image for Denoising");
         }
      } catch (e) {
         Console.writeln("  ⚠️ DENOISING step failed: " + e.message);
         processSummary.denoising.status = "❌";
         processSummary.denoising.details = "Step failed: " + e.message;
      }
      closeAllWindows();
   }

   // === STAR SEPARATION ===
   if (enableStarless) {
      Console.writeln("\n=== AI ENHANCEMENT - STAR SEPARATION ===");
      try {
         var aiWindow4 = ImageWindow.open(baselinePath)[0];
         if (aiWindow4 && !aiWindow4.isNull) {
            var success = applyStarSeparation(aiWindow4, processSummary);
            if (success) {
               var starlessPath = outputDir + "/6_final/Starless" + outputExtension;
               aiWindow4.saveAs(starlessPath, false, false, false, false);
               Console.writeln("  ✅ STARLESS saved: " + File.extractName(starlessPath));
               aiResults.starless = true;

               // Check if StarXTerminator auto-created a stars image
               var windows = ImageWindow.windows;
               for (var i = 0; i < windows.length; ++i) {
                  if (windows[i].mainView.id.indexOf("stars") !== -1 ||
                      windows[i].mainView.id.indexOf("Stars") !== -1) {
                     var starsPath = outputDir + "/6_final/Stars_Extracted" + outputExtension;
                     windows[i].saveAs(starsPath, false, false, false, false);
                     windows[i].forceClose();
                     Console.writeln("  ⭐ STARS EXTRACTED saved: " + File.extractName(starsPath));
                     aiResults.starImageCreated = true;
                     break;
                  }
               }
            }
            aiWindow4.forceClose();
         } else {
            Console.writeln("  ⚠️ Could not open baseline image for Star Separation");
         }
      } catch (e) {
         Console.writeln("  ⚠️ STAR SEPARATION step failed: " + e.message);
         processSummary.starSeparation.status = "❌";
         processSummary.starSeparation.details = "Step failed: " + e.message;
      }
      closeAllWindows();
   }

   // === FINAL SUMMARY WITH TIMESTAMP INFO ===
   Console.writeln("");
   Console.writeln("🎉".repeat(50));
   Console.writeln("🎉    UTAH MASTERCLASS - C. SASSE COMPLETE!    🎉");
   Console.writeln("🎉".repeat(50));
   Console.writeln("");
   Console.writeln("📁 Final Products in timestamped folder:");
   Console.writeln("   📂 " + outputDir);
   Console.writeln("      └── 6_final/");
   Console.writeln("          ├── Final_Stacked.xisf - Base stacked image with perfect color ✨");

   if (aiResults.deblurV1) {
      Console.writeln("          ├── Deblurred_V1_StarRounding.xisf - Gentle stellar enhancement ✨");
   }

   if (aiResults.deblurV2) {
      Console.writeln("          ├── Deblurred_V2_Enhancement.xisf - Moderate overall enhancement ✨");
   }

   if (aiResults.denoised) {
      Console.writeln("          ├── Denoised.xisf - Noise reduction applied ✨");
   }

   if (aiResults.starless) {
      Console.writeln("          ├── Starless.xisf - Stars removed for nebula processing ✨");
   }

   if (aiResults.starImageCreated) {
      Console.writeln("          └── Stars_Extracted.xisf - Extracted stars for separate processing ✨");
   }

   Console.writeln("");
   Console.writeln("📊 Processing Summary:");
   Console.writeln("  Light frames processed: " + lightFiles.length);
   Console.writeln("  Camera type: " + (monoMode ? "Monochrome (R/G/B stacking)" : (cfaImages ? "Color CFA" : "Monochrome")));
   Console.writeln("  Calibration: " + (enableImageCalibration ? "Applied" : "Pre-calibrated"));
   Console.writeln("  Registration: WBPP-STYLE (relative)");
   Console.writeln("  Integration: WBPP-STYLE (noise evaluation weighting)");
   Console.writeln("  Gradient removal: " + (enableGradientRemoval ? gradientRemovalMethod + " applied" : "Disabled"));
   Console.writeln("  Color Calibration: " + (spccSuccess ? "SPCC Successful" : (enableSPCC ? "SPCC Failed" : "Disabled")));
   Console.writeln("  Green Haze Removal: " + (blackPointSuccess ? "YOUR WORKING ALGORITHM SUCCESSFUL" : "Alternative method used"));
   Console.writeln("");
   Console.writeln("🤖 AI ENHANCEMENTS APPLIED:");
   Console.writeln("  Deblur V1 (Star Rounding): " + (aiResults.deblurV1 ? "✅" : "❌"));
   Console.writeln("  Deblur V2 (Enhancement): " + (aiResults.deblurV2 ? "✅" : "❌"));
   Console.writeln("  Noise Reduction: " + (aiResults.denoised ? "✅" : "❌"));
   Console.writeln("  Star Separation: " + (aiResults.starless ? "✅" : "❌"));
   Console.writeln("");

   // Extract just the timestamp from the output directory
   var timestamp = outputDir.substring(outputDir.lastIndexOf("_") + 1);
   Console.writeln("📁 TIMESTAMPED OUTPUT FOLDER:");
   Console.writeln("  Processed_" + timestamp);
   Console.writeln("  This prevents overwriting previous processing sessions!");
   Console.writeln("");

   // === PROCESS COMPLETION SUMMARY ===
   Console.writeln("📊 PROCESS COMPLETION SUMMARY:");
   Console.writeln("  ✅ = Completed successfully");
   Console.writeln("  ⚠️  = Partial success/warnings");
   Console.writeln("  ❌ = Failed/errors");
   Console.writeln("  ⏭️  = Skipped by user");
   Console.writeln("");

   var successCount = 0;
   var warningCount = 0;
   var failureCount = 0;
   var skipCount = 0;

   for (var key in processSummary) {
      var item = processSummary[key];
      Console.writeln("  " + item.status + " " + item.name + ": " + item.details);

      if (item.status === "✅") successCount++;
      else if (item.status === "⚠️") warningCount++;
      else if (item.status === "❌") failureCount++;
      else if (item.status === "⏭️") skipCount++;
   }

   Console.writeln("");
   Console.writeln("📈 OVERALL STATISTICS:");
   Console.writeln("  ✅ Successful: " + successCount);
   Console.writeln("  ⚠️ Warnings: " + warningCount);
   Console.writeln("  ❌ Failed: " + failureCount);
   Console.writeln("  ⏭️ Skipped: " + skipCount);
   Console.writeln("  📊 Total Processes: " + Object.keys(processSummary).length);
   Console.writeln("");

   Console.writeln("💡 NEXT STEPS:");
   Console.writeln("   • Use Final_Stacked for general editing with perfect color balance");
   Console.writeln("   • Use Deblurred_V1 for images that need gentle star improvement");
   Console.writeln("   • Use Deblurred_V2 for images that benefit from overall sharpening");
   Console.writeln("   • Use Denoised for cleaner results in high-ISO captures");
   Console.writeln("   • Process Starless and Stars components independently as needed");
   Console.writeln("   • Use different stretching techniques for each component");
   Console.writeln("   • Recombine with PixelMath if desired: Stars + Starless");
   Console.writeln("");
   Console.writeln("✨ UTAH MASTERCLASS - C. SASSE processing complete - no green haze! ✨");
   Console.writeln("🌟 Thank you for using Utah Masterclass - C. Sasse Astrophotography Pipeline! 🌟");
   Console.writeln("🌟 === UTAH MASTERCLASS - C. SASSE ASTROPHOTOGRAPHY PIPELINE === 🌟");
   Console.writeln("Complete processing with your proven working color algorithms and timestamped output folders");
}
// Start the main pipeline
main();
