#feature-id Scripts > ImageProcessing > AutoIntegrateMimic

#feature-info A script that mimics AutoIntegrate for automating basic astro image processing in PixInsight.

#include <pjsr/Sizer.jsh>
#include <pjsr/FrameStyle.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/UndoFlag.jsh>

function AutoMimicDialog() {
  this.__base__ = Dialog;
  this.__base__();
  this.restyle();

  // File list
  this.fileList = new TreeBox(this);
  this.fileList.numberOfColumns = 1;
  this.fileList.headerVisible = false;

  // Add files button
  this.addFileButton = new PushButton(this);
  this.addFileButton.text = "Add Files";
  this.addFileButton.parentDialog = this;
  this.addFileButton.onClick = function() {
    var ofd = new OpenFileDialog;
    ofd.multipleSelections = true;
    ofd.filters = [["Image files", "*.fits", "*.fit", "*.fts", "*.xisf"], ["All files", "*"]];
    if (ofd.execute()) {
      var existing = [];
      for (var k = 0; k < this.parentDialog.fileList.numberOfChildren; ++k) {
        existing.push(this.parentDialog.fileList.child(k).text(0));
      }
      for (var i = 0; i < ofd.fileNames.length; ++i) {
        if (existing.indexOf(ofd.fileNames[i]) === -1) {
          var node = new TreeBoxNode(this.parentDialog.fileList);
          node.setText(0, ofd.fileNames[i]);
        }
      }
      this.parentDialog.adjustToContents();
    }
  };

  // Remove selected button
  this.removeFileButton = new PushButton(this);
  this.removeFileButton.text = "Remove Selected";
  this.removeFileButton.parentDialog = this;
  this.removeFileButton.onClick = function() {
    var selected = this.parentDialog.fileList.selectedNodes;
    for (var i = selected.length - 1; i >= 0; --i) {
      this.parentDialog.fileList.remove(selected[i]);
    }
    this.parentDialog.adjustToContents();
  };

  // Run button
  this.runButton = new PushButton(this);
  this.runButton.text = "Run";
  this.runButton.parentDialog = this;
  this.runButton.onClick = function() {
    var files = [];
    for (var i = 0; i < this.parentDialog.fileList.numberOfChildren; ++i) {
      files.push(this.parentDialog.fileList.child(i).text(0));
    }
    if (files.length === 0) {
      (new MessageBox("No files selected.")).execute();
      return;
    }

    // Remove duplicates
    var uniqueFiles = [];
    for (var i = 0; i < files.length; ++i) {
      if (uniqueFiles.indexOf(files[i]) === -1) {
        uniqueFiles.push(files[i]);
      }
    }

    if (uniqueFiles.length === 0) {
      (new MessageBox("No unique files selected.")).execute();
      return;
    }

    console.show();
    console.writeln("Processing " + uniqueFiles.length + " unique files...");

    // Group by filter
    var filterGroups = {};
    for (var i = 0; i < uniqueFiles.length; ++i) {
      var file = uniqueFiles[i];
      var match = file.match(/-(Red|Green|Blue)-/i);
      var filter = match ? match[1].toUpperCase() : 'UNKNOWN';
      if (!filterGroups[filter]) filterGroups[filter] = [];
      filterGroups[filter].push(file);
    }

    var integratedViews = {};
    var failedViews = [];
    var outputDir = File.systemTempDirectory;
    if (!outputDir.endsWith('/') && !outputDir.endsWith('\\')) {
      outputDir += '/';
    }
    console.writeln("Temp directory for output: " + outputDir);

    for (var filter in filterGroups) {
      var group = filterGroups[filter];
      if (group.length === 0) continue;
      console.writeln("Processing filter " + filter + " with " + group.length + " files...");

      var integratedView;

      if (group.length === 1) {
        console.writeln("Opening single file for filter " + filter + ": " + group[0]);
        var windows = ImageWindow.open(group[0]);
        if (windows.length === 0) {
          console.criticalln("Failed to open file: " + group[0]);
          failedViews.push(filter);
          continue;
        }
        var w = windows[0];
        w.show();
        integratedView = w.mainView;
      } else {
        // === STAR ALIGNMENT ===
        var alignedFiles = [];
        var reference = group[0];
        console.writeln("Using reference for " + filter + ": " + reference);

        var sa = new StarAlignment;
        sa.referenceImage = reference;
        sa.referenceIsFile = true;
        sa.outputDir = outputDir;
        sa.outputExtension = ".xisf";
        sa.overwriteExistingFiles = true;
        sa.useSurfaceSplines = true;
        sa.pixelInterpolation = 1; // Auto
        sa.clampingThreshold = 0.30;
        sa.mode = 1; // RegisterMatch

        // Align all OTHER files EXCEPT reference
        var targetImages = [];
        for (var j = 1; j < group.length; ++j) {
          var target = group[j];
          var name = File.extractName(target);
          var alignedPath = outputDir + name + '_r' + sa.outputExtension;
          targetImages.push([target, alignedPath]);
        }

        if (targetImages.length > 0) {
          sa.targetImages = targetImages;
          console.writeln("Aligning " + targetImages.length + " files for " + filter + "...");

          var alignSuccess = false;
          try {
            alignSuccess = sa.executeGlobal(); // No flags
          } catch (e) {
            console.criticalln("Exception during alignment for " + filter + ": " + e.toString());
            alignSuccess = false;
          }

          if (!alignSuccess) {
            console.warning("Alignment failed for " + filter + ". Using original files.");
            alignedFiles = group.slice(); // all originals
          } else {
            alignedFiles.push(reference);
            for (var i = 0; i < targetImages.length; ++i) {
              alignedFiles.push(targetImages[i][1]); // use aligned versions
            }
          }
        } else {
          console.warning("No extra targets for alignment. Using reference alone for " + filter);
          alignedFiles = [reference];
        }

        console.writeln("Number of files to integrate for " + filter + ": " + alignedFiles.length);

        if (alignedFiles.length === 0) {
          console.criticalln("No files to integrate for " + filter);
          failedViews.push(filter);
          continue;
        }

        // === IMAGE INTEGRATION ===
        var ii = new ImageIntegration;
        ii.images = [];

        for (var k = 0; k < alignedFiles.length; ++k) {
          ii.images.push([true, alignedFiles[k], "", ""]);
        }

        ii.combination = 1;         // Average
        ii.weighting = 1;           // NoiseEvaluation
        ii.normalization = 2;       // AdditiveWithScaling
        ii.generateIntegratedImage = true;

        if (alignedFiles.length >= 3) {
          ii.rejection = 5;          // WinsorizedSigmaClipping
          ii.rejectionNormalization = 1; // Scale
        } else {
          console.warning("Only " + alignedFiles.length + " images for " + filter + " — disabling rejection.");
          ii.rejection = 0;
          ii.rejectionNormalization = 0;
        }

        var integrateSuccess = false;
        try {
          integrateSuccess = ii.executeGlobal(); // No flags
        } catch (e) {
          console.criticalln("Integration exception for " + filter + ": " + e.toString());
        }

        if (!integrateSuccess) {
          console.criticalln("Image integration FAILED for " + filter);
          failedViews.push(filter);
          continue;
        }

        // Get integrated view
        var integratedWindow = ImageWindow.windowById("integration");
        if (integratedWindow.isNull) {
          console.criticalln("Integration window not found for " + filter);
          failedViews.push(filter);
          continue;
        }

        integratedWindow.show();
        integratedView = integratedWindow.mainView;
        console.writeln("Integrated image created for " + filter);
      }

      integratedViews[filter] = integratedView;
    }

    if (failedViews.length > 0) {
      console.warning("There were failures with filters: ", failedViews.join(", "));
    }

    // === COMBINE RGB IF ALL CHANNELS ARE FOUND ===
    var red = integratedViews['RED'];
    var green = integratedViews['GREEN'];
    var blue = integratedViews['BLUE'];

    if (!red || !green || !blue) {
      console.criticalln("Missing one or more RGB channels. Cannot generate color image.");
      return;
    }

    console.writeln("Combining RGB channels...");

    var cc = new ChannelCombination;
    cc.colorSpace = 0; // RGB
    cc.channels = [
      [true, red.id],
      [true, green.id],
      [true, blue.id]
    ];

    if (!cc.executeGlobal()) {
      console.criticalln("RGB channel combination FAILED.");
      return;
    }

    var rgbWindow = ImageWindow.activeWindow;
    if (rgbWindow.isNull) {
      console.criticalln("RGB was not generated properly.");
      return;
    }

    rgbWindow.show();
    var finalView = rgbWindow.mainView;

    // === APPLY FINAL ADJUSTMENTS ===
    console.writeln("Applying background extraction...");
    var abe = new AutomaticBackgroundExtractor;
    abe.sampleSize = 10;
    abe.executeOn(finalView);

    console.writeln("Applying color calibration...");
    var cc2 = new ColorCalibration;
    cc2.executeOn(finalView);

    console.writeln("Applying auto stretch...");
    var stf = new ScreenTransferFunction;
    stf.autoStretch(finalView);

    console.writeln("Processing completed successfully.");

    // Close dialog after a moment to prevent lockup
    setTimeout(function () {
      this.parentDialog.ok();
    }.bind(this), 100);
  };

  // Buttons sizer
  this.buttonsSizer = new HorizontalSizer;
  this.buttonsSizer.add(this.addFileButton);
  this.buttonsSizer.add(this.removeFileButton);
  this.buttonsSizer.addStretch();
  this.buttonsSizer.add(this.runButton);

  // Main sizer
  this.sizer = new VerticalSizer;
  this.sizer.margin = 6;
  this.sizer.spacing = 6;
  this.sizer.add(this.fileList, 100);
  this.sizer.add(this.buttonsSizer);

  this.windowTitle = "AutoIntegrate Mimic";
  this.userResizable = true;
  this.adjustToContents();
}

AutoMimicDialog.prototype = new Dialog;

function main() {
  var dialog = new AutoMimicDialog();
  dialog.execute();
}

main();
