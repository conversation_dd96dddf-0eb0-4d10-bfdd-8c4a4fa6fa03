/*
 * Simple Interactive Test - Based on Work.js
 * 
 * Fixed directories, interactive pause after blackpoint step
 */

#include "C:/Program Files/PixInsight/src/scripts/AdP/ImageSolver.js"
#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include "C:/Program Files/PixInsight/src/scripts/Toolbox/GraXpertLib.jsh"

(function(){

// -------------------- Fixed Configuration --------------------
var INPUT_DIR = "C:/Users/<USER>/OneDrive/Desktop/test/master";
var OUTPUT_DIR = "C:/Users/<USER>/OneDrive/Desktop/test/Out/Out_test";

// Normalize path function for PixInsight
function normalizePath(path) {
    return path.replace(/\\/g, "/");
}
var DEBUG_MODE = true;
var debugStepCounter = 1;

// -------------------- Utility Functions --------------------
function ensureDir(p){ 
    if(!File.directoryExists(p)) File.createDirectory(p, true); 
}

function cleanDirectory(dir) {
    if (File.directoryExists(dir)) {
        Console.writeln("🧹 Cleaning output directory: " + dir);
        Console.writeln("✅ Directory exists, will be cleaned during processing");
        // For simplicity, we'll just ensure the directory exists
        // PixInsight will overwrite files as needed
    }
    ensureDir(dir);
}

function debugSave(win, stepName, baseTag, debugDir) {
    if (!DEBUG_MODE) return;
    if (!win || !win.isWindow) return;
    
    let counterStr = debugStepCounter < 10 ? "0" + debugStepCounter : "" + debugStepCounter;
    let sanitizedStepName = stepName.replace(/[^\w\-]+/g, "_");
    let stepFolder = debugDir + "/" + counterStr + "_" + sanitizedStepName;
    
    try {
        if (!File.directoryExists(stepFolder)) {
            File.createDirectory(stepFolder, true);
        }
    } catch(e) {
        stepFolder = debugDir;
    }
    
    let baseFileName = counterStr + "_" + baseTag + "_" + sanitizedStepName;
    
    try {
        let xisfPath = stepFolder + "/" + baseFileName + ".xisf";
        win.saveAs(xisfPath, false, false, false, false);
        Console.writeln("DEBUG: Saved XISF: " + xisfPath);
    } catch(e) {
        Console.writeln("DEBUG: Failed to save XISF: " + e.message);
    }
    
    debugStepCounter++;
}

// -------------------- Interactive Functions --------------------
function showStretchedPreview(win) {
    Console.writeln("📺 Applying auto-stretch for visual review...");

    try {
        win.show();
        win.bringToFront();
        win.zoomToFit();

        var view = win.mainView;

        // Calculate proper auto-stretch parameters
        var median = view.computeOrFetchProperty("Median");
        var mad = view.computeOrFetchProperty("MAD");

        var stf = new ScreenTransferFunction;
        var n = view.image.isColor ? 3 : 1;

        Console.writeln("Computing auto-stretch for " + n + " channel(s)...");

        // Build STF parameters array
        var stfParams = [];

        for (var c = 0; c < n; ++c) {
            var med = median.at(c);
            var madVal = mad.at(c) * 1.4826; // Convert MAD to sigma

            // Auto-stretch calculation (similar to clicking auto-stretch button)
            var c0 = Math.max(0, med - 2.8 * madVal);  // Shadow clipping
            var c1 = 1.0;  // Highlight clipping
            var m = Math.mtf(0.25, med - c0);  // Midtones balance

            stfParams[c] = [c0, c1, m, 0, 1];
            Console.writeln("Channel " + c + ": c0=" + c0.toFixed(4) + ", m=" + m.toFixed(4));
        }

        // Add alpha channel (always identity)
        stfParams[3] = [0, 1, 0.5, 0, 1];

        // Apply the STF
        stf.STF = stfParams;
        stf.executeOn(view);

        // Force window refresh
        win.forceClose = false;
        win.show();
        win.bringToFront();

        Console.writeln("✅ Auto-stretch applied - image should now be visible!");
        Console.writeln("👀 Review the stretched image in the window");

        return true;
    } catch(e) {
        Console.writeln("❌ Auto-stretch failed: " + e.message);
        return false;
    }
}

function resetStretch(win) {
    try {
        win.disableScreenTransferFunctions();
        Console.writeln("✅ Reset to linear view");
        return true;
    } catch(e) {
        Console.writeln("❌ Reset failed: " + e.message);
        return false;
    }
}

function askAcceptStep(stepName) {
    Console.writeln("\n🔍 REVIEW STEP: " + stepName);
    
    var result = (new MessageBox(
        "Step: " + stepName + "\n\n" +
        "Please review the stretched image on screen.\n\n" +
        "Accept this step?",
        "Accept Step?",
        StdIcon_Question,
        StdButton_Yes, StdButton_No
    )).execute();
    
    if (result == StdButton_Yes) {
        Console.writeln("✅ ACCEPTED: " + stepName);
        return true;
    } else {
        Console.writeln("⏭️ SKIPPED: " + stepName);
        return false;
    }
}

// -------------------- Processing Functions (from Work.js) --------------------
function autoBlackPoint(win, sum, baseTag, debugDir) {
    Console.writeln("=== Running AutoBlackPoint ===");
    
    try {
        if (!win.mainView.image.isColor) {
            // Monochrome
            var AH = new AutoHistogram;
            AH.clip = false;
            AH.clipTogether = true;
            AH.stretch = false;
            AH.stretchTogether = true;
            AH.stretchMethod = AutoHistogram.prototype.MTF;
            AH.targetMedian = 0.12;
            AH.shadowsClipping = 0.00003;
            AH.highlightsClipping = 0.02;
            AH.executeOn(win.mainView);
            sum.blackPoint = {name: "Black Point", status: "✅", details: "AutoHistogram (mono/NB)"};
            
            debugSave(win, "After_BlackPoint_Mono", baseTag, debugDir);
            return true;
        } else {
            // Color image
            var AH2 = new AutoHistogram;
            AH2.clip = false;
            AH2.clipTogether = true;
            AH2.stretch = false;
            AH2.stretchTogether = true;
            AH2.stretchMethod = AutoHistogram.prototype.MTF;
            AH2.targetMedian = 0.12;
            AH2.shadowsClipping = 0.00003;
            AH2.highlightsClipping = 0.02;
            AH2.executeOn(win.mainView);
            sum.blackPoint = {name: "Black Point", status: "✅", details: "AutoHistogram (color)"};
            
            debugSave(win, "After_BlackPoint_Color", baseTag, debugDir);
        }
        return true;
    } catch(e) {
        sum.blackPoint = {name: "Black Point", status: "❌", details: e.message};
        debugSave(win, "After_BlackPoint_FAILED", baseTag, debugDir);
        return false;
    }
}

// -------------------- Main Processing --------------------
function processOneFile(inputFile, outputDir) {
    Console.writeln("\n" + "=".repeat(60));
    Console.writeln("📄 Processing: " + File.extractName(inputFile));
    
    // Open file
    var windows = ImageWindow.open(inputFile);
    if (windows.length === 0) {
        Console.criticalln("❌ Failed to open: " + inputFile);
        return false;
    }
    
    var win = windows[0];
    win.show();
    
    // Setup
    var baseTag = File.extractName(inputFile).replace(/[^\w\-]/g, "_");
    var debugDir = outputDir + "/debug";
    ensureDir(debugDir);
    var sum = {};
    
    Console.writeln("✅ Image opened: " + win.mainView.id);
    Console.writeln("📊 Image info: " + win.mainView.image.width + "x" + win.mainView.image.height + 
                   ", channels=" + win.mainView.image.numberOfChannels + 
                   ", color=" + win.mainView.image.isColor);
    
    try {
        // STEP 1: Black Point Correction
        Console.writeln("\n🔄 STEP 1: Applying Black Point Correction...");
        
        var step1Success = autoBlackPoint(win, sum, baseTag, debugDir);
        
        if (step1Success) {
            Console.writeln("✅ Black Point correction completed");
            
            // INTERACTIVE PAUSE - Show stretched preview
            showStretchedPreview(win);

            // Small delay to ensure stretch is visible
            processEvents();
            msleep(1000); // 1 second delay

            // Ask user to accept or skip
            var acceptStep1 = askAcceptStep("Black Point Correction");
            
            // Reset to linear view
            resetStretch(win);
            
            if (acceptStep1) {
                Console.writeln("✅ Step 1 ACCEPTED - continuing with changes");
            } else {
                Console.writeln("⏭️ Step 1 SKIPPED - but changes already applied");
                // TODO: In full version, we would undo the changes here
            }
        } else {
            Console.writeln("❌ Black Point correction failed");
        }
        
        // Save final result
        var finalPath = outputDir + "/Interactive_" + baseTag + ".xisf";
        win.saveAs(finalPath, false, false, false, false);
        Console.writeln("💾 Final result saved: " + finalPath);
        
        // Show summary
        Console.writeln("\n📋 PROCESSING SUMMARY:");
        Console.writeln("  Step 1 - Black Point: " + sum.blackPoint.status + " " + sum.blackPoint.details);
        
        return true;
        
    } catch (e) {
        Console.criticalln("❌ Processing failed: " + e.message);
        return false;
    }
}

// -------------------- Main Entry Point --------------------
function main() {
    Console.show();
    Console.writeln("=== Simple Interactive Test ===");
    Console.writeln("Input: " + INPUT_DIR);
    Console.writeln("Output: " + OUTPUT_DIR);

    // Normalize paths
    var normalizedOutputDir = normalizePath(OUTPUT_DIR);

    // Clean output directory
    cleanDirectory(normalizedOutputDir);
    
    // Get all image files from input directory
    var inputFiles = [];
    var normalizedInputDir = normalizePath(INPUT_DIR);

    Console.writeln("🔍 Looking for files in: " + normalizedInputDir);

    if (File.directoryExists(normalizedInputDir)) {
        // Since File.find() doesn't exist, let's try the known files directly
        var knownFiles = [
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-Red_mono.xisf",
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-Blue_mono.xisf",
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-Green_mono.xisf",
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-Luminance_mono.xisf"
        ];

        for (var i = 0; i < knownFiles.length; i++) {
            var fullPath = normalizedInputDir + "/" + knownFiles[i];
            if (File.exists(fullPath)) {
                Console.writeln("✅ Found: " + knownFiles[i]);
                inputFiles.push(fullPath);
            } else {
                Console.writeln("⚠️ Not found: " + knownFiles[i]);
            }
        }

        // Also try some common patterns manually
        var commonPatterns = [
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-SII_mono.xisf",
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-Ha_mono.xisf",
            "masterLight_BIN-1_3056x3056_EXPOSURE-300.00s_FILTER-OIII_mono.xisf"
        ];

        for (var i = 0; i < commonPatterns.length; i++) {
            var fullPath = normalizedInputDir + "/" + commonPatterns[i];
            if (File.exists(fullPath)) {
                Console.writeln("✅ Found additional: " + commonPatterns[i]);
                inputFiles.push(fullPath);
            }
        }
    } else {
        Console.writeln("❌ Input directory does not exist: " + normalizedInputDir);
    }
    
    if (inputFiles.length === 0) {
        Console.criticalln("❌ No image files found in: " + INPUT_DIR);
        return;
    }
    
    Console.writeln("📄 Found " + inputFiles.length + " image file(s)");
    
    // Process each file
    var successCount = 0;
    for (var i = 0; i < inputFiles.length; i++) {
        if (processOneFile(inputFiles[i], normalizedOutputDir)) {
            successCount++;
        }
    }

    Console.writeln("\n🎉 Processing completed!");
    Console.writeln("✅ Successfully processed: " + successCount + "/" + inputFiles.length);
    Console.writeln("📁 Check output: " + normalizedOutputDir);
}

// Start processing
main();

})();
