#feature-id Scripts > ImageProcessing > AutoIntegrateMimic

#feature-info A script that mimics AutoIntegrate for automating basic astro image processing in PixInsight.

#include <pjsr/Sizer.jsh>
#include <pjsr/FrameStyle.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/UndoFlag.jsh>

function AutoMimicDialog() {
  this.__base__ = Dialog;
  this.__base__();
  this.restyle();

  // File list
  this.fileList = new TreeBox(this);
  this.fileList.numberOfColumns = 1;
  this.fileList.headerVisible = false;

  // Add files button
  this.addFileButton = new PushButton(this);
  this.addFileButton.text = "Add Files";
  this.addFileButton.parentDialog = this;
  this.addFileButton.onClick = function() {
    var ofd = new OpenFileDialog;
    ofd.multipleSelections = true;
    ofd.filters = [["Image files", "*.fits", "*.fit", "*.fts", "*.xisf"], ["All files", "*"]];
    if (ofd.execute()) {
      var existing = [];
      for (var k = 0; k < this.parentDialog.fileList.numberOfChildren; ++k) {
        existing.push(this.parentDialog.fileList.child(k).text(0));
      }
      for (var i = 0; i < ofd.fileNames.length; ++i) {
        if (existing.indexOf(ofd.fileNames[i]) === -1) {
          var node = new TreeBoxNode(this.parentDialog.fileList);
          node.setText(0, ofd.fileNames[i]);
        }
      }
      this.parentDialog.adjustToContents();
    }
  };

  // Remove selected button
  this.removeFileButton = new PushButton(this);
  this.removeFileButton.text = "Remove Selected";
  this.removeFileButton.parentDialog = this;
  this.removeFileButton.onClick = function() {
    var selected = this.parentDialog.fileList.selectedNodes;
    for (var i = selected.length - 1; i >= 0; --i) {
      this.parentDialog.fileList.remove(selected[i]);
    }
    this.parentDialog.adjustToContents();
  };

  // Run button
  this.runButton = new PushButton(this);
  this.runButton.text = "Run";
  this.runButton.parentDialog = this;
  this.runButton.onClick = function() {
    var files = [];
    for (var i = 0; i < this.parentDialog.fileList.numberOfChildren; ++i) {
      files.push(this.parentDialog.fileList.child(i).text(0));
    }
    if (files.length === 0) {
      (new MessageBox("No files selected.")).execute();
      return;
    }

    // Remove duplicates
    var uniqueFiles = [];
    for (var i = 0; i < files.length; ++i) {
      if (uniqueFiles.indexOf(files[i]) === -1) {
        uniqueFiles.push(files[i]);
      }
    }

    if (uniqueFiles.length === 0) {
      (new MessageBox("No unique files selected.")).execute();
      return;
    }

    console.show();
    console.writeln("Processing " + uniqueFiles.length + " unique files...");

    // Group by filter
    var filterGroups = {};
    for (var i = 0; i < uniqueFiles.length; ++i) {
      var file = uniqueFiles[i];
      var match = file.match(/-(Red|Green|Blue)-/i);
      var filter = match ? match[1].toUpperCase() : 'UNKNOWN';
      if (!filterGroups[filter]) filterGroups[filter] = [];
      filterGroups[filter].push(file);
    }

    var integratedViews = {};
    for (var filter in filterGroups) {
      var group = filterGroups[filter];
      if (group.length === 0) continue;
      console.writeln("Processing filter " + filter + " with " + group.length + " files...");

      var integratedView;
      if (group.length === 1) {
        console.writeln("Opening single file for filter " + filter + ": " + group[0]);
        var windows = ImageWindow.open(group[0]);
        if (windows.length === 0) {
          console.error("Failed to open file: " + group[0]);
          continue;
        }
        var w = windows[0];
        w.show();
        integratedView = w.mainView;
      } else {
        // Align
        var alignedFiles = [];
        var reference = group[0];
        console.writeln("Using reference for " + filter + ": " + reference);
        var sa = new StarAlignment;
        sa.referenceImage = reference;
        sa.referenceIsFile = true;
        sa.outputDir = File.systemTempDirectory;
        sa.outputExtension = ".xisf";
        sa.overwriteExistingFiles = true;
        sa.useSurfaceSplines = true;
        sa.pixelInterpolation = StarAlignment.prototype.Auto;
        sa.clampingThreshold = 0.30;
        sa.mode = StarAlignment.prototype.RegisterMatch;

        alignedFiles.push(reference);
        for (var j = 1; j < group.length; ++j) {
          var target = group[j];
          var dir = File.systemTempDirectory;
          if (!dir.endsWith('/') && !dir.endsWith('\\')) {
            dir += '/';
          }
          var name = File.extractName(target);
          var alignedFile = dir + name + '_r' + sa.outputExtension;
          sa.targetImages = [[true, target, "", ""]];
          console.writeln("Aligning for " + filter + ": " + target + " to " + alignedFile);
          if (sa.executeGlobal()) {
            alignedFiles.push(alignedFile);
            console.writeln("Aligned file for " + filter + ": " + alignedFile);
          } else {
            console.warning("Alignment failed for " + target + " in " + filter + ". Using original.");
            alignedFiles.push(target);
          }
        }

        if (alignedFiles.length === 0) {
          console.error("No files to integrate for " + filter + ".");
          continue;
        }

        // Debug: list aligned files
        console.writeln("Aligned files for " + filter + ":");
        for (var af = 0; af < alignedFiles.length; ++af) {
          console.writeln(alignedFiles[af]);
        }
        console.writeln("Number of files to integrate for " + filter + ": " + alignedFiles.length);

        // Integrate
        console.writeln("Integrating " + alignedFiles.length + " files for " + filter + "...");
        var ii = new ImageIntegration;
        ii.images = [];
        for (var k = 0; k < alignedFiles.length; ++k) {
          ii.images.push([true, alignedFiles[k], "", ""]);
        }
        ii.combination = ImageIntegration.prototype.Average;
        ii.weighting = ImageIntegration.prototype.NoiseEvaluation;
        ii.normalization = ImageIntegration.prototype.AdditiveWithScaling;
        if (alignedFiles.length >= 3) {
          ii.rejection = ImageIntegration.prototype.WinsorizedSigmaClipping;
          ii.rejectionNormalization = ImageIntegration.prototype.Scale;
        } else {
          ii.rejection = ImageIntegration.prototype.NoRejection;
          ii.rejectionNormalization = ImageIntegration.prototype.NoRejection;
        }
        ii.generateIntegratedImage = true;
        if (!ii.executeGlobal()) {
          console.error("Image integration failed for " + filter + ".");
          continue;
        }

        // Get the integrated view
        var integratedWindow = ImageWindow.windowById("integration");
        if (integratedWindow.isNull) {
          console.error("Integration failed to create window for " + filter + ".");
          continue;
        }
        integratedWindow.show();
        integratedView = integratedWindow.mainView;
        console.writeln("Integrated image created for " + filter + ".");
      }

      integratedViews[filter] = integratedView;
    }

    // Combine if multiple filters
    var keys = Object.keys(integratedViews);
    var finalView;
    if (keys.length === 1) {
      finalView = integratedViews[keys[0]];
    } else {
      // Assume RGB
      var cc = new ChannelCombination;
      cc.colorSpace = ChannelCombination.prototype.RGB;
      cc.channels = [
        [true, integratedViews['RED'] ? integratedViews['RED'].id : ""],
        [true, integratedViews['GREEN'] ? integratedViews['GREEN'].id : ""],
        [true, integratedViews['BLUE'] ? integratedViews['BLUE'].id : ""]
      ];
      if (!cc.executeGlobal()) {
        console.error("Channel combination failed.");
        this.parentDialog.ok();
        return;
      }
      var rgbWindow = ImageWindow.activeWindow;
      if (rgbWindow.isNull) {
        console.error("Channel combination failed to create window.");
        this.parentDialog.ok();
        return;
      }
      rgbWindow.show();
      finalView = rgbWindow.mainView;
      console.writeln("RGB image created.");
    }

    // Apply AutomaticBackgroundExtractor
    console.writeln("Applying background extraction...");
    var abe = new AutomaticBackgroundExtractor;
    abe.sampleSize = 10;
    abe.executeOn(finalView);

    // Color Calibration (if color image)
    if (finalView.image.colorSpace === ColorSpace_RGB) {
      console.writeln("Applying color calibration...");
      var cc = new ColorCalibration;
      cc.executeOn(finalView);
    }

    // Auto stretch using ScreenTransferFunction
    console.writeln("Applying auto stretch...");
    var stf = new ScreenTransferFunction;
    stf.autoStretch(finalView);

    console.writeln("Processing completed.");

    this.parentDialog.ok();
  };

  // Buttons sizer
  this.buttonsSizer = new HorizontalSizer;
  this.buttonsSizer.add(this.addFileButton);
  this.buttonsSizer.add(this.removeFileButton);
  this.buttonsSizer.addStretch();
  this.buttonsSizer.add(this.runButton);

  // Main sizer
  this.sizer = new VerticalSizer;
  this.sizer.margin = 6;
  this.sizer.spacing = 6;
  this.sizer.add(this.fileList, 100);
  this.sizer.add(this.buttonsSizer);

  this.windowTitle = "AutoIntegrate Mimic";
  this.userResizable = true;
  this.adjustToContents();
}

AutoMimicDialog.prototype = new Dialog;

function main() {
  var dialog = new AutoMimicDialog();
  dialog.execute();
}

main();
