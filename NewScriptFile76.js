#include <pjsr/Sizer.jsh>
#include <pjsr/FrameStyle.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/StdIcon.jsh>

/*
 * RGB Channel Combination Script for PixInsight
 * Automatically combines integrated R, G, B master frames using ChannelCombination
 * Reads filter information from FITS headers and file names
 */

// Configuration
var INPUT_DIRECTORY = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master";
var OUTPUT_DIRECTORY = "C:/Users/<USER>/OneDrive/Desktop/Input/Output/master"; // Can be changed if needed

function findMasterFrames() {
    var dir = new FileFind;
    var files = {
        red: "",
        green: "",
        blue: ""
    };

    if (!dir.begin(INPUT_DIRECTORY + "/*.xisf")) {
        throw new Error("Cannot access input directory: " + INPUT_DIRECTORY);
    }

    do {
        var fileName = dir.name;
        var fullPath = INPUT_DIRECTORY + "/" + fileName;

        // Check if it's a master light frame
        if (fileName.indexOf("masterLight") >= 0 && fileName.indexOf(".xisf") >= 0) {
            // Try to determine filter from filename first
            var filterFromName = "";
            if (fileName.indexOf("FILTER-Red") >= 0 || fileName.indexOf("Red") >= 0) {
                filterFromName = "Red";
            } else if (fileName.indexOf("FILTER-Green") >= 0 || fileName.indexOf("Green") >= 0) {
                filterFromName = "Green";
            } else if (fileName.indexOf("FILTER-Blue") >= 0 || fileName.indexOf("Blue") >= 0) {
                filterFromName = "Blue";
            }

            // If we found a filter in the filename, assign it
            if (filterFromName == "Red") {
                files.red = fullPath;
                console.writeln("Found Red frame: " + fileName);
            } else if (filterFromName == "Green") {
                files.green = fullPath;
                console.writeln("Found Green frame: " + fileName);
            } else if (filterFromName == "Blue") {
                files.blue = fullPath;
                console.writeln("Found Blue frame: " + fileName);
            } else {
                // If no filter in filename, try reading FITS header
                try {
                    var window = ImageWindow.open(fullPath)[0];
                    if (window.isNull) continue;

                    var keywords = window.keywords;
                    var filterValue = "";

                    for (var i = 0; i < keywords.length; i++) {
                        if (keywords[i].name == "FILTER") {
                            filterValue = keywords[i].value.replace(/'/g, "").trim();
                            break;
                        }
                    }

                    window.close();

                    if (filterValue.toLowerCase().indexOf("red") >= 0) {
                        files.red = fullPath;
                        console.writeln("Found Red frame (from header): " + fileName);
                    } else if (filterValue.toLowerCase().indexOf("green") >= 0) {
                        files.green = fullPath;
                        console.writeln("Found Green frame (from header): " + fileName);
                    } else if (filterValue.toLowerCase().indexOf("blue") >= 0) {
                        files.blue = fullPath;
                        console.writeln("Found Blue frame (from header): " + fileName);
                    }
                } catch (e) {
                    console.warningln("Could not read FITS header for: " + fileName);
                }
            }
        }
    } while (dir.next());

    return files;
}

function combineRGBChannels(redPath, greenPath, bluePath) {
    console.writeln("\n=== Starting RGB Channel Combination ===");

    // Open the source images
    console.writeln("Opening source images...");
    var redWindows = ImageWindow.open(redPath);
    var greenWindows = ImageWindow.open(greenPath);
    var blueWindows = ImageWindow.open(bluePath);

    if (redWindows.length == 0 || greenWindows.length == 0 || blueWindows.length == 0) {
        throw new Error("Failed to open one or more source images");
    }

    var redWindow = redWindows[0];
    var greenWindow = greenWindows[0];
    var blueWindow = blueWindows[0];

    console.writeln("Red: " + redWindow.mainView.id);
    console.writeln("Green: " + greenWindow.mainView.id);
    console.writeln("Blue: " + blueWindow.mainView.id);

    // Create ChannelCombination instance
    var CC = new ChannelCombination;

    // Configure ChannelCombination for RGB
    CC.colorSpace = ChannelCombination.prototype.RGB;
    CC.channels = [
        [true, redWindow.mainView.id],      // Red channel
        [true, greenWindow.mainView.id],    // Green channel
        [true, blueWindow.mainView.id]      // Blue channel
    ];

    // Execute ChannelCombination in global context to create new image
    console.writeln("Executing ChannelCombination...");
    CC.executeGlobal();

    // Find the newly created RGB image
    var rgbWindow = null;
    var windows = ImageWindow.windows;
    for (var i = 0; i < windows.length; i++) {
        if (windows[i].mainView.image.isColor &&
            windows[i].mainView.id.indexOf("RGB_Combination") >= 0) {
            rgbWindow = windows[i];
            break;
        }
    }

    if (rgbWindow == null) {
        // If not found by name, get the last color window
        for (var i = windows.length - 1; i >= 0; i--) {
            if (windows[i].mainView.image.isColor) {
                rgbWindow = windows[i];
                break;
            }
        }
    }

    if (rgbWindow != null) {
        // Rename the RGB image
        var timestamp = new Date().toISOString().replace(/[:.]/g, "-").slice(0, 19);
        var newId = "RGB_Combined_" + timestamp;
        rgbWindow.mainView.id = newId;

        // Copy some FITS keywords from one of the source images
        try {
            var sourceKeywords = redWindow.keywords;
            var newKeywords = [];

            // Copy relevant keywords
            for (var i = 0; i < sourceKeywords.length; i++) {
                var keyword = sourceKeywords[i];
                if (keyword.name != "FILTER" && keyword.name != "IMAGETYP") {
                    newKeywords.push(keyword);
                }
            }

            // Add new keywords
            newKeywords.push(new FITSKeyword("IMAGETYP", "'RGB Combined'", "Type of image"));
            newKeywords.push(new FITSKeyword("FILTER", "'RGB'", "Combined RGB filters"));
            newKeywords.push(new FITSKeyword("COMMENT", "", "RGB combination created by PixInsight script"));
            newKeywords.push(new FITSKeyword("RGBRED", "'" + File.extractName(redPath) + "'", "Red channel source"));
            newKeywords.push(new FITSKeyword("RGBGREEN", "'" + File.extractName(greenPath) + "'", "Green channel source"));
            newKeywords.push(new FITSKeyword("RGBBLUE", "'" + File.extractName(bluePath) + "'", "Blue channel source"));

            rgbWindow.keywords = newKeywords;
        } catch (e) {
            console.warningln("Could not copy FITS keywords: " + e.message);
        }

        // Save the result
        var outputPath = OUTPUT_DIRECTORY + "/" + newId + ".xisf";
        console.writeln("Saving RGB image to: " + outputPath);
        rgbWindow.saveAs(outputPath, false, false, false, false);

        console.writeln("RGB combination completed successfully!");
        console.writeln("Output: " + outputPath);
    } else {
        throw new Error("Could not find the RGB combined image");
    }

    // Close source windows
    redWindow.close();
    greenWindow.close();
    blueWindow.close();

    return rgbWindow;
}

function main() {
    console.writeln("=== RGB Channel Combination Script ===");
    console.writeln("Input directory: " + INPUT_DIRECTORY);
    console.writeln("Output directory: " + OUTPUT_DIRECTORY);

    try {
        // Find master frames
        console.writeln("\nSearching for master frames...");
        var files = findMasterFrames();

        // Check if all files were found
        if (files.red == "" || files.green == "" || files.blue == "") {
            var missing = [];
            if (files.red == "") missing.push("Red");
            if (files.green == "") missing.push("Green");
            if (files.blue == "") missing.push("Blue");

            throw new Error("Missing master frames for: " + missing.join(", ") +
                          "\nFound:\nRed: " + files.red +
                          "\nGreen: " + files.green +
                          "\nBlue: " + files.blue);
        }

        console.writeln("\nAll RGB master frames found:");
        console.writeln("Red: " + File.extractName(files.red));
        console.writeln("Green: " + File.extractName(files.green));
        console.writeln("Blue: " + File.extractName(files.blue));

        // Perform RGB combination
        var rgbWindow = combineRGBChannels(files.red, files.green, files.blue);

        console.writeln("\n=== Script completed successfully! ===");

        // Show completion message
        var msgBox = new MessageBox(
            "RGB Channel Combination completed successfully!\n\n" +
            "Combined RGB image: " + rgbWindow.mainView.id + "\n\n" +
            "The RGB image has been saved to:\n" + OUTPUT_DIRECTORY,
            "RGB Combination Complete",
            StdIcon_Information,
            StdButton_Ok
        );
        msgBox.execute();

    } catch (error) {
        console.criticalln("Error: " + error.message);

        var errorBox = new MessageBox(
            "RGB Channel Combination failed:\n\n" + error.message,
            "Error",
            StdIcon_Error,
            StdButton_Ok
        );
        errorBox.execute();
    }
}

// Execute the script
main();
