/*
 * Post-Integration Pipeline — Simplified Version
 * Focuses on core functionality without complex platform detection
 */

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

(function(){

// Simple platform detection
var isWindows = (CoreApplication.platform == "MSWindows");
if (!isWindows && File.directoryExists("C:/Program Files")) {
    isWindows = true;
}

var defaults = {
    inputDir: isWindows ? "C:/AstroImages/Processed" : File.homeDirectory + "/AstroImages/Processed",
    outputDir: isWindows ? "C:/AstroImages/Output" : File.homeDirectory + "/AstroImages/Output"
};

// Simple astrometry function
function hasAstrometricSolution(win) {
    try {
        var keywords = win.mainView.window.keywords;
        var hasRA = false, hasDEC = false;

        for (var i = 0; i < keywords.length; i++) {
            var name = keywords[i].name.toUpperCase();
            if (name === "RA" || name === "OBJCTRA" || name === "CRVAL1") hasRA = true;
            if (name === "DEC" || name === "OBJCTDEC" || name === "CRVAL2") hasDEC = true;
        }

        return hasRA && hasDEC;
    } catch (e) {
        return false;
    }
}

// Simple SPCC with astrometry check
function performSPCC(win) {
    Console.writeln("=== SPCC (Spectrophotometric Color Calibration) ===");

    if (!hasAstrometricSolution(win)) {
        Console.writeln("❌ No astrometric solution found");

        var result = (new MessageBox(
            "No Astrometric Solution Found\n\n" +
            "SPCC requires an astrometric solution to work.\n\n" +
            "Options:\n" +
            "1. Solve the image first using ImageSolver or astrometry.net\n" +
            "2. Skip SPCC for now\n\n" +
            "Skip SPCC?",
            "Astrometric Solution Required",
            StdIcon_Question,
            StdButton_Yes, StdButton_No
        )).execute();

        if (result == StdButton_Yes) {
            Console.writeln("⏭️ SPCC skipped by user");
            return false;
        } else {
            Console.writeln("❌ SPCC cannot proceed without astrometric solution");
            return false;
        }
    }

    try {
        var P = new SpectrophotometricColorCalibration();
        P.whiteReferenceId = "Average Spiral Galaxy";
        P.redFilter = "Sony Color Sensor R";
        P.greenFilter = "Sony Color Sensor G";
        P.blueFilter = "Sony Color Sensor B";
        P.applyCalibration = true;

        P.executeOn(win.mainView);

        // Verify it actually worked
        if (hasAstrometricSolution(win)) {
            Console.writeln("✅ SPCC completed successfully");
            return true;
        } else {
            Console.writeln("❌ SPCC failed - astrometric solution lost");
            return false;
        }

    } catch (e) {
        Console.writeln("❌ SPCC failed: " + e.message);
        return false;
    }
}

// Simple GUI
function showGUI() {
    var state = {
        inputDir: defaults.inputDir,
        outputDir: defaults.outputDir
    };

    var dlg = new Dialog;
    dlg.windowTitle = "Post-Integration Pipeline (Simplified)";
    dlg.sizer = new VerticalSizer;
    dlg.sizer.margin = 10;
    dlg.sizer.spacing = 8;

    // Title
    var title = new Label(dlg);
    title.useRichText = true;
    title.text = "<b>Post-Integration Pipeline - Simplified Version</b>";
    title.textAlignment = TextAlign_Center;
    dlg.sizer.add(title);

    // Input directory
    var inputGroup = new GroupBox(dlg);
    inputGroup.title = "Input Directory";
    inputGroup.sizer = new HorizontalSizer;
    inputGroup.sizer.margin = 8;
    inputGroup.sizer.spacing = 6;

    var inputEdit = new Edit(dlg);
    inputEdit.text = state.inputDir;
    inputEdit.minWidth = 400;

    var inputButton = new PushButton(dlg);
    inputButton.text = "Browse...";
    inputButton.onClick = function() {
        var d = new GetDirectoryDialog;
        d.caption = "Select Input Directory";
        d.initialDirectory = state.inputDir;
        if (d.execute()) {
            state.inputDir = d.directory;
            inputEdit.text = state.inputDir;
        }
    };

    inputGroup.sizer.add(inputEdit, 100);
    inputGroup.sizer.add(inputButton);
    dlg.sizer.add(inputGroup);

    // Output directory
    var outputGroup = new GroupBox(dlg);
    outputGroup.title = "Output Directory";
    outputGroup.sizer = new HorizontalSizer;
    outputGroup.sizer.margin = 8;
    outputGroup.sizer.spacing = 6;

    var outputEdit = new Edit(dlg);
    outputEdit.text = state.outputDir;
    outputEdit.minWidth = 400;

    var outputButton = new PushButton(dlg);
    outputButton.text = "Browse...";
    outputButton.onClick = function() {
        var d = new GetDirectoryDialog;
        d.caption = "Select Output Directory";
        d.initialDirectory = state.outputDir;
        if (d.execute()) {
            state.outputDir = d.directory;
            outputEdit.text = state.outputDir;
        }
    };

    outputGroup.sizer.add(outputEdit, 100);
    outputGroup.sizer.add(outputButton);
    dlg.sizer.add(outputGroup);

    // Buttons
    var buttonRow = new HorizontalSizer;
    buttonRow.spacing = 8;
    buttonRow.addStretch();

    var startButton = new PushButton(dlg);
    startButton.text = "Start Processing";
    startButton.defaultButton = true;
    startButton.onClick = function() {
        if (!File.directoryExists(state.inputDir)) {
            (new MessageBox("Input directory does not exist", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        if (!File.directoryExists(state.outputDir)) {
            (new MessageBox("Output directory does not exist", "Error", StdIcon_Error, StdButton_Ok)).execute();
            return;
        }
        dlg.ok();
    };

    var cancelButton = new PushButton(dlg);
    cancelButton.text = "Cancel";
    cancelButton.onClick = function() {
        dlg.cancel();
    };

    buttonRow.add(startButton);
    buttonRow.add(cancelButton);
    dlg.sizer.add(buttonRow);

    if (dlg.execute()) {
        return state;
    } else {
        return null;
    }
}

// Main function
function main() {
    Console.show();
    Console.writeln("=== Post-Integration Pipeline (Simplified) ===");
    Console.writeln("Platform: " + (isWindows ? "Windows" : "Unix-like"));

    var config = showGUI();
    if (!config) {
        Console.writeln("Cancelled by user");
        return;
    }

    Console.writeln("Input: " + config.inputDir);
    Console.writeln("Output: " + config.outputDir);

    // Test SPCC on current image
    var currentWindow = ImageWindow.activeWindow;
    if (currentWindow) {
        Console.writeln("Testing SPCC on current image: " + currentWindow.mainView.id);
        performSPCC(currentWindow);
    } else {
        Console.writeln("No active image window found");
        Console.writeln("Please open an image and run the script again");
    }
}

// Run
main();

})();
