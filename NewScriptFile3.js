/*
 * WBPP Launcher Script
 * Simple launcher that opens WBPP and provides configuration guidance
 */

#feature-id    WBPPLauncher > Batch > WBPPLauncher

#include <pjsr/Sizer.jsh>
#include <pjsr/StdIcon.jsh>
#include <pjsr/StdButton.jsh>
#include <pjsr/TextAlign.jsh>
#include <pjsr/DataType.jsh>

// Global settings object
var WBPPLauncherSettings = {
    // Camera settings
    cameraType: "color",
    bayerPattern: 0,
    pedestal: 300,

    // Calibration frames
    useMasterBias: false,
    masterBiasPath: "",
    useMasterDark: false,
    masterDarkPath: "",
    useMasterFlat: false,
    masterFlatPath: "",

    // Light frames
    lightFrames: [],

    // Processing options
    applyCalibration: true,
    cosmeticCorrection: true,
    debayer: true,
    registration: true,
    localNormalization: false,

    // Output
    outputDirectory: "",

    // Load settings from PixInsight's settings (fixed version)
    load: function() {
        try {
            var keyBase = "/WBPPLauncher/";

            // Check if Settings object has the read method before trying to use it
            if (typeof Settings !== 'undefined' && typeof Settings.read === 'function') {
                try {
                    var tempValue = Settings.read(keyBase + "cameraType", DataType_String);
                    if (tempValue !== null && tempValue !== undefined) this.cameraType = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "bayerPattern", DataType_Int32);
                    if (tempValue !== null && tempValue !== undefined) this.bayerPattern = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "pedestal", DataType_Int32);
                    if (tempValue !== null && tempValue !== undefined) this.pedestal = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "useMasterBias", DataType_Boolean);
                    if (tempValue !== null && tempValue !== undefined) this.useMasterBias = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "masterBiasPath", DataType_String);
                    if (tempValue !== null && tempValue !== undefined) this.masterBiasPath = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "useMasterDark", DataType_Boolean);
                    if (tempValue !== null && tempValue !== undefined) this.useMasterDark = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "masterDarkPath", DataType_String);
                    if (tempValue !== null && tempValue !== undefined) this.masterDarkPath = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "useMasterFlat", DataType_Boolean);
                    if (tempValue !== null && tempValue !== undefined) this.useMasterFlat = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "masterFlatPath", DataType_String);
                    if (tempValue !== null && tempValue !== undefined) this.masterFlatPath = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }

                try {
                    var tempValue = Settings.read(keyBase + "outputDirectory", DataType_String);
                    if (tempValue !== null && tempValue !== undefined) this.outputDirectory = tempValue;
                } catch (e) { /* ignore if key doesn't exist */ }
            }
        } catch (e) {
            Console.warningln("Could not load settings: " + e.message);
        }
    },

    // Save settings to PixInsight's settings
    save: function() {
        try {
            var keyBase = "/WBPPLauncher/";

            if (typeof Settings !== 'undefined' && typeof Settings.write === 'function') {
                Settings.write(keyBase + "cameraType", DataType_String, this.cameraType);
                Settings.write(keyBase + "bayerPattern", DataType_Int32, this.bayerPattern);
                Settings.write(keyBase + "pedestal", DataType_Int32, this.pedestal);

                Settings.write(keyBase + "useMasterBias", DataType_Boolean, this.useMasterBias);
                Settings.write(keyBase + "masterBiasPath", DataType_String, this.masterBiasPath);

                Settings.write(keyBase + "useMasterDark", DataType_Boolean, this.useMasterDark);
                Settings.write(keyBase + "masterDarkPath", DataType_String, this.masterDarkPath);

                Settings.write(keyBase + "useMasterFlat", DataType_Boolean, this.useMasterFlat);
                Settings.write(keyBase + "masterFlatPath", DataType_String, this.masterFlatPath);

                Settings.write(keyBase + "outputDirectory", DataType_String, this.outputDirectory);
            }
        } catch (e) {
            Console.warningln("Could not save settings: " + e.message);
        }
    }
};

function WBPPLauncherDialog() {
    this.__base__ = Dialog;
    this.__base__();

    // Load saved settings
    WBPPLauncherSettings.load();

    this.windowTitle = "WBPP Launcher";
    this.createGUI();
}

WBPPLauncherDialog.prototype = new Dialog;

WBPPLauncherDialog.prototype.createGUI = function() {
    var self = this;

    this.sizer = new VerticalSizer;
    this.sizer.margin = 10;
    this.sizer.spacing = 8;

    // Title
    var titleLabel = new Label(this);
    titleLabel.text = "<b>WBPP Launcher</b><br/>Configure and launch WeightedBatchPreprocessing";
    titleLabel.useRichText = true;
    titleLabel.textAlignment = TextAlign_Center;
    this.sizer.add(titleLabel);

    // Camera Configuration
    this.createCameraSection();

    // Master Calibration Files
    this.createCalibrationSection();

    // Light Frames
    this.createLightSection();

    // Processing Options
    this.createProcessingSection();

    // Output Directory
    this.createOutputSection();

    // Control Buttons
    this.createControlButtons();

    this.adjustToContents();
    this.setFixedWidth(700);
};

WBPPLauncherDialog.prototype.createCameraSection = function() {
    var self = this;

    var group = new GroupBox(this);
    group.title = "Camera Configuration";
    group.sizer = new VerticalSizer;
    group.sizer.margin = 8;
    group.sizer.spacing = 6;

    // Camera type row
    var typeRow = new HorizontalSizer;
    typeRow.spacing = 12;

    this.colorRadio = new RadioButton(this);
    this.colorRadio.text = "Color/OSC Camera";
    this.colorRadio.checked = (WBPPLauncherSettings.cameraType === "color");
    this.colorRadio.onCheck = function(checked) {
        if (checked) {
            WBPPLauncherSettings.cameraType = "color";
            WBPPLauncherSettings.debayer = true;
            self.bayerCombo.enabled = true;
        }
    };

    this.monoRadio = new RadioButton(this);
    this.monoRadio.text = "Monochrome Camera";
    this.monoRadio.checked = (WBPPLauncherSettings.cameraType === "mono");
    this.monoRadio.onCheck = function(checked) {
        if (checked) {
            WBPPLauncherSettings.cameraType = "mono";
            WBPPLauncherSettings.debayer = false;
            self.bayerCombo.enabled = false;
        }
    };

    typeRow.add(this.colorRadio);
    typeRow.add(this.monoRadio);
    typeRow.addStretch();

    // Bayer pattern and pedestal row
    var settingsRow = new HorizontalSizer;
    settingsRow.spacing = 8;

    var bayerLabel = new Label(this);
    bayerLabel.text = "Bayer Pattern:";
    bayerLabel.textAlignment = TextAlign_Right | TextAlign_VertCenter;

    this.bayerCombo = new ComboBox(this);
    this.bayerCombo.addItem("RGGB");
    this.bayerCombo.addItem("BGGR");
    this.bayerCombo.addItem("GRBG");
    this.bayerCombo.addItem("GBRG");
    this.bayerCombo.currentItem = WBPPLauncherSettings.bayerPattern;
    this.bayerCombo.enabled = (WBPPLauncherSettings.cameraType === "color");
    this.bayerCombo.onItemSelected = function(index) {
        WBPPLauncherSettings.bayerPattern = index;
    };

    settingsRow.add(bayerLabel);
    settingsRow.add(this.bayerCombo);
    settingsRow.addSpacing(20);

    var pedestalLabel = new Label(this);
    pedestalLabel.text = "Pedestal:";

    this.pedestalSpinBox = new SpinBox(this);
    this.pedestalSpinBox.minValue = 0;
    this.pedestalSpinBox.maxValue = 1000;
    this.pedestalSpinBox.value = WBPPLauncherSettings.pedestal;
    this.pedestalSpinBox.onValueUpdated = function(value) {
        WBPPLauncherSettings.pedestal = value;
    };

    settingsRow.add(pedestalLabel);
    settingsRow.add(this.pedestalSpinBox);
    settingsRow.addStretch();

    group.sizer.add(typeRow);
    group.sizer.add(settingsRow);
    this.sizer.add(group);
};

WBPPLauncherDialog.prototype.createCalibrationSection = function() {
    var self = this;

    var group = new GroupBox(this);
    group.title = "Master Calibration Frames";
    group.sizer = new VerticalSizer;
    group.sizer.margin = 8;
    group.sizer.spacing = 6;

    // Helper function to create calibration row
    function createCalibrationRow(parent, type, label) {
        var row = new HorizontalSizer;
        row.spacing = 6;

        var checkBox = new CheckBox(parent);
        checkBox.text = label;
        checkBox.minWidth = 100;
        checkBox.checked = WBPPLauncherSettings["useMaster" + type];

        var edit = new Edit(parent);
        edit.text = WBPPLauncherSettings["master" + type + "Path"];
        edit.readOnly = true;
        edit.enabled = checkBox.checked;

        var browseBtn = new PushButton(parent);
        browseBtn.text = "Browse...";
        browseBtn.icon = parent.scaledResource(":/icons/select-file.png");
        browseBtn.enabled = checkBox.checked;

        checkBox.onCheck = function(checked) {
            WBPPLauncherSettings["useMaster" + type] = checked;
            edit.enabled = checked;
            browseBtn.enabled = checked;
        };

        browseBtn.onClick = function() {
            var path = self.selectMasterFile(label, type.toLowerCase());
            if (path) {
                edit.text = path;
                WBPPLauncherSettings["master" + type + "Path"] = path;
            }
        };

        row.add(checkBox);
        row.add(edit, 100);
        row.add(browseBtn);

        parent["master" + type + "CheckBox"] = checkBox;
        parent["master" + type + "Edit"] = edit;
        parent["master" + type + "BrowseBtn"] = browseBtn;

        return row;
    }

    group.sizer.add(createCalibrationRow(this, "Bias", "Master Bias:"));
    group.sizer.add(createCalibrationRow(this, "Dark", "Master Dark:"));
    group.sizer.add(createCalibrationRow(this, "Flat", "Master Flat:"));

    this.sizer.add(group);
};

WBPPLauncherDialog.prototype.createLightSection = function() {
    var self = this;

    var group = new GroupBox(this);
    group.title = "Light Frames";
    group.sizer = new VerticalSizer;
    group.sizer.margin = 8;
    group.sizer.spacing = 6;

    var row = new HorizontalSizer;
    row.spacing = 6;

    this.lightCountLabel = new Label(this);
    this.updateLightCountLabel();

    var selectBtn = new PushButton(this);
    selectBtn.text = "Select Light Frames...";
    selectBtn.icon = this.scaledResource(":/icons/add.png");
    selectBtn.onClick = function() {
        self.selectLightFrames();
    };

    var clearBtn = new PushButton(this);
    clearBtn.text = "Clear";
    clearBtn.onClick = function() {
        WBPPLauncherSettings.lightFrames = [];
        self.updateLightCountLabel();
    };

    row.add(this.lightCountLabel, 100);
    row.add(selectBtn);
    row.add(clearBtn);

    group.sizer.add(row);
    this.sizer.add(group);
};

WBPPLauncherDialog.prototype.createProcessingSection = function() {
    var self = this;

    var group = new GroupBox(this);
    group.title = "Processing Options";
    group.sizer = new VerticalSizer;
    group.sizer.margin = 8;
    group.sizer.spacing = 6;

    var row1 = new HorizontalSizer;
    row1.spacing = 12;

    this.calibrationCheck = new CheckBox(this);
    this.calibrationCheck.text = "Apply Calibration";
    this.calibrationCheck.checked = WBPPLauncherSettings.applyCalibration;
    this.calibrationCheck.onCheck = function(checked) {
        WBPPLauncherSettings.applyCalibration = checked;
    };

    this.cosmeticCheck = new CheckBox(this);
    this.cosmeticCheck.text = "Cosmetic Correction";
    this.cosmeticCheck.checked = WBPPLauncherSettings.cosmeticCorrection;
    this.cosmeticCheck.onCheck = function(checked) {
        WBPPLauncherSettings.cosmeticCorrection = checked;
    };

    this.registrationCheck = new CheckBox(this);
    this.registrationCheck.text = "Star Alignment";
    this.registrationCheck.checked = WBPPLauncherSettings.registration;
    this.registrationCheck.onCheck = function(checked) {
        WBPPLauncherSettings.registration = checked;
    };

    row1.add(this.calibrationCheck);
    row1.add(this.cosmeticCheck);
    row1.add(this.registrationCheck);
    row1.addStretch();

    var row2 = new HorizontalSizer;
    row2.spacing = 12;

    this.localNormCheck = new CheckBox(this);
    this.localNormCheck.text = "Local Normalization";
    this.localNormCheck.checked = WBPPLauncherSettings.localNormalization;
    this.localNormCheck.onCheck = function(checked) {
        WBPPLauncherSettings.localNormalization = checked;
    };

    row2.add(this.localNormCheck);
    row2.addStretch();

    group.sizer.add(row1);
    group.sizer.add(row2);
    this.sizer.add(group);
};

WBPPLauncherDialog.prototype.createOutputSection = function() {
    var self = this;

    var group = new GroupBox(this);
    group.title = "Output Settings";
    group.sizer = new HorizontalSizer;
    group.sizer.margin = 8;
    group.sizer.spacing = 6;

    var label = new Label(this);
    label.text = "Output Directory:";
    label.textAlignment = TextAlign_Right | TextAlign_VertCenter;

    this.outputEdit = new Edit(this);
    this.outputEdit.text = WBPPLauncherSettings.outputDirectory;
    this.outputEdit.onEditCompleted = function() {
        WBPPLauncherSettings.outputDirectory = this.text;
    };

    var browseBtn = new PushButton(this);
    browseBtn.text = "Browse...";
    browseBtn.icon = this.scaledResource(":/icons/select-file.png");
    browseBtn.onClick = function() {
        var dlg = new GetDirectoryDialog;
        dlg.caption = "Select Output Directory";
        dlg.initialPath = WBPPLauncherSettings.outputDirectory;
        if (dlg.execute()) {
            self.outputEdit.text = dlg.directory;
            WBPPLauncherSettings.outputDirectory = dlg.directory;
        }
    };

    group.sizer.add(label);
    group.sizer.add(this.outputEdit, 100);
    group.sizer.add(browseBtn);

    this.sizer.add(group);
};

WBPPLauncherDialog.prototype.createControlButtons = function() {
    var self = this;

    var row = new HorizontalSizer;
    row.spacing = 8;

    var launchBtn = new PushButton(this);
    launchBtn.text = "Launch WBPP";
    launchBtn.icon = this.scaledResource(":/icons/ok.png");
    launchBtn.onClick = function() {
        self.launchWBPP();
    };

    var cancelBtn = new PushButton(this);
    cancelBtn.text = "Cancel";
    cancelBtn.icon = this.scaledResource(":/icons/cancel.png");
    cancelBtn.onClick = function() {
        self.cancel();
    };

    row.addStretch();
    row.add(launchBtn);
    row.add(cancelBtn);

    this.sizer.add(row);
};

WBPPLauncherDialog.prototype.selectMasterFile = function(title, type) {
    var dlg = new OpenFileDialog;
    dlg.caption = title;
    dlg.filters = [
        ["XISF Files", "*.xisf"],
        ["FITS Files", "*.fit", "*.fits", "*.fts"],
        ["All Files", "*"]
    ];

    if (dlg.execute()) {
        return dlg.fileName;
    }
    return null;
};

WBPPLauncherDialog.prototype.selectLightFrames = function() {
    var dlg = new OpenFileDialog;
    dlg.caption = "Select Light Frames";
    dlg.multipleSelections = true;
    dlg.filters = [
        ["XISF Files", "*.xisf"],
        ["FITS Files", "*.fit", "*.fits", "*.fts"],
        ["All Files", "*"]
    ];

    if (dlg.execute()) {
        WBPPLauncherSettings.lightFrames = dlg.fileNames;
        this.updateLightCountLabel();
    }
};

WBPPLauncherDialog.prototype.updateLightCountLabel = function() {
    if (WBPPLauncherSettings.lightFrames.length === 0) {
        this.lightCountLabel.text = "No light frames selected";
    } else {
        this.lightCountLabel.text = WBPPLauncherSettings.lightFrames.length + " light frames selected";
    }
};

WBPPLauncherDialog.prototype.launchWBPP = function() {
    // Validate inputs
    if (WBPPLauncherSettings.lightFrames.length === 0) {
        new MessageBox("Please select light frames first", "Error", StdIcon_Error, StdButton_Ok).execute();
        return;
    }

    // Save settings
    WBPPLauncherSettings.save();

    Console.show();
    Console.writeln("=== WBPP Configuration Summary ===");
    Console.writeln("Light frames: " + WBPPLauncherSettings.lightFrames.length);

    if (WBPPLauncherSettings.useMasterBias)
        Console.writeln("Master Bias: " + File.extractName(WBPPLauncherSettings.masterBiasPath));
    if (WBPPLauncherSettings.useMasterDark)
        Console.writeln("Master Dark: " + File.extractName(WBPPLauncherSettings.masterDarkPath));
    if (WBPPLauncherSettings.useMasterFlat)
        Console.writeln("Master Flat: " + File.extractName(WBPPLauncherSettings.masterFlatPath));

    Console.writeln("Camera Type: " + WBPPLauncherSettings.cameraType);
    if (WBPPLauncherSettings.cameraType === "color") {
        var patterns = ["RGGB", "BGGR", "GRBG", "GBRG"];
        Console.writeln("Bayer Pattern: " + patterns[WBPPLauncherSettings.bayerPattern]);
    }
    Console.writeln("Pedestal: " + WBPPLauncherSettings.pedestal);

    if (WBPPLauncherSettings.outputDirectory.length > 0) {
        Console.writeln("Output Directory: " + WBPPLauncherSettings.outputDirectory);
    }

    Console.writeln("\n" + "=".repeat(60));
    Console.writeln("LAUNCHING WBPP - Configure using the values above");
    Console.writeln("=".repeat(60));

    // Launch WBPP through the standard menu system
    try {
        // Use the core API to execute the WBPP script
        CoreApplication.executeScript("/Applications/PixInsight/src/scripts/BatchPreprocessing/WBPP.js");
        Console.writeln("✅ WBPP launched successfully!");

        // Provide configuration instructions
        Console.writeln("\nCONFIGURATION INSTRUCTIONS:");
        Console.writeln("1. In the WBPP window, add your " + WBPPLauncherSettings.lightFrames.length + " light frames");
        Console.writeln("2. Configure the settings as shown above");
        Console.writeln("3. Click 'Apply Global' to start processing");

    } catch (e) {
        Console.warningln("Could not auto-launch WBPP: " + e.message);
        Console.writeln("Please manually open: Script > Batch Processing > WeightedBatchPreprocessing");
        Console.writeln("Then configure using the values shown above.");
    }

    this.ok();
};

// Main execution
function main() {
    Console.hide();
    var dialog = new WBPPLauncherDialog();
    dialog.execute();
}

main();
